#!/bin/sh

#
# Common options need to change: J<PERSON><PERSON>_XMX, JVM_JMX_HOST, JVM_JMX_PORT
#
#
#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~#
# common attributes
CONF_FILES=${CONF_FILES:-config.ini}

#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~#
# app arguments: empty means disable or not-available

APP_ARGS=${APP_ARGS:-}

#~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~#
# jvm arguments: empty means disable or not-available

#auto the heap max size ($MAX_HEAP_SIZE) or leave it's empty  or custom the heap max size
JVM_XMX=${JVM_XMX:-1024M}
#auto the heap min size ($JVM_XMX) or leave it's empty  or custom the heap min size
JVM_XMS=${JVM_XMS:-300M}
#auto the heap new size ($HEAP_NEWSIZE) or leave it's empty  or custom the heap new size
JVM_XMN=${JVM_XMN}
#jmx monitoring
JVM_JMX_HOST=${JVM_JMX_HOST}
JVM_JMX_PORT=${JVM_JMX_PORT}
#remote debug
JVM_JDWP_PORT=${JVM_JDWP_PORT}

#jvm extra options
JVM_EXTRA_ARGS=${JVM_EXTRA_ARGS:-}
