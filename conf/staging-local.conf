server {
  http {
    port = ":8080"
  }
  thrift {
    port = ":8084"
  }
  admin {
    disable = false
  }
}

main {
  sql_debug = true

  list_pnl = [
    "TEST_PNL",
    "VINFAST",
    "VINHOMES",
    "VINME<PERSON>",
    "VINPEARL",
    "VINSCHO<PERSON>",
    "GSM",
    "FGF",
    "VGREEN"
  ]

  list_pnl_mapping_by_identity_doc = [
    "VINFAST",
    "VINHOMES"
  ]

  internal_secret_key = ""
  internal_secret_key = ${?INTERNAL_SECRET_KEY}
  internal_identify_verification_secret_key = ""
  internal_identify_verification_secret_key = ${?INTERNAL_IDENTIFY_VERIFICATION_SECRET_KEY}
}

common {
  quota_ip = 10
}

postgresql {
  driver = "org.postgresql.Driver"
  host = "vhm-vinclub-stag-rds-postgresql16.chgikaeouto3.ap-southeast-1.rds.amazonaws.com"
  port = 5432
  user = "vinclub_core_user"
  pass = "RYKAzUlByG6A7DC4Po35Bbh5QMpoElw"
  db = "vinclub_db"
  schema = "core_db"

  init_sql = []

}

caas {

  salt = ""
  salt = ${?CAAS_SALT}
  old_salt = ""
  old_salt = ${?OLD_CAAS_SALT}

  verify_phone {
    facebook = true
    u_p = true
    google = true
    apple = true
  }

  oauth {
    google {
      app_id = []
    }
    facebook {
      app_id_old = ""
      app_secret_old = ""

      app_id = ""
      app_secret = ""
    }
    apple {
      auth_url = "https://appleid.apple.com/auth/token"
      client_id = "vn.vinclub"
      team_id = ""
      key_id = ""
      private_key_path = "conf/apple_token/Vinhomes_AuthKey_....p8"
    }
  }
}

ssdb {
  common {
    host = "*************"
    port = 8888
    timeoutInMs = 10000
  }
  data {
    host = "*************"
    port = 8888
    timeoutInMs = 2000

    hash_customer_ranking = "vclub:data:customer_ranking"
    zset_customer_ranking = "vclub:data:customer_ranking:last_change"
  }
}

verify_phonenumber_service {
  message_template = "VinClub verify code: $code"
  message_template_ = "Xin chao, day la tin nhan duoc gui ra tu he thong VinClub, thong tin ma xac thuc cua ban la $code"
  code_expire_time_in_second = 180

  constant_otp {
    "84934634636" = "032517"
  }

  message_template_by_prefix = [
    // vinaphone
    {
      prefix_numbers = [
        "088", "091", "094", "081", "082", "083", "084", "085",
        "8488", "8491", "8494", "8481", "8482", "8483", "8484", "8485",
        "+8488", "+8491", "+8494", "+8481", "+8482", "+8483", "+8484", "+8485"
      ]
      message_template = "Ma xac thuc VinClub cua ban la $code"
    }
  ]

}

sms {
  src_delivery = "auth_system"
  receivers_status_key = "receivers_status"
  enable = false

  quota_phonenumber = 5
  quota_phonenumber_expire_time_in_second = 2592000
}

email {
  enable = false

  quota_email = 5
  quota_email_expire_time_in_second = 2592000

  send_otp_subject = "Mã xác minh email cho hệ thống VinClub"
}

delivery_service {
  thrift {
    host = "message-delivery-service"
    port = 11145
  }
}

kafka {

  pnl_customer_historical_consumer {
    topic_regex = "^historical\\.pnl_customer_(.+)"
    bootstrap.servers = "localhost:9092"
    group.id = "vclub-customer-svc-pnl-customer-historical-group-stag"
    client.id = "vclub-customer-svc-pnl-customer-historical-client-stag"
    options = {
      "enable.auto.commit" = false
      "max.poll.records" = 1
      "session.timeout.ms" = 120000
      "request.timeout.ms" = 125000
      "security.protocol" = "SASL_SSL"
      "sasl.mechanism" = "SCRAM-SHA-512"
      "sasl.jaas.config" = "org.apache.kafka.common.security.scram.ScramLoginModule required username=\"vhm-vinclub-stag-msk-consumer\" password=\"vhm-vinclub-stag-msk-consumerabcd!\";"
    }
  }

  producer {
    bootstrap.servers = "localhost:9092"
    acks = "all"
    linger.ms = 1
    security.protocol = "SASL_SSL"
    sasl.mechanism = "SCRAM-SHA-512"
    sasl.jaas.config = "org.apache.kafka.common.security.scram.ScramLoginModule required username=\"vhm-vinclub-stag-msk-consumer\" password=\"vhm-vinclub-stag-msk-consumerabcd!\";"
  }

  customer_tier_changed_topic = "vclub_customer.tier_changed"
  customer_historical_topic = "historical.vclub_customer"
  pnl_customer_change_trigger_log_topic = "log.vclub_customer.pnl_customer_change_trigger"

  customer_new_signedin_topic = "vclub_customer.new_signedin"
  customer_auto_approve_identity_verification_topic = "vclub_customer.auto_approve_identity_verification"
}

kafka_delivery {
  producer {
    bootstrap.servers = "localhost:9092"
    acks = "all"
    linger.ms = 1
    security.protocol = "SASL_SSL"
    sasl.mechanism = "SCRAM-SHA-512"
    sasl.jaas.config = "org.apache.kafka.common.security.scram.ScramLoginModule required username=\"vhm-vinclub-stag-msk-consumer\" password=\"vhm-vinclub-stag-msk-consumerabcd!\";"
  }
  topic = "personal_message_delivery"
}

session {
  jwt {
    algo = "RS256"
    key_id = ${?JWT_KEY_ID}
    secret_key_rs256_path = "conf/rs256-key/stag/jwtRS256.key"
    public_key_rs256_path = "conf/rs256-key/stag/jwtRS256.key.pub"

    access_token_expire_in_second = 3600
    refresh_token_expire_in_second = 86400000
  }
}

recaptcha_service {
  url_verify = ""
  secret_key = ""
}

authen_service {
  max_login_failed = 5
  // 4p
  lock_login_in_millis = 240000
  reset_num_login_failed_after_in_millis = 240000
}

sync_schedule {

  sync_customer_ranking {
    enable = false
    interval_in_second = 60
  }

  deactivate_identity_verification {
    enable = true
    schedule_at_hour = 5
    sync_before_in_hour = 25
  }
}

redis {
  host = "localhost"
  port = 6379
}

vclub_core_service {
  base_url = "http://stag-svc.vinclub.internal:30082/r"
  authz_token = "Bearer eyJ4NXQiOiJNell4TW1Ga09HWXdNV0kwWldObU5EY3hOR1l3WW1NNFpUQTNNV0kyTkRBelpHUXpOR00wWkdSbE5qSmtPREZrWkRSaU9URmtNV0ZoTXpVMlpHVmxOZyIsImtpZCI6Ik16WXhNbUZrT0dZd01XSTBaV05tTkRjeE5HWXdZbU00WlRBM01XSTJOREF6WkdRek5HTTBaR1JsTmpKa09ERmtaRFJpT1RGa01XRmhNelUyWkdWbE5nX1JTMjU2IiwiYWxnIjoiUlMyNTYifQ.****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.G-LkoaWweSHVyUL3FQmqNQ2m3Lkzpz2nKdtMMmK2s-Ax5Tom3bpktq02KTcJfuEIZc4chqL1Ry7XvBAluUuhCwUa869LY4DXIKJqoWKqH7BA-OCHR9JorjezJ4luG91_EWxmaMWbwfR37UxkMeHvKYfT76FfkErT8SpY2zX84YEtw0sAu2cVIPrA3sF4-0h7_5-kkx9BHUge8__8xKVzvoeQbeHwLKEkC2gBcnCz8N8KlgtEqGPdSACB3T0QjHfal7l68mfpk0ggSjU36TlODPTwN0YPMovOp2Kp443Qw-HBzm3ykOr2WT6Sniyi937m5LT4JEJ0x9paS9mC_9FUAg"
  connect_timeout_ms = 30000
  request_timeout_ms = 30000
}

files_client {
  url = "http://stag-svc.vinclub.internal:31160"
  integrationkey = ""
  integrationkey = ${?FILES_CLIENT_INTEGRATION_KEY}
}

ai_integration {
  extract_identify_url = "http://stag-svc.vinclub.internal:31702/extract_identify"
  matching_faces_url = "http://stag-svc.vinclub.internal:31718/matching_faces"
  extract_address_url = "http://stag-svc.vinclub.internal:31704/extract_detailed_addresses"
}

s3 {
  region = "ap-southeast-1"
  bucket = "vhm-vinclub-stag-file"
  base_path = "customer"

  temporary_bucket = "vhm-vinclub-stag-temporary"
  temporary_prefix_path = "customer-svc"

  customer_bucket = "vhm-vinclub-stag-customer"
  customer_base_path = "customer"
}

imgproxy_service {
  base_url = "http://stag-svc.vinclub.internal:31568"
  service_key = ""
  service_key = ${?IMGPROXY_SERVICE_KEY}
  service_salt = ""
  service_salt = ${?IMGPROXY_SERVICE_SALT}
}

internal_secret_key {
  internal_auth = [
    "dmNsdWItcGFydG5lcjpOeWticGRId0pZZmFpd3NCUXBpcnlqWUV6c05RblB0ejJr",
    "dmNsdWItc3NvOmh3VFgzajdEMmc0U1FiOXp6b0ZtN1NFZDdqRGU1WGRKM3F0WFZNekdB"
  ]

}
