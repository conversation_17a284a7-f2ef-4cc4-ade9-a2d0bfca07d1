server {
  http {
    port = ":8080"
    max_header_size_in_kb = 16
    max_header_size_in_kb = ${?MAX_HEADER_SIZE_IN_KB}
  }
  thrift {
    port = ":8084"
  }
  admin {
    disable = false
  }
}

alert_info {
  app_name = "vclub-customer-service"
  telegram {
    url = ${TELEGRAM_URL}
    receivers = ["-4265298818"]
  }
}

main {
  sql_debug = true

  list_pnl = [
    "VINFAST",
    "VINHOMES",
    "VINMEC",
    "VINPEARL",
    "VINSCHOOL",
    "GSM",
    "FGF"
  ]

  list_pnl_mapping_by_identity_doc = [
    "VINFAST",
    "VINHOMES"
  ]

  list_customer_score_type = [
    "HD_VIKKI",
    "VCLUB"
  ]

  internal_secret_key = ""
  internal_secret_key = ${?INTERNAL_SECRET_KEY}
  internal_identify_verification_secret_key = ${INTERNAL_IDENTIFY_VERIFICATION_SECRET_KEY}
}

common {
  recent_delay_in_millis = 5000
  quota_ip = 150
  //2h
  quota_ip_expire_time_in_second = 7200
  lock_in_second = 60
}

postgresql {
  driver = "org.postgresql.Driver"
  host = "vinclub-prod-rds-postgresql16.cpa6qwm0uhbu.ap-southeast-1.rds.amazonaws.com"
  port = 5432
  user = "vinclub_core_user"
  pass = "c0qW1GqC4KzDlbUtGJlV6I3kSBXPJF5"
  db = "vinclub_db"
  schema = "core_db"

  init_sql = []
}

caas {

  salt = ${CAAS_SALT}
  old_salt = ""
  old_salt = ${?OLD_CAAS_SALT}

  verify_phone {
    facebook = true
    u_p = true
    google = true
    apple = true
  }

  oauth {
    google {
      app_id = []
    }
    facebook {
      app_id_old = ""
      app_secret_old = ""

      app_id = ""
      app_secret = ""
    }
    apple {
      auth_url = "https://appleid.apple.com/auth/token"
      client_id = "vn.vinclub"
      team_id = ""
      key_id = ""
      private_key_path = "conf/apple_token/Vinhomes_AuthKey_....p8"
    }
  }
}

ssdb {
  common {
    host = "************"
    port = 8888
    timeoutInMs = 10000
  }
  data {
    host = "************"
    port = 8888
    timeoutInMs = 2000

    hash_customer_ranking = "vclub:data:customer_ranking"
    zset_customer_ranking = "vclub:data:customer_ranking:last_change"

    hash_cdp_profile = "vclub:data:cdp_profile"
    zset_cdp_profile = "vclub:data:cdp_profile:last_change"
  }
}

verify_phonenumber_service {
  message_template = "Ma xac thuc VinClub cua ban la $code"
  message_template_for_android = "Ma xac thuc VinClub cua ban la $code"
  message_template_for_ios = "Ma xac thuc VinClub cua ban la $code"
  code_expire_time_in_second = 180

  constant_otp {
    "+84934634636" = "032717"
  }

  message_template_by_prefix = [
    // viettel
    {
      prefix_numbers = [
        "086", "096", "097", "098", "039", "038", "037", "036", "035", "034", "033", "032",
        "8486", "8496", "8497", "8498", "8439", "8438", "8437", "8436", "8435", "8434", "8433", "8432",
        "+8486", "+8496", "+8497", "+8498", "+8439", "+8438", "+8437", "+8436", "+8435", "+8434", "+8433", "+8432"
      ]
      message_template = "Ma xac thuc VinClub cua ban la $code"
      message_template_for_android = "Ma xac thuc VinClub cua ban la $code.\n\n2RxTuo/Bh4E"
      message_template_for_ios = "Ma xac thuc VinClub cua ban la $code.\n\<EMAIL> #$code"
    },
    // mobile
    {
      prefix_numbers = [
        "070", "079", "077", "076", "078", "089", "090", "093",
        "84784", "8479", "8477", "8476", "8478", "8489", "84984", "8493",
        "+847+84", "+8479", "+8477", "+8476", "+8478", "+8489", "+849+84", "+8493",
      ]
      message_template = "Ma xac thuc VinClub cua ban la $code"
      message_template_for_android = "Ma xac thuc VinClub cua ban la $code.\n\n2RxTuo/Bh4E"
      message_template_for_ios = "Ma xac thuc VinClub cua ban la $code.\n\<EMAIL> #$code"
    }
  ]

}

sms {
  src_delivery = "auth_system"
  receivers_status_key = "receivers_status"
  enable = true

  quota_phonenumber = 5
  //2h
  quota_phonenumber_expire_time_in_second = 7200
}

email {
  enable = true

  quota_email = 5
  //2h
  quota_email_expire_time_in_second = 7200

  send_otp_subject = "Mã xác minh email cho hệ thống VinClub"

  // EmailHelper configuration
  helper {
    check_disposable_email = true
    check_domain_blacklist = true
    check_domain_whitelist = true
    check_email_blacklist = true
    check_email_whitelist = true
  }
}

delivery_service {
  thrift {
    host = "message-delivery-service.vinclub-backend-prod"
    port = 11145
  }
}

kafka {

  pnl_customer_historical_consumer_enable = true

  pnl_customer_historical_consumer {
    topic_regex = "^historical\\.pnl_customer_(.+)"
    bootstrap.servers = "b-1.vinclubprodmskclu.pa3gh0.c3.kafka.ap-southeast-1.amazonaws.com:9096"
    group.id = "vclub-customer-svc-pnl-customer-historical-group-prod"
    client.id = "vclub-customer-svc-pnl-customer-historical-client-prod"
    options = {
      "enable.auto.commit" = false
      "max.poll.records" = 1
      "session.timeout.ms" = 120000
      "request.timeout.ms" = 125000
      "security.protocol" = "SASL_SSL"
      "sasl.mechanism" = "SCRAM-SHA-512"
      "sasl.jaas.config" = "org.apache.kafka.common.security.scram.ScramLoginModule required username=\"vinclub-prod-msk-consumer\" password=\"vinclub-prod-msk-consumerP10lFM3gG4qP\";"
    }
  }

  customer_new_signedin_event_consumer {
    topics = ["vclub_customer.new_signedin"]
    bootstrap.servers = "b-1.vinclubprodmskclu.pa3gh0.c3.kafka.ap-southeast-1.amazonaws.com:9096"
    group.id = "vclub-customer-svc-customer-new-signedin-group-prod"
    client.id = "vclub-customer-svc-customer-new-signedin-client-prod"
    options = {
      "enable.auto.commit" = false
      "max.poll.records" = 1
      "session.timeout.ms" = 120000
      "request.timeout.ms" = 125000
      "security.protocol" = "SASL_SSL"
      "sasl.mechanism" = "SCRAM-SHA-512"
      "sasl.jaas.config" = "org.apache.kafka.common.security.scram.ScramLoginModule required username=\"vinclub-prod-msk-consumer\" password=\"vinclub-prod-msk-consumerP10lFM3gG4qP\";"
    }
  }

  customer_identity_verification_event_consumer_enable = true
  customer_identity_verification_event_consumer {
    topics = ["system_event_customer_identity_verification"]
    bootstrap.servers = "b-1.vinclubprodmskclu.pa3gh0.c3.kafka.ap-southeast-1.amazonaws.com:9096"
    group.id = "vclub-customer-svc-customer-identity-verification-group-prod"
    client.id = "vclub-customer-svc-customer-identity-verification-client-prod"
    options = {
      "enable.auto.commit" = false
      "max.poll.records" = 1
      "session.timeout.ms" = 120000
      "request.timeout.ms" = 125000
      "security.protocol" = "SASL_SSL"
      "sasl.mechanism" = "SCRAM-SHA-512"
      "sasl.jaas.config" = "org.apache.kafka.common.security.scram.ScramLoginModule required username=\"vinclub-prod-msk-consumer\" password=\"vinclub-prod-msk-consumerP10lFM3gG4qP\";"
    }
  }

  producer {
    bootstrap.servers = "b-1.vinclubprodmskclu.pa3gh0.c3.kafka.ap-southeast-1.amazonaws.com:9096"
    acks = "all"
    linger.ms = 1
    security.protocol = "SASL_SSL"
    sasl.mechanism = "SCRAM-SHA-512"
    sasl.jaas.config = "org.apache.kafka.common.security.scram.ScramLoginModule required username=\"vinclub-prod-msk-consumer\" password=\"vinclub-prod-msk-consumerP10lFM3gG4qP\";"
  }

  customer_tier_changed_topic = "vclub_customer.tier_changed"
  customer_historical_topic = "historical.vclub_customer"
  pnl_customer_change_trigger_log_topic = "log.vclub_customer.pnl_customer_change_trigger"

  customer_new_signedin_topic = "vclub_customer.new_signedin"
  customer_auto_approve_identity_verification_topic = "vclub_customer.auto_approve_identity_verification"
}

kafka_delivery {
  producer {
    bootstrap.servers = "b-1.vinclubprodmskclu.pa3gh0.c3.kafka.ap-southeast-1.amazonaws.com:9096"
    acks = "all"
    linger.ms = 1
    security.protocol = "SASL_SSL"
    sasl.mechanism = "SCRAM-SHA-512"
    sasl.jaas.config = "org.apache.kafka.common.security.scram.ScramLoginModule required username=\"vinclub-prod-msk-consumer\" password=\"vinclub-prod-msk-consumerP10lFM3gG4qP\";"
  }
  topic = "personal_message_delivery"
}

session {
  jwt {
    algo = "RS256"
    key_id = ${JWT_KEY_ID}
    secret_key_rs256_path = "conf/rs256-key/prod/jwtRS256.key"
    public_key_rs256_path = "conf/rs256-key/prod/jwtRS256.key.pub"

    access_token_expire_in_second = 3600
    //6thang
    refresh_token_expire_in_second = 15552000
  }
}

authen_service {
  max_login_failed = 5
  //30p
  lock_login_in_millis = 1800000
  //30p
  reset_num_login_failed_after_in_millis = 1800000
}

sync_schedule {

  sync_customer_ranking {
    enable = true
    interval_in_second = 60
  }

  sync_customer_cdp_profile {
    enable = true
    interval_in_second = 60
  }

  deactivate_identity_verification {
    enable = true
    schedule_at_hour = 5
    sync_before_in_hour = 25
    sync_before_in_hour = ${?DEACTIVATE_IDENTITY_VERIFICATION_SYNC_BEFORE_IN_HOUR}
  }

}

redis {
  host = "vinclub-prod-redis-azc.chyhmz.0001.apse1.cache.amazonaws.com"
  port = 6379
}

vclub_core_service {
  base_url = "http://core-service.vinclub-backend-prod:80/r"
  authz_token = ${AUTHZ_VCLB_CORE_SVC_TOKEN}
  connect_timeout_ms = 30000
  request_timeout_ms = 30000
}

vclub_shield_service {
  base_url = "http://vclub-shield-service.vinclub-backend-prod:8080/sh"
  connect_timeout_ms = 30000
  request_timeout_ms = 30000
  enable = ${SHIELD_ENABLE}
  no_app_check_latest_build_number = ${NO_APP_CHECK_LATEST_BUILD_NUMBER}
}

files_client {
  url = "http://vclub-files-service.vinclub-backend-prod:8080"
  integrationkey = ${FILES_CLIENT_INTEGRATION_KEY}
}

ai_integration {
  extract_identify_url = "http://identify-ocr-service.vinclub-ai-prod:11702/extract_identify"
  matching_faces_url = "http://ekyc-service.vinclub-ai-prod:11718/matching_faces"
  extract_address_url = "http://ner-service.vinclub-ai-prod:11704/extract_detailed_addresses"
}

s3 {
  region = "ap-southeast-1"
  bucket = "vinclub-prod-file"
  base_path = "customer"

  temporary_bucket = "vinclub-prod-temporary"
  temporary_prefix_path = "customer-svc"

  customer_bucket = "vinclub-prod-customer"
  customer_base_path = "customer"
}

imgproxy_service {
  base_url = "http://imgproxy-service.vinclub-backend-prod:11568"
  service_key = ${IMGPROXY_SERVICE_KEY}
  service_salt = ${IMGPROXY_SERVICE_SALT}
}

internal_secret_key {
  internal_auth = [
    // partner
    "dmNsdWItcGFydG5lcjpWZ0tBOGZoU3RaUU9USElEUXNTZkRFOThaOXFGV0toSmpRY3hk",
    // sso 1
    "dmNsdWItc3NvOjdYczhjUExvWEQ2WlZrR1hCQ2FwWUFzMDQ3STBCMXpJSW85WmhPZnM=",
    // gamification
    "dmNsdWItZ2FtaWZpY2F0aW9uOlZnTlpTVVFhRVlSZElCWkh1djNWZjlNOEtRUmNB",
    // sso 2
    "dmNsdWItc3NvOkhTYkxlanFXVUFCT0RTdTZqSVltWjBFSGpwNTV4OU5telJZRnN5",
    // miniapp
    "dmNsdWItbWluaWFwcDpNWENiSFFHS3ZpUUNIbFJnT3BGSzJOWndsaEY="
  ]
}

vin_bigdata_integration {
  base_url = ${VIN_BIGDATA_BASE_URL}
  app_id = ${VIN_BIGDATA_APP_ID}
  app_secret = ${VIN_BIGDATA_APP_SECRET}
  encrypt_key = ${VIN_BIGDATA_ENCRYPT_KEY}

  // document type supported
  document_type_supported = ${VIN_BIGDATA_DOCUMENT_TYPE_SUPPORTED}

  // document verify step
  id_card_verify_steps = ${VIN_BIGDATA_ID_CARD_VERIFY_STEPS}
  chip_based_id_card_verify_steps = ${VIN_BIGDATA_CHIP_BASED_ID_CARD_VERIFY_STEPS}
  white_id_card_verify_steps = ${VIN_BIGDATA_WHITE_ID_CARD_VERIFY_STEPS}
  legacy_id_verify_steps = ${VIN_BIGDATA_LEGACY_ID_VERIFY_STEPS}
  passport_verify_steps = ${VIN_BIGDATA_PASSPORT_VERIFY_STEPS}

  // scan face flow configs
  use_face_compare = ${VIN_BIGDATA_USE_FACE_COMPARE}
  use_face_liveness = ${VIN_BIGDATA_USE_FACE_LIVENESS}

  // test scenarios configs
  enable_test_scenarios = ""
}

admin_create_user {
  referral_code = {
    vinfast = "VFACC-ALL"
    vinhomes = "VHMACC-ALL"
    vinmec = "VMACC-ALL"
    vinpearl = "VPACC-ALL"
    vinschool = "VSCACC-ALL"
    gsm = "GSMACC-ALL"
    fgf = "GFACC-ALL"
  }
}

user_registration_queue {
  enable = ${USER_REGISTRATION_QUEUE_ENABLE}
  queue_key = "vclub:user_registration_queue"
  batch_size = ${USER_REGISTRATION_QUEUE_BATCH_SIZE}
  interval_in_second = ${USER_REGISTRATION_QUEUE_INTERVAL_IN_SECOND}
}