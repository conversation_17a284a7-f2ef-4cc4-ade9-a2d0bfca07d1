<configuration>
    <shutdownHook class="ch.qos.logback.core.hook.DelayingShutdownHook"/>

    <!-- ===================================================== -->
    <!-- Common Config -->
    <!-- ===================================================== -->

    <!-- JUL/JDK14 to Logback bridge -->
    <contextListener class="ch.qos.logback.classic.jul.LevelChangePropagator">
        <resetJUL>true</resetJUL>
    </contextListener>

    <appender name="STDOUT" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <pattern>%date %.-3level %-16X{traceId} %-25logger{0} %msg%n</pattern>
        </encoder>
    </appender>

    <!-- Service Log (Rollover every 50MB, max 11 logs) -->
    <appender name="SERVICE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>logs/service.log</file>
        <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
            <level>INFO</level>
        </filter>
        <rollingPolicy class="ch.qos.logback.core.rolling.FixedWindowRollingPolicy">
            <fileNamePattern>logs/service.log.%i</fileNamePattern>
            <minIndex>1</minIndex>
            <maxIndex>2</maxIndex>
        </rollingPolicy>
        <triggeringPolicy class="ch.qos.logback.core.rolling.SizeBasedTriggeringPolicy">
            <maxFileSize>50MB</maxFileSize>
        </triggeringPolicy>
        <encoder>
            <pattern>%date %.-3level %-16X{traceId} %-25logger{0} %msg%n</pattern>
        </encoder>
    </appender>

    <appender name="SERVICE-ERROR" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>logs/service-error.log</file>
        <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
            <level>ERROR</level>
        </filter>
        <rollingPolicy class="ch.qos.logback.core.rolling.FixedWindowRollingPolicy">
            <fileNamePattern>logs/service-error.log.%i</fileNamePattern>
            <minIndex>1</minIndex>
            <maxIndex>2</maxIndex>
        </rollingPolicy>
        <triggeringPolicy class="ch.qos.logback.core.rolling.SizeBasedTriggeringPolicy">
            <maxFileSize>50MB</maxFileSize>
        </triggeringPolicy>
        <encoder>
            <pattern>%date %.-3level %-16X{traceId} %-25logger{0} %msg%n</pattern>
        </encoder>
    </appender>

    <!-- Access Log (Rollover every 50MB, max 11 logs) -->
    <appender name="ACCESS" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>logs/access.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.FixedWindowRollingPolicy">
            <fileNamePattern>logs/access.log.%i</fileNamePattern>
            <minIndex>1</minIndex>
            <maxIndex>2</maxIndex>
        </rollingPolicy>
        <triggeringPolicy class="ch.qos.logback.core.rolling.SizeBasedTriggeringPolicy">
            <maxFileSize>50MB</maxFileSize>
        </triggeringPolicy>
        <encoder>
            <pattern>%msg %X{traceId}%n</pattern>
        </encoder>
    </appender>

    <appender name="THRIFT-ACCESS" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>logs/thrift-access.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.FixedWindowRollingPolicy">
            <fileNamePattern>logs/thrift-access.log.%i</fileNamePattern>
            <minIndex>1</minIndex>
            <maxIndex>2</maxIndex>
        </rollingPolicy>
        <triggeringPolicy class="ch.qos.logback.core.rolling.SizeBasedTriggeringPolicy">
            <maxFileSize>50MB</maxFileSize>
        </triggeringPolicy>
        <encoder>
            <pattern>%msg %X{traceId}%n</pattern>
        </encoder>
    </appender>

    <appender name="KafkaInteractiveLog" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>logs/kafka_interactive.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.FixedWindowRollingPolicy">
            <fileNamePattern>logs/kafka_interactive.log.%i</fileNamePattern>
            <minIndex>1</minIndex>
            <maxIndex>2</maxIndex>
        </rollingPolicy>
        <triggeringPolicy class="ch.qos.logback.core.rolling.SizeBasedTriggeringPolicy">
            <maxFileSize>50MB</maxFileSize>
        </triggeringPolicy>
        <encoder>
            <pattern>%date %.-3level %-16X{traceId} %msg%n</pattern>
        </encoder>
    </appender>

    <appender name="KafkaLogger" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>logs/kafka.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.FixedWindowRollingPolicy">
            <fileNamePattern>logs/kafka.log.%i</fileNamePattern>
            <minIndex>1</minIndex>
            <maxIndex>2</maxIndex>
        </rollingPolicy>
        <triggeringPolicy class="ch.qos.logback.core.rolling.SizeBasedTriggeringPolicy">
            <maxFileSize>20MB</maxFileSize>
        </triggeringPolicy>
        <encoder>
            <pattern>%date %.-3level %-16X{traceId} %-25logger{0} %msg%n</pattern>
        </encoder>
    </appender>

    <appender name="VerifyDataLog" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>logs/verify_data.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.FixedWindowRollingPolicy">
            <fileNamePattern>logs/verify_data.log.%i</fileNamePattern>
            <minIndex>1</minIndex>
            <maxIndex>2</maxIndex>
        </rollingPolicy>
        <triggeringPolicy class="ch.qos.logback.core.rolling.SizeBasedTriggeringPolicy">
            <maxFileSize>20MB</maxFileSize>
        </triggeringPolicy>
        <encoder>
            <pattern>%date %.-3level %-16X{traceId} %-25logger{0} %msg%n</pattern>
        </encoder>
    </appender>

    <appender name="SqlDebugLog" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>logs/sql_debug.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.FixedWindowRollingPolicy">
            <fileNamePattern>logs/sql_debug.log.%i</fileNamePattern>
            <minIndex>1</minIndex>
            <maxIndex>2</maxIndex>
        </rollingPolicy>
        <triggeringPolicy class="ch.qos.logback.core.rolling.SizeBasedTriggeringPolicy">
            <maxFileSize>20MB</maxFileSize>
        </triggeringPolicy>
        <encoder>
            <pattern>%date %.-3level %-16X{traceId} %-25logger{0} %msg%n</pattern>
        </encoder>
    </appender>

    <appender name="HttpQuotaServiceLog" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>logs/quota_service.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.FixedWindowRollingPolicy">
            <fileNamePattern>logs/quota_service.log.%i</fileNamePattern>
            <minIndex>1</minIndex>
            <maxIndex>2</maxIndex>
        </rollingPolicy>
        <triggeringPolicy class="ch.qos.logback.core.rolling.SizeBasedTriggeringPolicy">
            <maxFileSize>50MB</maxFileSize>
        </triggeringPolicy>
        <encoder>
            <pattern>%date %.-3level %-16X{traceId} %msg%n</pattern>
        </encoder>
    </appender>

    <appender name="SendSMSStatsLog" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>logs/send_sms_stats.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.FixedWindowRollingPolicy">
            <fileNamePattern>logs/send_sms_stats.log.%i</fileNamePattern>
            <minIndex>1</minIndex>
            <maxIndex>2</maxIndex>
        </rollingPolicy>
        <triggeringPolicy class="ch.qos.logback.core.rolling.SizeBasedTriggeringPolicy">
            <maxFileSize>20MB</maxFileSize>
        </triggeringPolicy>
        <encoder>
            <pattern>%date %.-3level %-16X{traceId} %-25logger{0} %msg%n</pattern>
        </encoder>
    </appender>

    <!-- ===================================================== -->
    <!-- Primary Async Appenders -->
    <!-- ===================================================== -->

    <property name="async_queue_size" value="${queue.size:-1024}"/>
    <property name="async_max_flush_time" value="${max.flush.time:-0}"/>

    <appender name="ASYNC-SERVICE" class="ch.qos.logback.classic.AsyncAppender">
        <queueSize>${async_queue_size}</queueSize>
        <maxFlushTime>${async_max_flush_time}</maxFlushTime>
        <appender-ref ref="SERVICE"/>
    </appender>
    <appender name="ASYNC-SERVICE-ERROR" class="ch.qos.logback.classic.AsyncAppender">
        <queueSize>${async_queue_size}</queueSize>
        <maxFlushTime>${async_max_flush_time}</maxFlushTime>
        <appender-ref ref="SERVICE-ERROR"/>
    </appender>

    <appender name="ASYNC-ACCESS" class="ch.qos.logback.classic.AsyncAppender">
        <queueSize>${async_queue_size}</queueSize>
        <maxFlushTime>${async_max_flush_time}</maxFlushTime>
        <appender-ref ref="ACCESS"/>
    </appender>

    <appender name="THRIFT-ASYNC-ACCESS" class="ch.qos.logback.classic.AsyncAppender">
        <queueSize>${async_queue_size}</queueSize>
        <maxFlushTime>${async_max_flush_time}</maxFlushTime>
        <appender-ref ref="THRIFT-ACCESS"/>
    </appender>

    <!-- ===================================================== -->
    <!-- Package Config -->
    <!-- ===================================================== -->

    <!-- Root Config -->
    <root level="${log_level:-INFO}">
        <appender-ref ref="STDOUT"/>
        <appender-ref ref="ASYNC-SERVICE"/>
        <appender-ref ref="ASYNC-SERVICE-ERROR"/>
        <appender-ref ref="Sentry"/>
    </root>

    <!-- Access Logging -->
    <logger name="com.twitter.finatra.http.filters.AccessLoggingFilter" level="info" additivity="false">
        <!--        <appender-ref ref="STDOUT"/>-->
        <appender-ref ref="ASYNC-ACCESS"/>
    </logger>

    <logger name="com.twitter.finatra.thrift.filters.AccessLoggingFilter" level="info" additivity="false">
        <!--        <appender-ref ref="STDOUT"/>-->
        <appender-ref ref="THRIFT-ASYNC-ACCESS"/>
    </logger>

    <!-- Put kafka Logging -->
    <logger name="PutKafkaLogger" level="debug" additivity="false">
        <!--        <appender-ref ref="STDOUT"/>-->
        <appender-ref ref="KafkaInteractiveLog"/>
    </logger>

    <logger name="ConsumeKafkaLogger" level="debug" additivity="false">
        <!--        <appender-ref ref="STDOUT"/>-->
        <appender-ref ref="KafkaInteractiveLog"/>
    </logger>

    <logger name="VerifyDataLogger" level="all" additivity="false">
        <appender-ref ref="VerifyDataLog"/>
    </logger>

    <logger name="SqlDebugLogger" level="all" additivity="false">
        <appender-ref ref="SqlDebugLog"/>
    </logger>

    <logger name="org.apache.kafka" level="debug" additivity="false">
        <appender-ref ref="KafkaLogger"/>
    </logger>

    <logger name="SendSMSStatsLogger" level="all" additivity="false">
        <appender-ref ref="SendSMSStatsLog"/>
    </logger>

</configuration>
