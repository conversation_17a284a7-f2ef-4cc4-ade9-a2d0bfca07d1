apiVersion: apps/v1
kind: Deployment
metadata:
  name: vclub-customer-auth-deployment
  namespace: vinclub-backend-stag
spec:
  replicas: 1
  progressDeadlineSeconds: 120
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 1        # how many pods we can add at a time
      maxUnavailable: 0  # maxUnavailable define how many pods can be unavailable # during the rolling update
  selector:
    matchLabels:
      app: vclub-customer-auth
  template:
    metadata:
      labels:
        app: vclub-customer-auth
    spec:
      affinity:
        nodeAffinity:
          requiredDuringSchedulingIgnoredDuringExecution:
            nodeSelectorTerms:
              - matchExpressions:
                  - key: vhm-type
                    operator: In
                    values:
                      - main16
                      - main8
                      - main
                  - key: sv-type
                    operator: In
                    values:
                      - on-demand
                      - spot
          preferredDuringSchedulingIgnoredDuringExecution:
            - weight: 1
              preference:
                matchExpressions:
                  - key: sv-type
                    operator: In
                    values:
                      - spot
                      #- on-demand
      serviceAccountName: vhm-vinclub-customer-svc-sa
      containers:
        - name: vclub-customer
          image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/vclub-customer-service:staging
          args: [ "startservice","eks-staging" ]
          resources:
            requests:
              memory: "1000Mi"
              cpu: "3000m"
            limits:
              memory: "1000Mi"
              cpu: "3000m"
          lifecycle:
            preStop:
              exec:
                command: [ "/bin/sh", "-ec", "sleep 15" ]
          imagePullPolicy: Always
          ports:
            - name: http-port
              containerPort: 8080
          startupProbe:
            tcpSocket:
              port: http-port
            failureThreshold: 30
            periodSeconds: 20
          #          readinessProbe:
          #            tcpSocket:
          #              port: 8080
          #            initialDelaySeconds: 60
          #            periodSeconds: 10
          #            successThreshold: 1
          #            timeoutSeconds: 3
          #            failureThreshold: 3
          #          envFrom:
          #            - configMapRef:
          #                name: vclub-customer
          envFrom:
            - configMapRef:
                name: vclub-customer
          env:
            - name: TZ
              value: "Asia/Ho_Chi_Minh"
            - name: JVM_XMS
              value: "400M"
            - name: JVM_XMX
              value: "600M"
            - name: JVM_EXTRA_ARGS
              value: "-Dconfig.override_with_env_vars=true"
            - name: CONFIG_FORCE_sync___schedule_sync___customer___ranking_enable
              value: "false"
            - name: CONFIG_FORCE_sync___schedule_deactivate___identity___verification_enable
              value: "false"
            - name: CONFIG_FORCE_kafka_pnl___customer___historical___consumer___enable
              value: "false"
            - name: CONFIG_FORCE_alert___info_app___name
              value: "vclub-customer-auth-service"
            - name: CONFIG_FORCE_user___registration___queue_enable
              value: "false"
