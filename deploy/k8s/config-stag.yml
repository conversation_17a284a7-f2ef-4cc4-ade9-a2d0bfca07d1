apiVersion: v1
kind: ConfigMap
metadata:
  name: vclub-customer
  namespace: vinclub-backend-stag
data:
  JVM_EXTRA_ARGS: "-javaagent:/project/elastic-apm-agent.jar -Delastic.apm.service_name=vinclub-customer-service -Delastic.apm.application_packages=caas,core,profile,vn.vinclub -Delastic.apm.server_url=https://stag-apm.vinhomes.vn -Delastic.apm.secret_token=6NZOW0qPVsfVIdKv1oVZa0Gq -Delastic.apm.environment=stag"
  TELEGRAM_URL: "https://api.telegram.org/bot7396961283:AAFHcU9DggHV7BSNxXCvgmVop94E2pdeSXU/sendMessage"

  CAAS_SALT: "$2a$10$bx/IWM2XZPiZ.friGYB0T."
  OLD_CAAS_SALT: "xIYt5L6Lbb87z2HIlMVukqDTOdaeHx937EWphBMhdNY="
  JWT_KEY_ID: "16a57eff-f5e4-4214-a819-cd1f5fa523a3"
  AUTHZ_VCLB_CORE_SVC_TOKEN: "Bearer eyJ4NXQiOiJNell4TW1Ga09HWXdNV0kwWldObU5EY3hOR1l3WW1NNFpUQTNNV0kyTkRBelpHUXpOR00wWkdSbE5qSmtPREZrWkRSaU9URmtNV0ZoTXpVMlpHVmxOZyIsImtpZCI6Ik16WXhNbUZrT0dZd01XSTBaV05tTkRjeE5HWXdZbU00WlRBM01XSTJOREF6WkdRek5HTTBaR1JsTmpKa09ERmtaRFJpT1RGa01XRmhNelUyWkdWbE5nX1JTMjU2IiwiYWxnIjoiUlMyNTYifQ.****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.G-LkoaWweSHVyUL3FQmqNQ2m3Lkzpz2nKdtMMmK2s-Ax5Tom3bpktq02KTcJfuEIZc4chqL1Ry7XvBAluUuhCwUa869LY4DXIKJqoWKqH7BA-OCHR9JorjezJ4luG91_EWxmaMWbwfR37UxkMeHvKYfT76FfkErT8SpY2zX84YEtw0sAu2cVIPrA3sF4-0h7_5-kkx9BHUge8__8xKVzvoeQbeHwLKEkC2gBcnCz8N8KlgtEqGPdSACB3T0QjHfal7l68mfpk0ggSjU36TlODPTwN0YPMovOp2Kp443Qw-HBzm3ykOr2WT6Sniyi937m5LT4JEJ0x9paS9mC_9FUAg"
  INTERNAL_SECRET_KEY: "dBuAn5TihyWqUuMSfOAEISUefyCz4NLjthwK2d8b2WM"
  INTERNAL_IDENTIFY_VERIFICATION_SECRET_KEY: "EQGAAIdqghDrqTp3agjzFrdcHkSq8djM6X32teZA71YAdHiCnbVA"
  FILES_CLIENT_INTEGRATION_KEY: "69fnbXBHh0lAQ0Xpl3mgoO4MBDcgkM6CO8HFIJt3SkQ"
  IMGPROXY_SERVICE_KEY: "e02bdc2fc7435b30eb4705cb8357de9e10e5e6e92966e9416c7332f33b1212406496c3a1cb6f56b5940c61041f45ea4de847cb854d1505b20a1e4cc42109f2ed"
  IMGPROXY_SERVICE_SALT: "f482be4fd72679f817c84f15dff92c7e29ad3c3e258556044f296394b04f74f813bb5146b64eb78aa09e362ea4839e05d354293a3ccd3ad12e49e5a301020079"

  DEACTIVATE_IDENTITY_VERIFICATION_SYNC_BEFORE_IN_HOUR: "720"
  MAX_HEADER_SIZE_IN_KB: "16"

  # VIN BIGDATA INTEGRATION
  ## INTEGRATION CONFIG
  VIN_BIGDATA_BASE_URL: "https://eyepass-api.vizone.ai/vhm"
  VIN_BIGDATA_APP_ID: "6adbe8ac-647c-466a-8ef8-c4626762bfcd"
  VIN_BIGDATA_APP_SECRET: "tXooSw8IYOa_jYxGitgo_JuScxQdFtOjwLZPID0P4_A="
  VIN_BIGDATA_ENCRYPT_KEY: "r13odkNpt6dX-G2CFeVFkjvrDFDUwOkO"

  ## DOCUMENT TYPE SUPPORTED: id_card (CCCD 2024), chip_based_id_card (CCCD Chip), white_id_card (CCCD), passport, legacy_id (CMND)
  VIN_BIGDATA_DOCUMENT_TYPE_SUPPORTED: "id_card, chip_based_id_card, white_id_card, passport"

  ## DOCUMENT VERIFY STEPS: scan_front_document, scan_back_document, scan_nfc, scan_face
  VIN_BIGDATA_ID_CARD_VERIFY_STEPS: "scan_front_document, scan_back_document, scan_face"
  VIN_BIGDATA_CHIP_BASED_ID_CARD_VERIFY_STEPS: "scan_front_document, scan_back_document, scan_face"
  VIN_BIGDATA_WHITE_ID_CARD_VERIFY_STEPS: "scan_front_document, scan_back_document, scan_face"
  VIN_BIGDATA_PASSPORT_VERIFY_STEPS: "scan_front_document, scan_face"
  VIN_BIGDATA_LEGACY_ID_VERIFY_STEPS: "scan_front_document, scan_back_document, scan_face"

  ## FACE FLOW CONFIG
  VIN_BIGDATA_USE_FACE_COMPARE: "true"
  VIN_BIGDATA_USE_FACE_LIVENESS: "true"

  ## ENABLE TEST SCENARIOS: verify_document_call_api_fail,verify_document_validate_fail, verify_document_compare_fail
  VIN_BIGDATA_ENABLE_TEST_SCENARIOS: ""

  # SHIELD SERVICE CONFIG
  SHIELD_ENABLE: "true"
  NO_APP_CHECK_LATEST_BUILD_NUMBER: "0"

  # USER REGISTRATION QUEUE CONFIG
  USER_REGISTRATION_QUEUE_ENABLE: "false"
  USER_REGISTRATION_QUEUE_BATCH_SIZE: "50"
  USER_REGISTRATION_QUEUE_INTERVAL_IN_SECOND: "5"


