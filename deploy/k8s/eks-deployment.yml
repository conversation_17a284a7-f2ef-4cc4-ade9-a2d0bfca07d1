apiVersion: apps/v1
kind: Deployment
metadata:
  name: vclub-customer-deployment
  namespace: vinclub-backend-stag
spec:
  replicas: 1
  progressDeadlineSeconds: 120
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 1        # how many pods we can add at a time
      maxUnavailable: 0  # maxUnavailable define how many pods can be unavailable # during the rolling update
  selector:
    matchLabels:
      app: vclub-customer
  template:
    metadata:
      labels:
        app: vclub-customer
    spec:
      affinity:
        nodeAffinity:
          requiredDuringSchedulingIgnoredDuringExecution:
            nodeSelectorTerms:
              - matchExpressions:
                  - key: sv-type
                    operator: In
                    values:
                      - on-demand
                      - spot
          preferredDuringSchedulingIgnoredDuringExecution:
            - weight: 1
              preference:
                matchExpressions:
                  - key: sv-type
                    operator: In
                    values:
                      - on-demand
      serviceAccountName: vhm-vinclub-customer-svc-sa
      containers:
        - name: vclub-customer
          image: ************.dkr.ecr.ap-southeast-1.amazonaws.com/vclub-customer-service:staging
          args: [ "startservice","eks-staging" ]
          resources:
            requests:
              memory: "400Mi"
            #              cpu: "400m"
            limits:
              memory: "1500Mi"
          #              cpu: "800m"
          lifecycle:
            preStop:
              exec:
                command: [ "/bin/sh", "-ec", "sleep 15" ]
          imagePullPolicy: Always
          ports:
            - name: http-port
              containerPort: 8080
          startupProbe:
            tcpSocket:
              port: http-port
            failureThreshold: 30
            periodSeconds: 20
          #          readinessProbe:
          #            tcpSocket:
          #              port: 8080
          #            initialDelaySeconds: 60
          #            periodSeconds: 10
          #            successThreshold: 1
          #            timeoutSeconds: 3
          #            failureThreshold: 3
          #          envFrom:
          #            - configMapRef:
          #                name: vclub-customer
          envFrom:
            - configMapRef:
                name: vclub-customer
          env:
            - name: TZ
              value: "Asia/Ho_Chi_Minh"
            - name: JVM_XMS
              value: "500M"
            - name: JVM_XMX
              value: "1300M"