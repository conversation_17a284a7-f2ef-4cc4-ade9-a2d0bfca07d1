include:
  - project: 'vinclub/devops/ci-templates'
    ref: master
    file: "vinclub-ci-template.yml"

image: $VINCLUB_AWS_STAG_ECR_REPO_URL/maven:3.6.0-jdk-8

cache:
  paths:
    - .m2/

variables:
  MAVEN_OPTS: "-Dmaven.repo.local=.m2 -Dorg.slf4j.simpleLogger.log.org.apache.maven.cli.transfer.Slf4jMavenTransferListener=WARN -Dorg.slf4j.simpleLogger.showDateTime=true -Djava.awt.headless=true"
  MAVEN_CLI_OPTS: "-Dmode=ci --batch-mode --errors --fail-at-end --show-version -DinstallAtEnd=true -DdeployAtEnd=true"
  mode: "ci"
  DOCKER_HOST: tcp://docker:2375
  DOCKER_DRIVER: overlay2
  DOCKER_TLS_CERTDIR: ""
  ECR_REPOSITORY_URL: 339712890740.dkr.ecr.ap-southeast-1.amazonaws.com
  ENV: "ci"

services:
  - name: docker:24-dind
    alias: docker

stages:
  - deploy_doc_and_test
  - docker
  - deploy

maven:package:
  stage: deploy_doc_and_test
  extends: .templated_maven_package
  tags:
    - vhm-vinclub-stag-gitlab-runner
  artifacts:
    paths:
      - dist

docker-build-image-stag:
  image: $VINCLUB_AWS_STAG_ECR_REPO_URL/docker:18-git
  stage: docker
  tags:
    - vhm-vinclub-stag-gitlab-runner
  extends: .templated_docker_build
  only:
    - master
    - staging

deploy-staging:
  image: $VINCLUB_AWS_STAG_ECR_REPO_URL/eks-deploy:latest
  stage: deploy
  tags:
    - vhm-vinclub-stag-gitlab-runner
  only:
    - staging
  extends: .templated_docker_deploy
  script:
    - prepare_eks
    - kubectl_aws_profile apply -f deploy/k8s/eks-deployment.yml
    - kubectl_aws_profile apply -f deploy/k8s_auth/eks-deployment.yml
    - kubectl_aws_profile apply -f deploy/k8s/service.yml
    - kubectl_aws_profile apply -f deploy/k8s_auth/service.yml
    - kubectl_aws_profile rollout restart -f deploy/k8s/eks-deployment.yml
    - kubectl_aws_profile rollout restart -f deploy/k8s_auth/eks-deployment.yml

docker-build-image-prod:
  image: $VINCLUB_AWS_STAG_ECR_REPO_URL/docker:18-git
  stage: docker
  tags:
    - vhm-vinclub-stag-gitlab-runner
  extends: .templated_docker_build
  only:
    - tags

