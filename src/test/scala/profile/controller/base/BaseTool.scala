package profile.controller.base

import com.fasterxml.jackson.databind.JsonNode
import com.twitter.finagle.http.Response
import com.twitter.util.{Await, Future}
import vn.vhm.common.util.{JsonHelper, Utils}

/**
 * <AUTHOR>
 */
trait BaseTool {

  implicit class ImplicitAny(value: Any) {
    def toJson(pretty: Boolean = false) = JsonHelper.toJson(value, pretty)

    def toJsonNode: JsonNode = JsonHelper.readTree(JsonHelper.toJson(value, false))
  }

  implicit class ImplicitString(value: String) {
    def asJsonNode = JsonHelper.readTree(value)
    def encodeURI = Utils.encodeURIComponent(value)

  }

  def sync[A](f: => Future[A]): A = {
    Await.result(f)
  }

  def printSync[A](f: => Future[A], json: Boolean = false, jsonPretty: Boolean = false): A = {
    val r = sync(f)
    json match {
      case true => println(JsonHelper.toJson(r, jsonPretty))
      case _ => println(r)
    }
    r
  }

  def printJson(json: String, pretty: Boolean = false) = {
    println(json)
    println(JsonHelper.toJson(JsonHelper.fromJson[Map[String, Any]](json), pretty))
  }

  def printResp(resp: Response, msg: String = null): Response = {

    import ResponseContext._

    if (msg == null) return resp
    Option(msg).filterNot(_.isEmpty).foreach(f => println(s"[$f] "))
    resp.requestData match {
      case Some(reqData) =>
        println(s"\tRequest:")
        println(s"                    ${reqData.path}")
        if (reqData.body.isDefined && reqData.body.get.nonEmpty) {
          println(s"                    ${JsonHelper.toJson(JsonHelper.readTree(reqData.body.get)).replaceAll("\n", "\n                    ")}")
        }
      case _ =>
    }
    println(s"\tHeader: ")
    println(s"\t        ${resp.headerMap.toJson()}")
    println(s"\tBody: ")
    println(s"                    ${resp.version.versionString} ${resp.statusCode} ${resp.status.reason}")
    println(s"                    ${JsonHelper.toJson(JsonHelper.readTree(resp.contentString)).replaceAll("\n", "\n                    ")}")
    resp
  }

  def debug(resp: Response, msg: String = null): Response = {
    printResp(resp, msg)
    if (resp.contentString.nonEmpty) {
      val jsonResult = resp.contentString.asJsonNode
      val isError = jsonResult.has("error")
      if (msg == null && isError) {
        printResp(resp, "")
      }
    }
    resp
  }

  def assetRespSuccess(resp: Response, msg: String = null, statusCode: Int = 200): Response = {
    printResp(resp, msg)
    assert(resp.statusCode == statusCode)
    if (resp.contentString.nonEmpty) {
      val jsonResult = resp.contentString.asJsonNode
      val isError = jsonResult.has("error")
      if (msg == null && isError) {
        printResp(resp, "")
      }
      assert(!isError)
    }
    resp
  }

  def assetRespFailed(resp: Response, msg: String = null, statusCode: Int = 200, error: String = null): Response = {
    printResp(resp, msg)
    assert(resp.statusCode == statusCode)
    val jsonResult = resp.contentString.asJsonNode
    val isError = jsonResult.has("error")
    assert(isError)
    if (error != null) {
      assert(error == jsonResult.path("error").asText())
    }
    resp
  }

  def tryTest(fn: => Unit)(fnFinally: => Unit = {}): Unit = {
    try {
      fn
    } finally {
      fnFinally
    }
  }

  def fnChain[A](fn: => A)(finallyFn: A => Unit): Unit = {
    var value: Option[A] = None
    try {
      value = Some(fn)
    } finally {
      value.foreach(finallyFn)
    }
  }

  def errAlone(fn: => Unit): Unit = {
    try {
      fn
    } catch {
      case e: Exception =>
    }
  }

  implicit class ImplResponse(response: Response) {
    def debug(msg: String = null, statusCode: Int = 200) = BaseTool.this.debug(response, msg)

    def assertSuccess(msg: String = null, statusCode: Int = 200) = BaseTool.this.assetRespSuccess(response, msg, statusCode)

    def assertFailed(msg: String = null, statusCode: Int = 400, error: String = null) = BaseTool.this.assetRespFailed(response, msg, statusCode, error)

    def asJsonNode = response.contentString.asJsonNode
  }

}
