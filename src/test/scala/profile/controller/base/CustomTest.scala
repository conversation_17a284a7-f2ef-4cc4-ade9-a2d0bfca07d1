package profile.controller.base

import com.google.inject.util.Modules
import com.google.inject.{<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>}
import com.twitter.inject.{Injector, Test}
import profile.module.DependencyModule

/**
 * <AUTHOR>
 */
trait CustomTest extends Test with DbTest {

  def overrideModule(modules: Module*): Module = {
    if (modules.size == 1) return modules.head

    var module = modules.head
    modules.tail.foreach(m => {
      module = Modules.`override`(module).`with`(m)
    })
    module
  }

  lazy val testInjector = Injector.apply(Guice.createInjector(overrideModule(DependencyModule, DependencyModuleTest)))

  protected override def beforeAll(): Unit = {
    initDB()
    super.beforeAll()
  }

  protected override def afterAll(): Unit = {
    super.afterAll()
    stopDB()
  }
}
