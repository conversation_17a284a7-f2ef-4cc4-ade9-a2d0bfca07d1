package profile.controller.base

import com.fasterxml.jackson.databind.JsonNode
import com.google.inject.util.Modules
import com.twitter.finagle.http.{Message, Response, Status}
import com.twitter.finatra.http.{EmbeddedHttpServer, RouteHint}
import com.twitter.inject.server.FeatureTest
import profile.Server
import vn.vhm.common.util.ZConfig

/**
 * <AUTHOR>
 */

class ServerTest extends Server {

  override def modules: Seq[com.google.inject.Module] = Seq(Modules.`override`(super.modules: _*).`with`(DependencyModuleTest))

}

trait CustomFeatureTest extends FeatureTest with DbTest {

  if (ZConfig.appMode.contains("production")) throw new Exception("WARNING: Test run with production mode will delete all data.")

  def defaultHeaders(): Map[String, String]

  override protected val server = new EmbeddedHttpServer(twitterServer = new ServerTest, disableTestLogging = true)
    //with com.twitter.finatra.thrift.ThriftClient
    {

    override def httpPost(
                           path: String,
                           postBody: String,
                           accept: String = null,
                           suppress: Boolean = false,
                           contentType: String = Message.ContentTypeJson,
                           headers: Map[String, String] = defaultHeaders,
                           andExpect: Status = null,
                           withLocation: String = null,
                           withBody: String = null,
                           withJsonBody: String = null,
                           withJsonBodyNormalizer: JsonNode => JsonNode = null,
                           withErrors: Seq[String] = null,
                           routeHint: RouteHint = null,
                           secure: Option[Boolean] = None
                         ): Response = {
      val r = super.httpPost(path, postBody, accept, suppress, contentType, headers, andExpect, withLocation, withBody, withJsonBody, withJsonBodyNormalizer, withErrors, routeHint, secure)
      ResponseContext.setRequestData(r, RequestData("POST " + path, Option(postBody)))
      resp = r
      resp
    }

    override def httpGet(
                          path: String,
                          accept: String = null,
                          headers: Map[String, String] = defaultHeaders,
                          suppress: Boolean = false,
                          andExpect: Status = null,
                          withLocation: String = null,
                          withBody: String = null,
                          withJsonBody: String = null,
                          withJsonBodyNormalizer: JsonNode => JsonNode = null,
                          withErrors: Seq[String] = null,
                          routeHint: RouteHint = null,
                          secure: Option[Boolean] = None
                        ) = {
      val r = super.httpGet(path, accept, headers, suppress, andExpect, withLocation, withBody, withJsonBody, withJsonBodyNormalizer, withErrors, routeHint, secure)
      ResponseContext.setRequestData(r, RequestData("GET " + path, None))
      resp = r
      resp
    }

    override def httpPut(
                          path: String,
                          putBody: String,
                          accept: String = null,
                          suppress: Boolean = false,
                          contentType: String = Message.ContentTypeJson,
                          headers: Map[String, String] = defaultHeaders,
                          andExpect: Status = null,
                          withLocation: String = null,
                          withBody: String = null,
                          withJsonBody: String = null,
                          withJsonBodyNormalizer: JsonNode => JsonNode = null,
                          withErrors: Seq[String] = null,
                          routeHint: RouteHint = null,
                          secure: Option[Boolean] = None
                        ) = {
      val r = super.httpPut(path, putBody, accept, suppress, contentType, headers, andExpect, withLocation, withBody, withJsonBody, withJsonBodyNormalizer, withErrors, routeHint, secure)
      ResponseContext.setRequestData(r, RequestData("PUT " + path, Option(putBody)))
      resp = r
      resp
    }

    override def httpDelete(path: String,
                            deleteBody: String = null,
                            accept: String = null,
                            suppress: Boolean = false,
                            contentType: String = Message.ContentTypeJson,
                            headers: Map[String, String] = defaultHeaders,
                            andExpect: Status = null,
                            withLocation: String = null,
                            withBody: String = null,
                            withJsonBody: String = null,
                            withJsonBodyNormalizer: JsonNode => JsonNode = null,
                            withErrors: Seq[String] = null,
                            routeHint: RouteHint = null,
                            secure: Option[Boolean] = None) = {
      val r = super.httpDelete(path, deleteBody, accept, suppress, contentType, headers, andExpect, withLocation, withBody, withJsonBody, withJsonBodyNormalizer, withErrors, routeHint, secure)
      ResponseContext.setRequestData(r, RequestData("DELETE " + path, Option(deleteBody)))
      resp = r
      resp
    }
  }

  //lazy val thriftClient = server.thriftClient[TUserProfileService.MethodPerEndpoint](clientId = "test")
  var resp: Response = _
  var jsonNode: JsonNode = _

  protected override def beforeAll(): Unit = {
    initDB()
    super.beforeAll()
  }

  protected override def afterAll(): Unit = {
    super.afterAll()
    stopDB()
    server.close()
  }
}

object ResponseContext {
  private val RequestField = Response.Schema.newField[RequestData]()

  implicit class ResponseContextSyntax(val response: Response) extends AnyVal {
    def requestData: Option[RequestData] = Option(response.ctx(RequestField))
  }

  def setRequestData(response: Response, data: RequestData): Unit = {
    response.ctx.update(RequestField, data)
  }
}

case class RequestData(path: String, body: Option[String] = None)