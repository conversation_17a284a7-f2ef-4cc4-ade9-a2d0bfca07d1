package profile.controller.base

import com.google.inject.name.Named
import com.google.inject.{Provides, Singleton}
import com.twitter.inject.TwitterModule
import com.twitter.util.Future
import com.typesafe.config.Config
import org.apache.kafka.clients.producer.RecordMetadata
import vn.vhm.common.client.kafka_010.StringKafkaProducer
import vn.vhm.common.util.ZConfig

import java.sql.ResultSet


/**
 * <AUTHOR>
 */
object DependencyModuleTest extends TwitterModule {

  implicit class ResultSetImplicitTest(rs: ResultSet) {
    def optString(field: String): Option[String] = {
      if (rs.getObject(field) != null) {
        Option(rs.getString(field))
      } else None
    }
  }

  @Singleton
  @Provides
  def providesStringKafkaProducer(): StringKafkaProducer = {
    newForTestKafkaProducer(ZConfig.getConf("kafka.producer"))
  }


//  @Singleton
//  @Provides
//  def providesCaptchaService(
//                              externalCaptchaService: ExternalCaptchaService,
//                              @Named("common") commonSsdbClient: SSDBClient
//                            ): CaptchaService = {
//    new CaptchaServiceImpl(
//      externalCaptchaService, commonSsdbClient
//    ) {
//      override def verifyCaptchaWithCache(tokenCaptcha: String, captchaVersion: Option[String], numberOfTimes: Option[Int], cacheCaptchaTtlInSecond: Option[Int]): Future[Boolean] = {
//        Future.True
//      }
//    }
//  }

  @Singleton
  @Provides
  @Named("notification")
  def providesNotificationKafka(): StringKafkaProducer = {
    newForTestKafkaProducer(ZConfig.getConf("kafka_delivery.producer"))
  }

  private def newForTestKafkaProducer(config: Config): StringKafkaProducer = {
    new StringKafkaProducer(config) {
      override def sendOnlyValue(topic: String, value: String) = {
        Future.value(new RecordMetadata(null, 0L, 0L, 0L, 0L, 0, 0))
      }

      override def send(topic: String, key: String, value: String, partition: Integer, timestamp: java.lang.Long) = {
        Future.value(new RecordMetadata(null, 0L, 0L, 0L, 0L, 0, 0))
      }

      override def sendSync(topic: String, key: String, value: String, partition: Integer, timestamp: java.lang.Long) = {
        new RecordMetadata(null, 0L, 0L, 0L, 0L, 0, 0)
      }
    }
  }

}
