package profile.controller.base

import org.apache.commons.codec.CharEncoding
import org.apache.commons.io.IOUtils
import profile.module.DependencyModule
import vn.vhm.common.util.{Utils, ZConfig}

/**
 * <AUTHOR>
 */

trait DbTest {

  val testModes = Seq("ci", "test", "development", "local")
  if (ZConfig.appMode.contains("production")) throw new Exception("WARNING: Test run with production mode will delete all data.")

  def initSqlFiles: Seq[String] = Nil

  def initDB(): Unit = {

    println("STARTING PREPARE DB...")
    Thread.sleep(10000L)
    try {
      DependencyModule.initDB(
        initSqlFiles.map(file => IOUtils.toString(getClass.getClassLoader.getResource(file), CharEncoding.UTF_8))
      )
      println("START PREPARE DB SUCCESSFUL")
    } catch {
      case e: Exception =>
        println(s"START PREPARE DB ERROR: ${Utils.getStackTraceAsString(e)}")
    }
  }

  def stopDB(): Unit = {

  }

}
