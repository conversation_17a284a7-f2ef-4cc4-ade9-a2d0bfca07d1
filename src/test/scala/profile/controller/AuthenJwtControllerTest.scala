package profile.controller

import profile.controller.base.{BaseTool, CustomFeatureTest}
import profile.domain.customer.CustomerDAO
import vn.vhm.common.util.JsonHelper

/**
 * <AUTHOR> 8/27/20 11:02 AM
 */
class AuthenJwtControllerTest extends CustomFeatureTest with BaseTool {

  override def defaultHeaders(): Map[String, String] = Map.empty[String, String]

  override protected def beforeEach(): Unit = {

    super.beforeEach()
    //ssdbClient.exec(v => v.flushdb("kv"))
    val userDAO = injector.instance[CustomerDAO]
    userDAO.execute(userDAO.executeUpdate("DELETE FROM customers")(_))

  }

  //  val oauthAP = ("apple", Map("oauth_id" -> "sonpn_test", "oauth_token" -> "sonpn_test_code"))
  //  val oauthGG = ("google", Map("oauth_id" -> "sonpn_test_gg", "oauth_token" -> "sonpn_test_gg_code"))
  //  val oauthFB = ("facebook", Map("oauth_id" -> "sonpn_test_fb", "oauth_token" -> "sonpn_test_fb_code"))
  //  val phone1 = "0766773414"
  //  val phone2 = "0766773415"
  //  val oauthAP1 = ("apple", Map("oauth_id" -> "sonpn_test_1", "oauth_token" -> "sonpn_test_1_code"))

  test("simple register") {

    println("start test simple register # 1")

    jsonNode = server.httpPost(s"/user/register", postBody = JsonHelper.toJson(Map(
        "identify" -> "0766773414"
      )))
      .assertSuccess().asJsonNode

    assert(jsonNode.at("/otp").asBoolean())

    println("start test simple register # 2")

    jsonNode = server.httpPost(s"/user/register/verify", postBody = JsonHelper.toJson(Map(
        "identify" -> "0766773414",
        "code" -> "123123"
      )))
      .assertSuccess().asJsonNode

    println("start test simple register # 3")

    jsonNode = server.httpPost(s"/user/register/confirm", postBody = JsonHelper.toJson(Map(
        "identify" -> "0766773414",
        "device_id" -> "123456",
        "token" -> jsonNode.path("token"),
        "password" -> commonPassword,
        "display_name" -> "phong pham qui"
      )))
      .assertSuccess("").asJsonNode

    println("start test simple register # 4")

    jsonNode = server.httpPost(s"/user/login", postBody = JsonHelper.toJson(Map(
        "identify" -> "0766773111",
        "device_id" -> "123456",
        "password" -> commonPassword,
      )))
      .assertFailed().asJsonNode

    println("start test simple register # 5")

    jsonNode = server.httpPost(s"/user/login", postBody = JsonHelper.toJson(Map(
        "identify" -> "0766773414",
        "device_id" -> "123456",
        "password" -> "111111",
      )))
      .assertFailed().asJsonNode

    println("end test simple register")
  }

  test("register with email") {

    println("start test register with email")

    jsonNode = server.httpPost(s"/user/register", postBody = JsonHelper.toJson(Map(
        "identify" -> "<EMAIL>"
      )))
      .assertSuccess().asJsonNode

    assert(jsonNode.at("/otp").asBoolean())

    jsonNode = server.httpPost(s"/user/register/verify", postBody = JsonHelper.toJson(Map(
        "identify" -> "<EMAIL>",
        "code" -> "123123"
      )))
      .assertSuccess().asJsonNode

    println("jsonNode " + jsonNode)

    jsonNode = server.httpPost(s"/user/register/confirm", postBody = JsonHelper.toJson(Map(
        "identify" -> "<EMAIL>",
        "device_id" -> "123456",
        "token" -> jsonNode.path("token"),
        "password" -> commonPassword,
        "display_name" -> "phong pham qui"
      )))
      .assertSuccess().asJsonNode
    println(jsonNode)

    jsonNode = server.httpPost(s"/user/register", postBody = JsonHelper.toJson(Map(
        "identify" -> "<EMAIL>",
        "device_id" -> "123456"
      )))
      .assertFailed().asJsonNode

    //    jsonNode = server.httpPost(s"/user/register", postBody = JsonHelper.toJson(Map(
    //        "identify" -> "<EMAIL>",
    //        "device_id" -> "123457"
    //      )))
    //      .assertSuccess().asJsonNode
    //
    //    jsonNode = server.httpPost(s"/user/register/verify", postBody = JsonHelper.toJson(Map(
    //        "identify" -> "<EMAIL>",
    //        "device_id" -> "123457",
    //        "code" -> "123123"
    //      )))
    //      .assertSuccess().asJsonNode
  }

  test("register by phone # 1") {
    jsonNode = server.httpPost(s"/user/register", postBody = JsonHelper.toJson(Map(
        "identify" -> "0766773414"
      )))
      .assertSuccess().asJsonNode

    assert(jsonNode.at("/otp").asBoolean())

    jsonNode = server.httpPost(s"/user/register/verify", postBody = JsonHelper.toJson(Map(
        "identify" -> "0766773414",
        "code" -> "123123"
      )))
      .assertSuccess().asJsonNode

    println("jsonNode " + jsonNode)

    jsonNode = server.httpPost(s"/user/register/confirm", postBody = JsonHelper.toJson(Map(
        "identify" -> "0766773414",
        "device_id" -> "123456",
        "token" -> jsonNode.path("token"),
        "password" -> commonPassword,
        "display_name" -> "phong pham qui"
      )))
      .assertSuccess().asJsonNode

    jsonNode = server.httpPost(s"/user/login", postBody = JsonHelper.toJson(Map(
        "identify" -> "0766773111",
        "device_id" -> "123456",
        "password" -> commonPassword,
      )))
      .assertFailed().asJsonNode

    jsonNode = server.httpPost(s"/user/login", postBody = JsonHelper.toJson(Map(
        "identify" -> "0766773414",
        "device_id" -> "123456",
        "password" -> "111111",
      )))
      .assertFailed().asJsonNode

    jsonNode = server.httpPost(s"/user/login", postBody = JsonHelper.toJson(Map(
        "identify" -> "0766773414",
        "device_id" -> "123456",
        "password" -> commonPassword,
      )))
      .assertSuccess().asJsonNode

    assert(jsonNode.at("/user_info").size() > 0)

    assert(jsonNode.at("/user_profile").size() > 0)

    val accessToken = jsonNode.at("/jwt/token").asText()
    val refreshToken = jsonNode.at("/jwt/refresh_token").asText()

    jsonNode = server.httpGet(s"/user/profile",
      headers = Map("Authorization" -> s"Bearer $accessToken")
    ).assertSuccess().asJsonNode

    println(jsonNode)
  }

  test("login - exceed wrong pass") {
    jsonNode = server.httpPost(s"/user/register", postBody = JsonHelper.toJson(Map(
        "identify" -> "0358984752"
      )))
      .assertSuccess().asJsonNode

    assert(jsonNode.at("/otp").asBoolean())

    jsonNode = server.httpPost(s"/user/register/verify", postBody = JsonHelper.toJson(Map(
        "identify" -> "0358984752",
        "code" -> "123123"
      )))
      .assertSuccess().asJsonNode

    println("jsonNode " + jsonNode)

    jsonNode = server.httpPost(s"/user/register/confirm", postBody = JsonHelper.toJson(Map(
        "identify" -> "0358984752",
        "device_id" -> "123456",
        "token" -> jsonNode.path("token"),
        "password" -> commonPassword,
        "display_name" -> "phong pham qui"
      )))
      .assertSuccess().asJsonNode

    jsonNode = server.httpPost(s"/user/login", postBody = JsonHelper.toJson(Map(
        "identify" -> "0358984752",
        "device_id" -> "123456",
        "password" -> "wrong pass",
      )))
      .assertFailed().asJsonNode

    jsonNode = server.httpPost(s"/user/login", postBody = JsonHelper.toJson(Map(
        "identify" -> "0358984752",
        "device_id" -> "123456",
        "password" -> "wrong pass",
      )))
      .assertFailed().asJsonNode

    //user locked
    jsonNode = server.httpPost(s"/user/login", postBody = JsonHelper.toJson(Map(
        "identify" -> "0358984752",
        "device_id" -> "123456",
        "password" -> "wrong pass",
      )))
      .assertFailed().asJsonNode

    println(jsonNode)
    assert(jsonNode.at("/data/exceed_login_wrong_pass_quota").asBoolean())
  }

  test("login 01") {
    jsonNode = server.httpPost(s"/user/register", postBody = JsonHelper.toJson(Map(
        "identify" -> "0358984752"
      )))
      .assertSuccess().asJsonNode

    assert(jsonNode.at("/otp").asBoolean())

    jsonNode = server.httpPost(s"/user/register/verify", postBody = JsonHelper.toJson(Map(
        "identify" -> "0358984752",
        "code" -> "123123"
      )))
      .assertSuccess().asJsonNode

    println("jsonNode " + jsonNode)

    jsonNode = server.httpPost(s"/user/register/confirm", postBody = JsonHelper.toJson(Map(
        "identify" -> "0358984752",
        "device_id" -> "123456",
        "token" -> jsonNode.path("token"),
        "password" -> commonPassword,
        "display_name" -> "phong pham qui"
      )))
      .assertSuccess().asJsonNode

    jsonNode = server.httpPost(s"/user/login", postBody = JsonHelper.toJson(Map(
        "identify" -> "0358984752",
        "device_id" -> "123457",
        "password" -> commonPassword,
      )))
      .assertFailed().asJsonNode

    jsonNode = server.httpPost(s"/user/login", postBody = JsonHelper.toJson(Map(
        "identify" -> "0358984752",
        "device_id" -> "123457",
        "code" -> "312511",
        "password" -> commonPassword,
      )))
      .assertFailed().asJsonNode
    jsonNode = server.httpPost(s"/user/login", postBody = JsonHelper.toJson(Map(
        "identify" -> "0358984752",
        "device_id" -> "123457",
        "code" -> "312511",
        "password" -> commonPassword,
      )))
      .assertFailed().asJsonNode
    jsonNode = server.httpPost(s"/user/login", postBody = JsonHelper.toJson(Map(
        "identify" -> "0358984752",
        "device_id" -> "123457",
        "code" -> "312511",
        "password" -> commonPassword,
      )))
      .assertFailed().asJsonNode
    jsonNode = server.httpPost(s"/user/login", postBody = JsonHelper.toJson(Map(
        "identify" -> "0358984752",
        "device_id" -> "123457",
        "code" -> "312511",
        "password" -> commonPassword,
      )))
      .assertFailed().asJsonNode

    jsonNode = server.httpPost(s"/user/login", postBody = JsonHelper.toJson(Map(
        "identify" -> "0358984752",
        "device_id" -> "123457",
        "code" -> "123123",
        "password" -> commonPassword,
        "token_captcha" -> "ok"
      )))
      .assertSuccess().asJsonNode
  }

  test("logout") {
    jsonNode = server.httpPost(s"/user/register", postBody = JsonHelper.toJson(Map(
        "identify" -> "0358984752"
      )))
      .assertSuccess().asJsonNode

    assert(jsonNode.at("/otp").asBoolean())

    jsonNode = server.httpPost(s"/user/register/verify", postBody = JsonHelper.toJson(Map(
        "identify" -> "0358984752",
        "code" -> "123123"
      )))
      .assertSuccess().asJsonNode

    println("jsonNode " + jsonNode)

    jsonNode = server.httpPost(s"/user/register/confirm", postBody = JsonHelper.toJson(Map(
        "identify" -> "0358984752",
        "device_id" -> "123456",
        "token" -> jsonNode.path("token"),
        "password" -> commonPassword,
        "display_name" -> "phong pham qui"
      )))
      .assertSuccess().asJsonNode

    val accessToken = jsonNode.at("/jwt/token").asText()
    val refreshToken = jsonNode.at("/jwt/refresh_token").asText()

    jsonNode = server.httpPost(s"/user/logout", postBody = JsonHelper.toJson(Map(
        "refresh_token" -> accessToken
      )))
      .assertSuccess().asJsonNode

    jsonNode = server.httpPost(s"/user/logout", postBody = JsonHelper.toJson(Map(
        "refresh_token" -> refreshToken
      )))
      .assertSuccess().asJsonNode
  }

  test("refresh token #1") {
    jsonNode = server.httpPost(s"/user/register", postBody = JsonHelper.toJson(Map(
        "identify" -> "0766773414"
      )))
      .assertSuccess().asJsonNode

    assert(jsonNode.at("/otp").asBoolean())

    jsonNode = server.httpPost(s"/user/register/verify", postBody = JsonHelper.toJson(Map(
        "identify" -> "0766773414",
        "code" -> "123123",
        "password" -> commonPassword,
        "first_name" -> "phong"
      )))
      .assertSuccess().asJsonNode

    jsonNode = server.httpPost(s"/user/register/confirm", postBody = JsonHelper.toJson(Map(
        "identify" -> "0766773414",
        "token" -> jsonNode.at("/token").asText(),
        "device_id" -> commonDeviceId,
        "password" -> commonPassword,
        "first_name" -> "phong"
      )))
      .assertSuccess().asJsonNode

    val accessToken = jsonNode.at("/jwt/token").asText()
    val refreshToken = jsonNode.at("/jwt/refresh_token").asText()

    jsonNode = server.httpPost(s"/user/refresh-token", postBody = JsonHelper.toJson(Map(
        "refresh_token" -> accessToken
      )))
      .assertFailed().asJsonNode

    jsonNode = server.httpPost(s"/user/refresh-token", postBody = JsonHelper.toJson(Map(
        "refresh_token" -> refreshToken
      )))
      .assertSuccess().asJsonNode
  }

  test("logout after refresh token #2") {

    jsonNode = server.httpPost(s"/user/register", postBody = JsonHelper.toJson(Map(
        "identify" -> "0766773414"
      )))
      .assertSuccess().asJsonNode

    assert(jsonNode.at("/otp").asBoolean())

    jsonNode = server.httpPost(s"/user/register/verify", postBody = JsonHelper.toJson(Map(
        "identify" -> "0766773414",
        "code" -> "123123",
        "password" -> commonPassword,
        "first_name" -> "phong"
      )))
      .assertSuccess().asJsonNode

    jsonNode = server.httpPost(s"/user/register/confirm", postBody = JsonHelper.toJson(Map(
        "identify" -> "0766773414",
        "token" -> jsonNode.at("/token").asText(),
        "device_id" -> commonDeviceId,
        "password" -> commonPassword,
        "first_name" -> "phong"
      )))
      .assertSuccess().asJsonNode


    val accessToken = jsonNode.at("/jwt/token").asText()
    val refreshToken = jsonNode.at("/jwt/refresh_token").asText()

    jsonNode = server.httpPost(s"/user/refresh-token", postBody = JsonHelper.toJson(Map(
        "refresh_token" -> refreshToken
      )))
      .assertSuccess().asJsonNode

    jsonNode = server.httpPost(s"/user/logout", postBody = JsonHelper.toJson(Map(
        "refresh_token" -> refreshToken
      )))
      .assertSuccess().asJsonNode

    jsonNode = server.httpPost(s"/user/refresh-token", postBody = JsonHelper.toJson(Map(
        "refresh_token" -> refreshToken
      )))
      .assertFailed().asJsonNode
  }

  test("reset pass #1") {
    jsonNode = server.httpPost(s"/user/register", postBody = JsonHelper.toJson(Map(
        "identify" -> "0766773414"
      )))
      .assertSuccess("").asJsonNode

    assert(jsonNode.at("/otp").asBoolean())

    jsonNode = server.httpPost(s"/user/register/verify", postBody = JsonHelper.toJson(Map(
        "identify" -> "0766773414",
        "code" -> "123123"
      )))
      .assertSuccess().asJsonNode

    println("jsonNode " + jsonNode)

    jsonNode = server.httpPost(s"/user/register/confirm", postBody = JsonHelper.toJson(Map(
        "identify" -> "0766773414",
        "device_id" -> "123456",
        "token" -> jsonNode.path("token"),
        "password" -> commonPassword,
        "full_name" -> "phong pham qui"
      )))
      .assertSuccess().asJsonNode

    jsonNode = server.httpPut(s"/user/reset-password", putBody = JsonHelper.toJson(Map(
        "identify" -> "0766773414"
      )))
      .assertSuccess("").asJsonNode

    Thread.sleep(2000L)

    jsonNode = server.httpPut(
        s"/user/reset-password/verify",
        putBody = JsonHelper.toJson(Map(
          "identify" -> "0766773414",
          "code" -> "123123"
        ))
      )
      .assertSuccess().asJsonNode

    println(jsonNode)

    val tokenPhone = jsonNode.path("token").asText("")

    jsonNode = server.httpPut(
        s"/user/reset-password/confirm",
        putBody = JsonHelper.toJson(Map(
          "identify" -> "0766773414",
          "token" -> tokenPhone,
          "new_password" -> "321321",
          "device_id" -> "123321"
        ))
      )
      .assertSuccess().asJsonNode

    jsonNode = server.httpPost(s"/user/login", postBody = JsonHelper.toJson(Map(
        "identify" -> "0766773414",
        "device_id" -> "123321",
        "password" -> "123578",
      )))
      .assertFailed().asJsonNode

    jsonNode = server.httpPost(s"/user/login", postBody = JsonHelper.toJson(Map(
        "identify" -> "0766773414",
        "device_id" -> "123321",
        "password" -> "321321",
      )))
      .assertSuccess().asJsonNode
  }

  test("reset pass #2") {
    jsonNode = server.httpPost(s"/user/register", postBody = JsonHelper.toJson(Map(
        "identify" -> "0766773414"
      )))
      .assertSuccess().asJsonNode

    assert(jsonNode.at("/otp").asBoolean())

    jsonNode = server.httpPost(s"/user/register/verify", postBody = JsonHelper.toJson(Map(
        "identify" -> "0766773414",
        "code" -> "123123"
      )))
      .assertSuccess().asJsonNode

    println("jsonNode " + jsonNode)

    jsonNode = server.httpPost(s"/user/register/confirm", postBody = JsonHelper.toJson(Map(
        "identify" -> "0766773414",
        "device_id" -> "123456",
        "token" -> jsonNode.path("token"),
        "password" -> commonPassword,
        "display_name" -> "phong pham qui"
      )))
      .assertSuccess().asJsonNode

    jsonNode = server.httpPut(s"/user/reset-password", putBody = JsonHelper.toJson(Map(
        "identify" -> "0766773414"
      )))
      .assertSuccess().asJsonNode

    Thread.sleep(2000L)

    jsonNode = server.httpPut(
        s"/user/reset-password/verify",
        putBody = JsonHelper.toJson(Map(
          "identify" -> "0766773414",
          "code" -> "123123"
        ))
      )
      .assertSuccess().asJsonNode

    println(jsonNode)

    val tokenPhone = jsonNode.path("token").asText("")

    jsonNode = server.httpPut(
        s"/user/reset-password/confirm",
        putBody = JsonHelper.toJson(Map(
          "identify" -> "0766773414",
          "token" -> tokenPhone,
          "new_password" -> "321321",
          "device_id" -> "123321"
        ))
      )
      .assertSuccess().asJsonNode

    jsonNode = server.httpPost(s"/user/login", postBody = JsonHelper.toJson(Map(
        "identify" -> "0766773414",
        "device_id" -> "123321",
        "password" -> "123578",
      )))
      .assertFailed().asJsonNode

    jsonNode = server.httpPost(s"/user/login", postBody = JsonHelper.toJson(Map(
        "identify" -> "0766773414",
        "device_id" -> "123321",
        "password" -> "321321",
      )))
      .assertSuccess().asJsonNode
  }

}
