package profile.controller

import profile.controller.base.{BaseTool, CustomFeatureTest}
import profile.domain.customer.CustomerDAO
import vn.vhm.common.util.JsonHelper

class UserControllerTest extends CustomFeatureTest with BaseTool {

  override def defaultHeaders(): Map[String, String] = Map.empty[String, String]

  override protected def beforeEach(): Unit = {
    super.beforeEach()

    val userDAO = injector.instance[CustomerDAO]
    userDAO.execute(userDAO.executeUpdate("DELETE FROM customers")(_))
  }

  test("get user profile") {
    jsonNode = server.httpPost(s"/user/register", postBody = JsonHelper.toJson(Map(
        "identify" -> "0358984752"
      )))
      .assertSuccess().asJsonNode

    assert(jsonNode.at("/otp").asBoolean())

    jsonNode = server.httpPost(s"/user/register/verify", postBody = JsonHelper.toJson(Map(
        "identify" -> "0358984752",
        "code" -> "123123"
      )))
      .assertSuccess().asJsonNode

    println("jsonNode " + jsonNode)

    jsonNode = server.httpPost(s"/user/register/confirm", postBody = JsonHelper.toJson(Map(
        "identify" -> "0358984752",
        "device_id" -> "123456",
        "token" -> jsonNode.path("token"),
        "password" -> commonPassword,
        "display_name" -> "phong pham qui"
      )))
      .assertSuccess().asJsonNode
    println(jsonNode)

    val accessToken = jsonNode.at("/jwt/token").asText()
    val refreshToken = jsonNode.at("/jwt/refresh_token").asText()

    jsonNode = server.httpGet(s"/user/profile",
      headers = Map("Authorization" -> s"Bearer $accessToken")
    ).assertSuccess().asJsonNode

    println(jsonNode)
  }

  test("update user profile") {
    jsonNode = server.httpPost(s"/user/register", postBody = JsonHelper.toJson(Map(
        "identify" -> "0766773414"
      )))
      .assertSuccess().asJsonNode

    assert(jsonNode.at("/otp").asBoolean())

    jsonNode = server.httpPost(s"/user/register/verify", postBody = JsonHelper.toJson(Map(
        "identify" -> "0766773414",
        "code" -> "123123"
      )))
      .assertSuccess().asJsonNode

    println("jsonNode " + jsonNode)

    jsonNode = server.httpPost(s"/user/register/confirm", postBody = JsonHelper.toJson(Map(
        "identify" -> "0766773414",
        "device_id" -> "123456",
        "token" -> jsonNode.path("token"),
        "password" -> commonPassword,
        "full_name" -> "phong pham qui"
      )))
      .assertSuccess().asJsonNode

    val accessToken = jsonNode.at("/jwt/token").asText()
    val refreshToken = jsonNode.at("/jwt/refresh_token").asText()

    val body = Map(
      "full_name" -> "phong pham qui",
      "first_name" -> "phong",
      "gender" -> "male",
      "birthday" -> "2024-01-01"
      //"birthday" -> "2024/01/01"
    )

    jsonNode = server.httpPut(s"/user/update",
      headers = Map("Authorization" -> s"Bearer $accessToken"),
      putBody = JsonHelper.toJson(body)
    ).assertSuccess().asJsonNode


    jsonNode = server.httpGet(s"/user/profile",
      headers = Map("Authorization" -> s"Bearer $accessToken")
    ).assertSuccess().asJsonNode

    println(jsonNode)
  }

  test("update email") {
    jsonNode = server.httpPost(s"/user/register", postBody = JsonHelper.toJson(Map(
        "identify" -> "0766773414"
      )))
      .assertSuccess().asJsonNode

    assert(jsonNode.at("/otp").asBoolean())

    jsonNode = server.httpPost(s"/user/register/verify", postBody = JsonHelper.toJson(Map(
        "identify" -> "0766773414",
        "code" -> "123123"
      )))
      .assertSuccess().asJsonNode

    println("jsonNode " + jsonNode)

    jsonNode = server.httpPost(s"/user/register/confirm", postBody = JsonHelper.toJson(Map(
        "identify" -> "0766773414",
        "device_id" -> "123456",
        "token" -> jsonNode.path("token"),
        "password" -> commonPassword,
        "full_name" -> "phong pham qui"
      )))
      .assertSuccess().asJsonNode

    val accessToken = jsonNode.at("/jwt/token").asText()
    val refreshToken = jsonNode.at("/jwt/refresh_token").asText()

    jsonNode = server.httpPost(s"/user/update-email", postBody = JsonHelper.toJson(Map(
        "email" -> "<EMAIL>",
      )),
        headers = Map("Authorization" -> s"Bearer $accessToken"))
      .assertSuccess().asJsonNode

    jsonNode = server.httpPost(s"/user/update-email/verify", postBody = JsonHelper.toJson(Map(
        "email" -> "<EMAIL>",
        "code" -> "123123"
      )),
        headers = Map("Authorization" -> s"Bearer $accessToken"))
      .assertSuccess().asJsonNode
  }

  test("change pass") {
    jsonNode = server.httpPost(s"/user/register", postBody = JsonHelper.toJson(Map(
        "identify" -> "0766773414"
      )))
      .assertSuccess().asJsonNode

    assert(jsonNode.at("/otp").asBoolean())

    jsonNode = server.httpPost(s"/user/register/verify", postBody = JsonHelper.toJson(Map(
        "identify" -> "0766773414",
        "code" -> "123123"
      )))
      .assertSuccess().asJsonNode

    println("jsonNode " + jsonNode)

    jsonNode = server.httpPost(s"/user/register/confirm", postBody = JsonHelper.toJson(Map(
        "identify" -> "0766773414",
        "device_id" -> "123456",
        "token" -> jsonNode.path("token"),
        "password" -> commonPassword,
        "full_name" -> "phong pham qui"
      )))
      .assertSuccess().asJsonNode

    val accessToken = jsonNode.at("/jwt/token").asText()
    val refreshToken = jsonNode.at("/jwt/refresh_token").asText()

    jsonNode = server.httpPut(s"/user/change-password", putBody = JsonHelper.toJson(Map(
        "old_password" -> commonPassword,
        "new_password" -> "123123"
      )),
        headers = Map("Authorization" -> s"Bearer $accessToken"))
      .assertSuccess().asJsonNode
  }
}
