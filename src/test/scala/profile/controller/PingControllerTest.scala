package profile.controller

import com.twitter.finatra.http.EmbeddedHttpServer
import com.twitter.inject.server.FeatureTest
import profile.Server

/**
 * <AUTHOR> 7/23/21 5:17 PM
 */
class PingControllerTest extends FeatureTest {

  override protected val server = new EmbeddedHttpServer(twitterServer = new Server, disableTestLogging = true)

  test("ping") {
    val resp = server.httpGet("/profiler")
    println(resp.status)
    println(resp.contentString)
  }

}
