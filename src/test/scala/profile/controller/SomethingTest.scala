package profile.controller

import profile.util.{CTimeUtils, CustomUtils}
import vn.vhm.common.util.{JsonHelper, PhoneUtils, ZConfig}

import java.util.UUID
import scala.concurrent.duration.DurationInt

object SomethingTest {
  private val whitePhoneList = ZConfig.getStringList("sms.white_phone_list", Nil)
  def main(args: Array[String]): Unit = {

    println(CTimeUtils.dailyDiff(2, 0))

    println(CTimeUtils.getValidDate("12-12-2022"))

    println(ZConfig.getString("verify_phonenumber_service.message_template_for_ios").replaceAll("\\$code", "123"))
    println(60.second)
    println(whitePhoneList.isEmpty)
    val a =
      """
        |{
        |"a" : "b",
        |"v" : 1
        |}
        |""".stripMargin
    val tree = JsonHelper.readTree(a)
    val v = JsonHelper.fromNode[Map[String, Any]](tree).map(v => v._1 -> v._2.toString)
    println(v)
    println(Option(tree.get("c").asText()))

    //    println(UUID.randomUUID())
    //    println("xIYt5L6Lbb87z2HIlMVukqDTOdaeHx937EWphBMhdNY=".length)
    //    val regexPass = "^\\d{6}$".r.pattern
    //    println(regexPass.matcher("214612"))
    //    println(PhoneUtils.normalizePhone("0358984752"))
    println(CustomUtils.isEmail("!phongpq!@gmail.com"))
    println(CustomUtils.isEmail("<EMAIL>"))
  }
}
