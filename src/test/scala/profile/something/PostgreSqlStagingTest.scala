package profile.something

import profile.domain.customer.CustomerDAO
import profile.module.DependencyModule
import vn.vhm.common.util.JsonHelper
import vn.vhm.jdbc.util.Utils

/**
 * <AUTHOR> 8/9/24 12:17
 */
object PostgreSqlStagingTest {

  System.setProperty("mode", "staging-local")

  def main(args: Array[String]): Unit = {


    Utils.using(DependencyModule.getPostgreDataSource()) { ds =>

      val customerDAO = CustomerDAO(ds)

      println(JsonHelper.toJson(customerDAO.select("18171658484654080"), true))

    }
  }
}
