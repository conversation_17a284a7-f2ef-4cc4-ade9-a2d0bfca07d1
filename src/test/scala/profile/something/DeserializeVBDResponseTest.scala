package profile.something

import profile.domain.entity.Tier
import profile.service.VBDDocumentInfo
import vn.vhm.common.util.JsonHelper

/**
 * <AUTHOR> 8/23/24 13:28
 */
object DeserializeVBDResponseTest {
  def main(args: Array[String]): Unit = {

    val responseNode = JsonHelper.readTree(
      """{
        |  "data": {
        |    "document_id": "6afe3f1a-a02b-47ad-bf0f-b1c10c32b284",
        |    "face_id": "892c2f16-d2a7-4416-9efb-7fbc69c3af62",
        |    "validation_info": {
        |      "face": {
        |        "liveness_data": {
        |          "mode": 1,
        |          "num_face_detected": 1,
        |          "face_ratio": 0.3132491282927684,
        |          "is_liveness": true,
        |          "is_same_person": true,
        |          "is_bad_quality": false
        |        },
        |        "compare_data": {
        |          "person_card_validation": {
        |            "id_validation": "passed",
        |            "similarity_percentage": 100,
        |            "is_same_person": true
        |          },
        |          "age_gender_validation": {
        |            "input_gender": "male",
        |            "input_age": 25,
        |            "exact_age": 30
        |          }
        |        },
        |        "selfie_image": "https://eyepass-storage.vizone.ai/eyepass-demo/face_selfie/2025/03/14/09_36_52_649896389.jpg?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=eyepass%2F20250314%2Fus-east-1%2Fs3%2Faws4_request&X-Amz-Date=20250314T025421Z&X-Amz-Expires=300&X-Amz-SignedHeaders=host&X-Amz-Signature=7ecea0fb080f7c2c8f2d79a4af2592ae3d11091b1ea141f850d233d45a917cab",
        |        "face_to_compare_image": "https://eyepass-storage.vizone.ai/eyepass-demo/avatar/2025/03/14/09_35_39_159108794.jpg?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=eyepass%2F20250314%2Fus-east-1%2Fs3%2Faws4_request&X-Amz-Date=20250314T025421Z&X-Amz-Expires=300&X-Amz-SignedHeaders=host&X-Amz-Signature=a345f7ae7352e640521acd64bfb26bc195399b95794e3a84685640c06bb20624"
        |      },
        |      "ocr": {
        |        "address": {
        |          "value": "Thôn khi Gio Mỹ, Gio Linh, Quảng"
        |        },
        |        "poi": {
        |          "value": "BỘ CÔNG AN"
        |        },
        |        "nationality": {
        |          "value": "Vietnamese"
        |        },
        |        "id_number": {
        |          "value": "045200008027"
        |        },
        |        "name": {
        |          "value": "LÊ HỮU VÕ LONG"
        |        },
        |        "dob": {
        |          "value": "14/02/2000"
        |        },
        |        "gender": {
        |          "value": "Male"
        |        },
        |        "mrz_info": {
        |          "code": {
        |            "value": "IDVNM2000080275045200008027<<2\n0002141M4002149VNM<<<<<<<<<<<8\nLE<<HUU<VO<LONG<<<<<<<<<<<<<<<"
        |          },
        |          "id_number": {
        |            "value": "045200008027"
        |          },
        |          "gender": {
        |            "value": "Male"
        |          },
        |          "nationality": {
        |            "value": "Vietnamese"
        |          },
        |          "dob": {
        |            "value": "14/02/2000"
        |          },
        |          "doe": {
        |            "value": "14/02/2040"
        |          },
        |          "name": {
        |            "value": "LE HUU VO LONG"
        |          },
        |          "document_number": {
        |            "value": "200008027"
        |          }
        |        },
        |        "pob": {
        |          "value": "Gio Mỹ, Gio Linh, Quảng Tri"
        |        },
        |        "doe": {
        |          "value": "14/02/2040"
        |        },
        |        "doi": {
        |          "value": "16/09/2024"
        |        },
        |        "photo_from_document": "https://eyepass-storage.vizone.ai/eyepass-demo/avatar/2025/03/14/09_35_39_159108794.jpg?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=eyepass%2F20250314%2Fus-east-1%2Fs3%2Faws4_request&X-Amz-Date=20250314T025421Z&X-Amz-Expires=300&X-Amz-SignedHeaders=host&X-Amz-Signature=a345f7ae7352e640521acd64bfb26bc195399b95794e3a84685640c06bb20624",
        |        "front_aligned_image": "https://eyepass-storage.vizone.ai/eyepass-demo/front/2025/03/14/09_35_38_967377052.jpg?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=eyepass%2F20250314%2Fus-east-1%2Fs3%2Faws4_request&X-Amz-Date=20250314T025421Z&X-Amz-Expires=300&X-Amz-SignedHeaders=host&X-Amz-Signature=897e01b7c8d0b7757989b98444672fc2bf7dfb2150c04624635dc89ea16e3e51",
        |        "back_aligned_image": "https://eyepass-storage.vizone.ai/eyepass-demo/back/2025/03/14/09_36_06_680222346.jpg?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=eyepass%2F20250314%2Fus-east-1%2Fs3%2Faws4_request&X-Amz-Date=20250314T025421Z&X-Amz-Expires=300&X-Amz-SignedHeaders=host&X-Amz-Signature=0c4111e2c18779c3afcd62cf437bda4d61c8d9b74b39abb35bfe992d7496d798",
        |        "type": "id_card"
        |      },
        |      "validation_summary": 1
        |    }
        |  },
        |  "request_id": "bc661431-bdac-4d19-b80c-c7683302b85f",
        |  "message": "OK",
        |  "code": 0,
        |  "server_time": 1741920861
        |}""".stripMargin
    )
    val data = JsonHelper.fromNode[VBDDocumentInfo](responseNode.at("/data"))



    println(data.validationInfo.face.flatMap(_.compareData).flatMap(_.personCardValidation).flatMap(_.similarityPercentage).map(_ / 100))

  }

}
