package profile.something

import com.twitter.util.Await
import profile.module.DependencyModule
import vn.vhm.common.util.JsonHelper

/**
 * <AUTHOR> 8/9/24 12:31
 */
object TierListServiceTest {

  def main(args: Array[String]): Unit = {

    val tierService = DependencyModule.providesInternalService()

    println(
      JsonHelper.toJson(Await.result(tierService.getTiersCodeOrdered()))
    )
  }

}
