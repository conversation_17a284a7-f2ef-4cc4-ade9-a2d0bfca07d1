package profile.something

import com.twitter.util.Await
import profile.module.DependencyModule
import vn.vhm.common.domain.OptionImplicits.OptionImplicitAny

/**
 * <AUTHOR> 8/10/24 15:03
 */
object TestUpgradeTier {

  def main(args: Array[String]): Unit = {

    val tierService = DependencyModule.providesInternalService()

    val tiersOrdered = Await.result(tierService.getTiersCodeOrdered())
    val tiersCodeOrdered = tiersOrdered.map(_.code)

    val oldInitTierCode = Seq("GOLD")
      .filter(tiersCodeOrdered.contains)
      .sortWith((v1, v2) => tiersCodeOrdered.indexOf(v1) > tiersCodeOrdered.indexOf(v2))
      .headOption

    val newInitTierCode = Seq("DIAMOND")
      .filter(tiersCodeOrdered.contains)
      .sortWith((v1, v2) => tiersCodeOrdered.indexOf(v1) > tiersCodeOrdered.indexOf(v2))
      .headOption

    val currTierCode = "PLATINUM".toSome
    val isUpgradeFromCurrentTier = currTierCode.isEmpty || tiersCodeOrdered.indexOf(currTierCode.get) < tiersCodeOrdered.indexOf(newInitTierCode.getOrElse(""))

    println(isUpgradeFromCurrentTier)
  }

}
