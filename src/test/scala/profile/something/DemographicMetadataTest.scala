package profile.something

import profile.domain.customer.DemographicMetadata
import vn.vhm.common.util.JsonHelper

/**
 * <AUTHOR>
 */
object DemographicMetadataTest {
  def main(args: Array[String]): Unit = {

    val demographicMetadataJsonStr  =
      """{
        |  "gender": "M",
        |  "birthday": "1995-12-12",
        |  "hometown": "Kỳ Ph<PERSON>, Kỳ Anh, Hà Tĩnh",
        |  "nationality": "Việt Nam",
        |  "year_of_birth": 1995,
        |  "month_of_birth": 12,
        |  "permanent_address": "Tổ 5, Ấp 2 <PERSON><PERSON><PERSON>, <PERSON>, Mộc, Bà Rịa - Vũng Tàu",
        |  "hometown_region_code": "1104",
        |  "hometown_province_code": "1025",
        |  "permanent_address_region_code": "1107",
        |  "permanent_address_province_code": "1002",
        |  "unknown_field_1": "1",
        |  "unknown_field_2": "2"
        |}""".stripMargin


    val demographicMetadataJsonNode = JsonHelper.readTree(demographicMetadataJsonStr)
    val demographicMetadata = JsonHelper.fromNode[DemographicMetadata](demographicMetadataJsonNode)

    println(s"Read from json: ${JsonHelper.toJson(demographicMetadata, pretty = true)}}")

    val tierMap = Map(
      "vinfast" -> "Gold",
      "gsm" -> "Platinum"
    )

    demographicMetadata.setAllPnlTiers(tierMap)
    println(s"After set PnlTier]: ${JsonHelper.toJson(demographicMetadata, pretty = true)}")

    val pnlSpendingAmount = Map(
      "vinfast" -> 1000L,
      "gsm" -> 2000L
    )

    demographicMetadata.setAllPnlSpendingAmount(pnlSpendingAmount)
    println(s"After set PnlSpendingAmount: ${JsonHelper.toJson(demographicMetadata, pretty = true)}")

    val customerScores = Map(
      "VCLUB" -> "100",
      "HD_VIKKI" -> "200"
    )

    val customerScoreRanks = Map(
      "VCLUB" -> "A",
      "HD_VIKKI" -> "B"
    )


    demographicMetadata.setAllCustomerScores(customerScores).setAllCustomerScoreRanks(customerScoreRanks)
    println(s"After set CustomerScores, CustomerScoreRanks: ${JsonHelper.toJson(demographicMetadata, pretty = true)}")


    var demographicMetadataFinal = demographicMetadata.copy(livingAddressProvinceCode = Some("1025"), livingAddressRegionCode = Some("1104"))
    println(s"After copy livingAddress data: ${JsonHelper.toJson(demographicMetadataFinal, pretty = true)}")

    demographicMetadataFinal = demographicMetadataFinal.copy(yearOfBirth = Some(1996), monthOfBirth = Some(1))
    println(s"After copy new yearOfBirth and monthOfBirth: ${JsonHelper.toJson(demographicMetadataFinal, pretty = true)}")

    assert(demographicMetadataFinal.undefinedFields.get("unknown_field_1").contains("1"))
    assert(demographicMetadataFinal.undefinedFields.get("unknown_field_2").contains("2"))
    assert(demographicMetadataFinal.pnlTiers.get("vinfast_tier").contains("Gold"))
    assert(demographicMetadataFinal.pnlTiers.get("gsm_tier").contains("Platinum"))
    assert(demographicMetadataFinal.pnlSpendingAmount.get("vinfast_spending_amount").contains(1000L))
    assert(demographicMetadataFinal.pnlSpendingAmount.get("gsm_spending_amount").contains(2000L))
    assert(demographicMetadataFinal.livingAddressRegionCode.contains("1104"))
    assert(demographicMetadataFinal.livingAddressProvinceCode.contains("1025"))
    assert(demographicMetadataFinal.yearOfBirth.contains(1996))
    assert(demographicMetadataFinal.monthOfBirth.contains(1))
  }
}