package profile.something

import com.google.inject.Guice
import com.google.inject.name.Names
import com.twitter.inject.Injector
import com.twitter.util.Await
import profile.module.DependencyModule
import vn.vhm.common.domain.Implicits.async
import vn.vhm.common.repository.lock.AsyncLockManager

/**
 * <AUTHOR> 8/26/24 05:57
 */
object RedisAsyncLockTest {

  System.setProperty("mode", "staging-local")

  def main(args: Array[String]): Unit = {

    lazy val inject = Injector.apply(Guice.createInjector(DependencyModule))
    lazy val asyncLockManager = inject.instance[AsyncLockManager](Names.named("internal_process_lock"))

    while (true) {
      //      Thread.sleep(100L)

      Await.result(
        asyncLockManager.tryLock("sonpn_lock_test") {
          case true => async {
            println("Get lock")
          }
          case false => async {
            println("Can not get lock")
          }
        }
      )

    }
  }

}
