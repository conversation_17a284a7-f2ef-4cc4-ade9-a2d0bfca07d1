package profile.something

import profile.domain.entity.Tier
import vn.vhm.common.util.JsonHelper

/**
 * <AUTHOR> 8/23/24 13:28
 */
object TierSortTest {
  def main(args: Array[String]): Unit = {

    val tiers: Seq[Tier] = JsonHelper.fromJson[Seq[Tier]](
//      """
//        |[{"id":1,"code":"MEMBER"},{"id":2,"code":"GOLD","previous_tier":{"id":0,"code":"MEMBER"}},{"id":3,"code":"PLATINUM","previous_tier":{"id":0,"code":"GOLD"}},{"id":4,"code":"DIAMOND","previous_tier":{"id":0,"code":"PLATINUM"}}]
//        |""".stripMargin
      """
        | [
        |      {
        |        "id": 1,
        |        "createdOn": 1720093011001,
        |        "createdBy": "v.duydb4",
        |        "code": "GOLD",
        |        "name": "Gold",
        |        "status": "ACTIVE",
        |        "conditions": [
        |          "MANUAL",
        |          "POINT_BASED"
        |        ],
        |        "haveIssuedCards": false,
        |        "relegationAllowed": false,
        |        "previousTier": {
        |          "name": "Member",
        |          "benefitGroupList": [],
        |          "code": "MEMBER",
        |          "id_str": "null"
        |        },
        |        "tierGroup": {
        |          "code": "VINCLUB_MEMBERSHIP",
        |          "name": "VinClub",
        |          "id_str": "null"
        |        },
        |        "id_str": "1"
        |      },
        |      {
        |        "id": 2,
        |        "createdOn": 1720093069478,
        |        "createdBy": "v.duydb4",
        |        "code": "DIAMOND",
        |        "name": "Diamond",
        |        "status": "ACTIVE",
        |        "conditions": [
        |          "MANUAL"
        |        ],
        |        "haveIssuedCards": false,
        |        "relegationAllowed": true,
        |        "previousTier": {
        |          "name": "Platinum",
        |          "benefitGroupList": [],
        |          "code": "PLATINUM",
        |          "id_str": "null"
        |        },
        |        "tierGroup": {
        |          "code": "VINCLUB_MEMBERSHIP",
        |          "name": "VinClub",
        |          "id_str": "null"
        |        },
        |        "id_str": "2"
        |      },
        |      {
        |        "id": 3,
        |        "createdOn": 1720166976378,
        |        "createdBy": "v.duydb4",
        |        "code": "PLATINUM",
        |        "name": "Platinum",
        |        "status": "ACTIVE",
        |        "conditions": [
        |          "MANUAL"
        |        ],
        |        "haveIssuedCards": false,
        |        "relegationAllowed": true,
        |        "previousTier": {
        |          "name": "Gold",
        |          "benefitGroupList": [],
        |          "code": "GOLD",
        |          "id_str": "null"
        |        },
        |        "tierGroup": {
        |          "code": "VINCLUB_MEMBERSHIP",
        |          "name": "VinClub",
        |          "id_str": "null"
        |        },
        |        "id_str": "3"
        |      },
        |      {
        |        "id": 5,
        |        "createdOn": 1721211447542,
        |        "createdBy": "thientdq",
        |        "code": "MEMBER",
        |        "name": "Member",
        |        "status": "ACTIVE",
        |        "conditions": [
        |          "MANUAL"
        |        ],
        |        "haveIssuedCards": false,
        |        "relegationAllowed": false,
        |        "tierGroup": {
        |          "code": "VINCLUB_MEMBERSHIP",
        |          "name": "VinClub",
        |          "id_str": "null"
        |        },
        |        "id_str": "5"
        |      },
        |      {
        |        "id": 6,
        |        "createdOn": 1721212833613,
        |        "createdBy": "thientdq",
        |        "code": "TEST",
        |        "name": "Test",
        |        "status": "INACTIVE",
        |        "conditions": [
        |          "MANUAL",
        |          "SPENT_BASED"
        |        ],
        |        "haveIssuedCards": false,
        |        "relegationAllowed": false,
        |        "tierGroup": {
        |          "code": "VINCLUB_TEST",
        |          "name": "[Draft] Nhóm hạng Vinclub",
        |          "id_str": "null"
        |        },
        |        "id_str": "6"
        |      },
        |      {
        |        "id": 7,
        |        "createdOn": 1723008488354,
        |        "createdBy": "v.thuynt264",
        |        "code": "HANG_01",
        |        "name": "Hạng 01 thuộc nhóm hạng Vin Club tháng 8/2024",
        |        "status": "ACTIVE",
        |        "conditions": [
        |          "POINT_BASED"
        |        ],
        |        "haveIssuedCards": false,
        |        "relegationAllowed": false,
        |        "tierGroup": {
        |          "code": "NHOM_HANG_VINCLUB_07082024",
        |          "name": "Nhóm hạng Vin Club tháng 8/2024",
        |          "id_str": "null"
        |        },
        |        "id_str": "7"
        |      }
        |    ]""".stripMargin
    )
    val tierMap = tiers.map(tier => tier.code -> tier).toMap

    def getSortOrder(tier: Tier, visited: Set[String] = Set()): Int = {
      tier.previousTier match {
        case Some(prev) if !visited.contains(prev.code) =>
          1 + getSortOrder(tierMap(prev.code), visited + tier.code)
        case _ => 0
      }
    }

    println(JsonHelper.toJson(tiers.sortBy(tier => getSortOrder(tier)), true))

  }

}
