package profile.something

import profile.util.SnowflakeDefault

/**
 * <AUTHOR> 7/21/24 11:10
 */
object SnowflakeIdTest {
  def main(args: Array[String]): Unit = {
    println(SnowflakeDefault.nextId())
    Thread.sleep(200L)
    println(SnowflakeDefault.nextId())
    Thread.sleep(200L)
    println(SnowflakeDefault.nextId())
    Thread.sleep(200L)
    println(SnowflakeDefault.nextId())
    Thread.sleep(200L)
    println(SnowflakeDefault.nextId())
  }
}
