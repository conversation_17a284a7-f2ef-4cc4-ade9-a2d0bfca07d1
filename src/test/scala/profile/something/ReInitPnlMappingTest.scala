package profile.something

import com.google.inject.Guice
import com.twitter.inject.Injector
import com.twitter.util.Await
import profile.module.DependencyModule
import profile.service.ProfileService
import vn.vhm.common.util.JsonHelper

/**
 * <AUTHOR> 8/25/24 18:09
 */
object ReInitPnlMappingTest {

//  System.setProperty("mode", "production-local")
  System.setProperty("mode", "staging-local")

  def main(args: Array[String]): Unit = {

    lazy val inject = Injector.apply(Guice.createInjector(DependencyModule))

    lazy val profileService = inject.instance[ProfileService]

        val result = Await.result(profileService.reInitPnlMapping("110882824523008"))

//    val result = Await.result(profileService.getPnlMappings("49599837331456", true, Nil))
//    val result = Await.result(profileService.getPnlMappings("48866308834048", true, Nil))

    println(JsonHelper.toJson(result, true))

  }

}
