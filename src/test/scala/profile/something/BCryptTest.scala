package profile.something

import org.mindrot.jbcrypt.BCrypt
import profile.util.PasswordHelper

/**
 * <AUTHOR> 8/7/24 12:03
 */
object BCryptTest {

  def main(args: Array[String]): Unit = {

    val user = "sonpn17"
    val password = "mySecurePassword"

    println(BCrypt.gensalt(10))
    println(BCrypt.gensalt(10))
    println(BCrypt.gensalt(10))

    println(s"Original password: $password")
    println(s"Hashed password: ${PasswordHelper.hashPasswordBCrypt(user, password)}")
    println(s"Hashed password: ${PasswordHelper.hashPasswordBCrypt(user, password)}")
  }

}
