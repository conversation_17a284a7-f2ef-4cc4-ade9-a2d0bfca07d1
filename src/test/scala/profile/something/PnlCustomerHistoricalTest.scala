package profile.something

import com.google.inject.Guice
import com.twitter.inject.Injector
import profile.consumer.PnlCustomerHistoricalData
import profile.domain.customer.{PnlCustomer, PnlCustomerDAO}
import profile.domain.event.HistoricalEvent
import profile.module.DependencyModule
import vn.vhm.common.domain.OptionImplicits.OptionImplicitAny
import vn.vhm.common.util.JsonHelper

/**
 * <AUTHOR> 10/10/24 17:15
 */
object PnlCustomerHistoricalTest {

  System.setProperty("mode", "staging-local")

  private lazy val injector = Injector.apply(Guice.createInjector(DependencyModule))
  private lazy val pnlCustomerDAO = injector.instance[PnlCustomerDAO]

  def main(args: Array[String]): Unit = {

    val s =
      """
        |{"action":"UPDATED","timestamp":1728553348896,"object_id":"||+84794444333|||<EMAIL>","data":{"created_by":"SYSTEM","updated_by":"SYSTEM","created_on":1728552101783,"updated_on":1728553348896,"id":608407,"version":3,"pnl":"VINSCHOOL","company_code":null,"pnl_profile_id":"","pnl_user_id":"","vclub_user_id":46498725145856,"first_name":"","last_name":"","full_name":"","status":"ACTIVE","phone":"+84794444333","phone_verified_status":1,"email":"<EMAIL>","email_verified_status":1,"nationality_code":null,"birthday":null,"list_address":null,"gender":"U","list_identity_document":null,"identity_number_default":null,"metadata":"{\"num_of_studying_child\":4}","init_migrate_data":null,"vclub_event_id":1488106135199744,"source_created_on":1721535842745,"source_updated_on":1721377395350,"original_phone_number":"0794444333","active":true,"updated_time":1728553348896,"created_time":1728552101783},"old_data":{"created_by":"SYSTEM","updated_by":"SYSTEM","created_on":1728552101783,"updated_on":1728552315067,"id":608407,"version":2,"pnl":"VINSCHOOL","company_code":null,"pnl_profile_id":"","pnl_user_id":"","vclub_user_id":46498725145856,"first_name":"","last_name":"","full_name":"","status":"ACTIVE","phone":"+84794444333","phone_verified_status":1,"email":"<EMAIL>","email_verified_status":1,"nationality_code":null,"birthday":null,"list_address":null,"gender":"U","list_identity_document":null,"identity_number_default":null,"metadata":"{\"num_of_studying_child\": 1}","init_migrate_data":null,"vclub_event_id":1487970637365248,"source_created_on":1721535842745,"source_updated_on":1721377395350,"original_phone_number":"0794444333","active":true,"updated_time":1728552315067,"created_time":1728552101783}}""".stripMargin

    val historicalData = JsonHelper.fromJson[HistoricalEvent[PnlCustomerHistoricalData]](s)

    historicalData.data
      .flatMap(_.id)
      .flatMap(pnlCustomerId => pnlCustomerDAO.select(PnlCustomer(id = pnlCustomerId.toSome))).foreach(pnlCustomer => {
        if (pnlCustomer.vclubUserId.exists(_ > 0)) {
          println(JsonHelper.toJson(Map(
            "pnl_customer_id" -> pnlCustomer.id.get,
            "vclub_user_id" -> pnlCustomer.vclubUserId.get,
            "timestamp" -> historicalData.timestamp
          )))
        }
      })
  }
}
