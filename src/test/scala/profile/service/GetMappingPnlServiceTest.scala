package profile.service

import com.google.inject.Guice
import com.twitter.inject.Injector
import profile.domain.customer.PnlCustomerDAO
import profile.module.DependencyModule
import profile.util.Constant

/**
 * <AUTHOR> 7/20/24 09:18
 */
object GetMappingPnlServiceTest {

  System.setProperty("mode", "staging-local")

  def main(args: Array[String]): Unit = try {

    lazy val injector = Injector.apply(Guice.createInjector(DependencyModule))

    lazy val profileService = injector.instance[ProfileService]
    lazy val pnlCustomerDAO = injector.instance[PnlCustomerDAO]
    lazy val tierService = injector.instance[InternalService]

    //    val tiersCodeOrdered = Await.result(tierService.getTiersCodeOrdered().map(_.map(_.code)))
    //    val mapping = Await.result(profileService.getPnlMappings("14214826539442177", true))
    //    println(JsonHelper.toJson(mapping, true))

    //    val result = Await.result(profileService.get("14214826539442177"))
    //    println(result.getValue(Customer.CREATED_ON))
    //    println(result.getValue(Customer.UPDATED_ON))
    //    val result = Await.result(profileService.getPnlMappings("20587080396544", true))

    //    val result = Await.result(profileService.getPnlMappings("24083588911104", true, Map.empty[String, Long]))

    val result = pnlCustomerDAO.searchLatestActive("28677191930624", Constant.listPnl, Some("+84832330001"), Some("<EMAIL>"), Some("095980232022"), Some("022776542"))
    println(result)

  } catch {
    case e: Throwable => e.printStackTrace()
  } finally {
    println("Done")
    System.exit(0)
  }

}

/**
 * {
 * "mappings": {
 * "GSM": {
 * "id": 97493,
 * "pnl": "GSM",
 * "phone": "+84366618122",
 * "consent_at": 1721896543687,
 * "mapping_at": 1721896543687,
 * "pnl_user_id": "c64bf695-aea5-403e-8ef1-db4316772be6",
 * "consent_status": 1,
 * "init_tier_code": "SILVER",
 * "total_spend_amount": 0.00
 * },
 * "VINMEC": {
 * "id": 571143,
 * "pnl": "VINMEC",
 * "email": "<EMAIL>",
 * "phone": "+84987335747",
 * "consent_at": 1721896543687,
 * "mapping_at": 1721896543687,
 * "consent_status": 1,
 * "init_tier_code": "GOLD",
 * "total_spend_amount": 35000000,
 * "migrate_calc_at_time": 1721644564678,
 * "number_of_transaction": 5
 * },
 * "TEST_PNL": {
 * "id": 571523,
 * "pnl": "TEST_PNL",
 * "email": "<EMAIL>",
 * "phone": "+84987335747",
 * "consent_at": 0,
 * "mapping_at": 1722140452817,
 * "consent_status": 0,
 * "init_tier_code": "GOLD",
 * "total_spend_amount": 35000000,
 * "migrate_calc_at_time": 1721644564678,
 * "number_of_transaction": 5
 * }
 * },
 * "consent_at": 0,
 * "last_check_at": 1722140467889,
 * "consent_status": 1
 * }
 */