package profile.service

import com.twitter.util.Await
import vn.vhm.common.util.{<PERSON><PERSON>Helper, ZConfig}

/**
 * <AUTHOR> 9/19/24 05:48
 */
object AiIntegrationServiceTest {

  def main(args: Array[String]): Unit = {

    val svc = AiIntegrationServiceImpl(ZConfig.getConf("ai_integration"))

    //    val result = Await.result(
    //      svc.extractIdentify(
    //        identityType = CustomerIdentityDocType.CCCD,
    //        urls = Seq(
    //          "https://nhanlamgiayto.com/wp-content/uploads/2021/01/lam-can-cuoc-cong-dan-gia-2.jpg",
    //          "https://img.upanh.tv/2024/07/11/CMND_ba_Lan_mat_sau.jpg"
    //        )
    //      )
    //    )

    val result = Await.result(
      svc.faceMatching(
        Seq(
          "https://nhanlamgiayto.com/wp-content/uploads/2021/01/lam-can-cuoc-cong-dan-gia-2.jpg",
          "https://img.upanh.tv/2024/07/11/CMND_ba_Lan_mat_sau.jpg"
        ),
        "https://nhanlamgiayto.com/wp-content/uploads/2021/01/lam-can-cuoc-cong-dan-gia-2.jpg",
      )
    )

    println(JsonHelper.toJson(result))

  }

}
