package profile.service

import com.google.inject.Guice
import com.twitter.inject.Injector
import com.twitter.util.Await
import profile.module.DependencyModule
import vn.vhm.common.domain.OptionImplicits.OptionImplicitAny
import vn.vhm.common.util.JsonHelper

/**
 * <AUTHOR> 7/22/24 17:44
 */
object ProfilerServiceTest {

    System.setProperty("mode", "staging-local")
//  System.setProperty("mode", "production-local")

  def main(args: Array[String]): Unit = {

    lazy val inject = Injector.apply(Guice.createInjector(DependencyModule))

    lazy val profileService = inject.instance[ProfileService]
    lazy val contextHolder = inject.instance[ContextHolder]

//    val result = Await.result(profileService.getPnlMappings("24082508948480", true, Nil))
//    val result = Await.result(profileService.get("43659629682432"))

    val result = contextHolder.setContext(lang = "en".toSome) {
      Await.result(profileService.get("106228059178752"))
    }

    println(JsonHelper.toJson(result, true))

  }
}
