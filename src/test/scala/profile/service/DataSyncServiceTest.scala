package profile.service

import com.google.inject.Guice
import com.twitter.inject.Injector
import com.twitter.util.Await
import profile.module.DependencyModule

/**
 * <AUTHOR> 7/29/24 16:43
 */
object DataSyncServiceTest {

  System.setProperty("mode", "staging-local")

  def main(args: Array[String]): Unit = {

    lazy val injector = Injector.apply(Guice.createInjector(DependencyModule))
    lazy val dataSyncService = injector.instance[DataSyncService]

    Await.result(
      dataSyncService.singleSyncCustomerRanking("", 0L, None, (a, b) => println(s"$a, $b"))
    )
  }

}
