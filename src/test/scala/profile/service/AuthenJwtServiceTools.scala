package profile.service

import org.apache.commons.io.FileUtils
import profile.domain.customer.Customer
import profile.util.Constant
import vn.vhm.common.domain.OptionImplicits.OptionImplicitAny

import java.io.File
import java.security.KeyFactory
import java.security.spec.PKCS8EncodedKeySpec
import java.util.Base64

object AuthenJwtServiceTools {

  //public key staging: MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAnw+km1fT3/pTDZRxA2ZLChY1Yt6Q2Y2C+/wlz9O1UktivIbsO6hjXO8BLex2PReSGyWg2WangcmGeQquJEnqkV+gYMRhMsdVASoKGQXH1bEVQqJai5owHYwtEMApoFiLo0c/ZH357uB5DgzByZ8hErkgIi2Abgg/m05/yU3PJCp3Aj1s92PK47AjNDqOqNupq34Qp04ru+SfWmsq9EiUvUlXDfisgBlnm1pt7jxc6YY4qg7AhWh762pEQxzfziSU0bC2sKJJqOYvDvmRUEC05vpKxsBXaCrcseoafqbJxiYJG6rRIA4JIqFFqecJa/ZXewZTHlTyiRptpmgBxzllowIDAQAB
  def main(args: Array[String]): Unit = {
    buildJwt()
    //        buildPublicKey()
    //        buildSecretKey()
    // readFile()
    //getPrivateKey
    //buildPublicKey()
  }

  val authenService = AuthenJwtServiceImpl(null, null, null, null, null, null, null, null, null)

  def readFile(): Unit = {
    val content = FileUtils.readFileToString(new File("conf/rs256-key/jwtRS256.key"))
    buildSecretKey(content)
  }

  def buildJwt(): Unit = {
    val jwtToken = authenService._buildJwtToken(Customer(
      userId = "phongpq".toSome,
      email = "<EMAIL>".toSome,
      emailVerified = 1.toSome,
      phone = "0358984752".toSome,
      phoneVerified = 1.toSome
    ), None, jwtRequestedBy = Constant.JWT_CLAIM_REQUIRED_BY_VALUE_EMPTY)

    println("token " + jwtToken.token)

    val rs = authenService._parseJwtTokenRS256(jwtToken.token)
    println(rs)

    //    val rs02 = authenService._parseJwtTokenRS256(jwtToken.token)
    //    println(rs02)
  }

  def getPrivateKey = {
    val key =
      """
        |-----BEGIN PRIVATE KEY-----
        |MIIEvgIBADANBgkqhkiG9w0BAQEFAASCBKgwggSkAgEAAoIBAQDafwj36CO2/74Q
        |bVXALKy8CYQd0XCdXsjqs0kXF1VR3PAIN5AjXHWDu1IGqRSbCLYQwd0PuvUdiZ6L
        |G5sd/KHp4XihWhbEq8HJhjNV6CiG5lDAAq7/h4ME9PaUS3tSG1HXsZbCOEiJzGET
        |f0yyitqIfk9F0uh6hNhLgwkhGu9E++2apvO+AoGvPvbOJEnXqbXRT1DGhs1Zx0wY
        |/p2woPzvB6+moWHuMD2DXPcYUbJPnoqNMTxxuiLmJmytj1MbroHJ8c9CYmTZb6z4
        |DSIUT2mSQ/8g8AuEx6sa48lEhSKnDjTGCLhcLuEbs1R5N/JitaYCWu2WHc8ucevM
        |9VxuIkIxAgMBAAECggEAKUo75uNQyn6SS5bpz0bx/pfaxmrDmg2lMMsgKxOu/Skg
        |GCFt5OzOTU5bHiSaHwEarNiB3rzhIMuWsSmOmvBYpbLE4dL9Pf3KQXvRnDCi5flE
        |tcBcEzBn10o2hcgs3hOJGBZ4i6vos1ey2aUjGJNqYERkVSjZ43tSx5RT55RoNnP+
        |p2kkdd1m5KL9pAsLSDeGTbR/8kL2dYd/dn69w8EhuWErBMzFhKHfADMD+5N55wsE
        |aZuUAl4/inlIe+ksJk8KmmIWt3qroYS2oUwN3XTvamB9VwMfZyEfePEBQSfR1zpu
        |8i+dXG+eNnVDSGbOZinr0u+z+xnTpqJPIE+yxfLA1QKBgQD/f+fgmX2TZs8LZRXy
        |hvRGt+FeA/HvgKyUtaRX2VZmmt3azfTTnUrwJ9fHuwSHduQIAR6CLFHBlNi7g+aq
        |kx82Dl/cOri0yjWP3L87OcfRyA+R9KUfb2Q4AMo+9SUN7gw+SoTNyjNCPEBLpa3D
        |LmAyVxXWEWed/P65VOyBo35sdQKBgQDa7JPi4cGRWjYL6ga7lTcMZyOySHt1OApB
        |8CAbCERsM66LOT2yvACsJWt6NfNeoEIYKdYeemGQ3u/RRT2QE0qlPjhAoDrFs+2i
        |d8aILU3Q7V9hb/AblaIm13f28OTKlNwHWOK54oLAX4UF8yC03xJEfHdJc8av74bz
        |7XPJeMS3TQKBgDXlxIODvZLXsDuGIoAb6fH1HkyX5nSQn1drjfSeO3S/nKdopQny
        |93j73rAaipUNBoONwOLS+Jgujx3XcFRxXrcnhx9NH+O7eFsVxR14Tee6HVa71g1w
        |kSKjQAbR7fQnUFvfmm5TBkclsKaEl8hBwzc7Wbbjywjv8sgj2xRp6eR1AoGBAK8p
        |/YXaJju7l2TfAjYSxPHqkFg0wHtgtGxfygB3E/ATRgPlb+VToaIbaqxlAG3lAh09
        |VDIcr3XvuqiRoxllJiOi7fVffsbzGR8WgMYyoYGae+eFXfNkPVKgsj7mLdmdU5OZ
        |XgdnIkDz7IZ/KtstZPthLzyZIw9gMnV3t14rgBI9AoGBAPJq0CcyEJC3rfj6UI7j
        |PIDeX6U7icrXEMeNR4uXktecU4NiCvwHI+bINCxxiU+G2dhJw1t6hqpG3Uv6f42e
        |Do9984hNATSkUCK85ueexALZ/nFcWEXKVbO0TGbcDxz/R9PTY6Rjs8SzhMqdFJ8b
        |QNCLVeer8ZhhquJpaH0ddht2
        |-----END PRIVATE KEY-----
        |
        |""".stripMargin
    authenService._getPrivateKeyRS256(key)
  }

  def buildPublicKey(): Unit = {
    //MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAnw+km1fT3/pTDZRxA2ZLChY1Yt6Q2Y2C+/wlz9O1UktivIbsO6hjXO8BLex2PReSGyWg2WangcmGeQquJEnqkV+gYMRhMsdVASoKGQXH1bEVQqJai5owHYwtEMApoFiLo0c/ZH357uB5DgzByZ8hErkgIi2Abgg/m05/yU3PJCp3Aj1s92PK47AjNDqOqNupq34Qp04ru+SfWmsq9EiUvUlXDfisgBlnm1pt7jxc6YY4qg7AhWh762pEQxzfziSU0bC2sKJJqOYvDvmRUEC05vpKxsBXaCrcseoafqbJxiYJG6rRIA4JIqFFqecJa/ZXewZTHlTyiRptpmgBxzllowIDAQAB
    val publicKey =
      """
        |-----BEGIN PUBLIC KEY-----
        |MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA2n8I9+gjtv++EG1VwCys
        |vAmEHdFwnV7I6rNJFxdVUdzwCDeQI1x1g7tSBqkUmwi2EMHdD7r1HYmeixubHfyh
        |6eF4oVoWxKvByYYzVegohuZQwAKu/4eDBPT2lEt7UhtR17GWwjhIicxhE39Msora
        |iH5PRdLoeoTYS4MJIRrvRPvtmqbzvgKBrz72ziRJ16m10U9QxobNWcdMGP6dsKD8
        |7wevpqFh7jA9g1z3GFGyT56KjTE8cboi5iZsrY9TG66ByfHPQmJk2W+s+A0iFE9p
        |kkP/IPALhMerGuPJRIUipw40xgi4XC7hG7NUeTfyYrWmAlrtlh3PLnHrzPVcbiJC
        |MQIDAQAB
        |-----END PUBLIC KEY-----
        |
        |""".stripMargin
    println(authenService._getPublicRS256(publicKey))
    println(publicKey.replaceAll(System.lineSeparator, ""))
  }

  /*
  *  ssh-keygen -t rsa -b 4096 -m PEM -f jwtRS256.key
  *  openssl genrsa -out keypair.pem 2048
  *  openssl rsa -in keypair.pem -pubout -out jwtRS256.key.pub
  *  openssl pkcs8 -topk8 -inform PEM -outform PEM -nocrypt -in keypair.pem -out jwtRS256.key
  *
  * https://stackoverflow.com/questions/11410770/load-rsa-public-key-from-file
 */
  def buildSecretKey(key: String = ""): Unit = {
    val secretKey =
      """
        |-----BEGIN PRIVATE KEY-----
        |MIIJQgIBADANBgkqhkiG9w0BAQEFAASCCSwwggkoAgEAAoICAQCHjPjMSXXIc+d7
        |5AzcjLKdt5Id/QdRHB+CCGEaiV4lgLheQBUVW+oe/4uZC7dS4zQkV0HgaWVoYklo
        |CdJ1TbpoYhy1ghOVfK1BlwSm2igKC5YaHRWAx0X4Mfvir5k77IsO2/viKuv4qxIc
        |LiqzmAU8bTTR5K1sS93sDyeVtt2V2xg4ozNrnVgiycM1xiJ+OhEA0F5XGfalXd/z
        |XT+apmu1KWz79id1D1IYJCu0ggOtCi2cEEnJl05IE+jtpbIxwWX5K/sfU2CboxnJ
        |********************************+vK+/iHxPha+uJjM1t29fKa2YXqxVT12
        |CLbMaFx4AZC4BtgrPUVRqBt0EvqUp4h3OWetaUpIv9NH7oV6Dp1sv1zUZ8yFOqrw
        |x4Aou4oC4IFTbbQCQbV/Rk4PenDsnqVpYIe3Ppuuna11ZBzhDdQ24Za00bKDE/kY
        |IMX3/s+LRwHTiTkDbA7/Wi1Tu0B5JOMwL9llimaysgL4I/JGwc0PCSNEMpjmCXUe
        |d3wqCIYiTCUe3vCwUDOZDNixo4VdezgsBfxB3E8s68S2yXmdY4HUtLHmTQdBTMaA
        |Ux6ouIvn1iMKqonFBgtmwud++gfHQlUNwRinHX1rKE5l3exdeOo0Y6SQLNnLTV0F
        |+44yLGZ+RsD9C98fvBJu+EoJkmt+WQIDAQABAoICAAFzWMDsPakaKcKsUMGWH93g
        |Nl1rynBBGh/iHGfpoGr2UwLEh3U7DpOrrcAYMqtdvb5we0ao2hT2jaKMuFvZARhX
        |icccz+jW9LkG913HEovzo8o+asR+CBmn2fI1vttipKT8EFiol5iJpsqhE9k9fzuD
        |pP+6BWvAmWy1R3nVWHm0u9r0pCWQAX7IefZRWyfOp0VC9ZTTtPgAES1231S1reFl
        |GBgk14yWerfCFgBQg97jhP7D4zDtFZa1NSuV4lpLSjQTFxThZ4ntgjvNlHba+h0b
        |SQA/DSnBV3mv9tUCAQojjYfPF69Djklunf+x1LDHJohTP9V5JZRQPTV40mai7Jpi
        |19kguW/J8968QeYr3pvFAmmb/BwgwoWJF/gqW2m+TQi4Yt47FnlxXL1+wpZDkZaN
        |HMbPFp15Vpj0JBj90Ap+x8g/3XDGDvUAR25WA+8wgp9U60l0Y9eJrcbLooz3ctfK
        |AOKud6CVWxu+oqQ9emcv02AMIW5TDV+FqPxIkjR0wlt+YH/7GU9WqcEbnqtd6lKT
        |cpTlU4udXawbvp5i4HRDD5k7UQBhoQfdsV9mQgEHX+CUDAMp72k4Z46WNxyN/v63
        |+6g8Kf0o/bKiPI2LqAz+QLilQyotspGArLXscdoLJ4SnipKyLSXeOZfOGrk1oCsz
        |Tt3rHXm9UWGY09P7RA0hAoIBAQC8JhWY0YYslEH58aRE3YAan5ieGMN7Ywdb/XMF
        |6c6o56hs9+0Eqem1NmTZV/zSZzHcXHQ4XfaOnNjWAZpiPkIJWSs/yG4B9YzR5lef
        |+s4oWs6oZxktcppCvB5ZyQIEFZifRuG5S5qb4bvukUcHXSn6oP17AS6s83PEuhFY
        |RY+B4khniNvqOSMlQ49DCSNJEZ9KoagmrPqNEUc6Jlg7ytOKdcH55UxRMou4Z93n
        |MTexkPcPhEkPHHCZK14q/+oUnzk5EeSyHhDoYNokD1sY9nhs5kVMPKyLwaEaG3+S
        |TleXJRuzyUV6jC/29IEV2yuRvORiHs1xIHPkYr2XcOX0ZAz/AoIBAQC4bwZ6t50D
        |rOqlK9hoHD0V2NOC1TMaMtQGU+l5Tzjiv9umvfyTD27OJh6bY6ktU69sDgauAKta
        |AWz7mHRlsRgITJjICgDnFuayzH0d1D//GCBcPs1m8Jrg0br3Kn2F/WO+2GH81Wvc
        |dORdmQ2h442fGlXtvBA9cFv+ntLCfhrpuBFQU/Mi6hDFnql4quEPJIPR4cG0Nb03
        |eGmj88L1Sun+7KZOcCJVD24TwyP4KIk1BFnHhIiTm8rtua0wv1C69qRHg+2UUuK5
        |Vn0SACDh8ewq85C8eOXQPoPC7VPdtPZtXfzNVxF64JJRvPMAB/Xt3tXyes2K57pl
        |onYrR1KrpPynAoIBAQCtnQ7sttpypFPFCZ8lFKcy6fXwXLV1U8nkrpjG6hqSbUFT
        |W+tQFGHdn1Wr4K8gj6zIxF9R7BKpQj4sK8qDwSM6cRehgZdRI35P6ZcNvmchgbmU
        |V545+7gwwFnZS8wabLsqrkN3sjX2hXPfDhtWxjcIWCnOqSai3PsEmxWKOJXVqjJZ
        |p0YWeabeqdq2ZuYydJpiSY1aMt/37+uj0uI0OlK9PzBBIb8sZdHb1axDu3zC0xvH
        |K7Amy31zOTtpvkAHy20IJmS7n1bGtyhTgf/zpus0wb9MO+iUBZZc+uba4IOeqtVH
        |LwB3tohD/xZmTpmDn6O2junP67xFEIAWEE2BGBmNAoIBAAJFY7gJIMbVz0Hfa1fE
        |2T+2nswiofF1pMS7LcnPnuyz3hmMh1qsOvoQ4BmbvY6QVD2pYo2r9nAySkxC0VQb
        |PSeFAfCcpuwxjP+wou3GGk+i1WstXswgtFeGXZ4DiO4oc5z6/yHk4uIhElrQsqlG
        |qwzFfz9hLHYFNyzIeeHpOZ2dJaFulzTb2v8D+CRDPmg4U+M2u3LjooPXWL2rF8B7
        |fqwj79xRZyrS+NEHex7oKOBdQFnDmQbwSSxqN+a8xFq5BMbYqglEiAQuO3YISF9f
        |4j+ZJjQb7QCGdIrm7zxwqQQpvx4sL0vrDgIxqHBcqLuYHdxRhMxsFBCCUqAvzaXN
        |b0sCggEACVUti+p8vILetmKgcPEBxqp21qo/jmJug8PrXnlrnD1gvmEYZb/IiYGd
        |xS3b0JUD2aB3JHvoay/RgzaKOiWeIU9Qee3lEKHMVufdVss/04DZaqe/MCPPomZO
        |+b1vokqCWmPTnSz32LOGBpKJ/9EmMXQfM4tDc08YlAI0LvvlbbdFwBIcTOukFaRG
        |cwGX3JuwaOwiaD+6W2OTizn6zek//zuXqgnyg+ikDrAAbFqC197/kJNYHbCGA79B
        |J6i4N6sxfLoUOb2z876Sm6TBcaKSG3s0dhR0QVzg2SkMOM6mVOU6aX1+ZZQS2qOx
        |213T1jpdBV9wpId08BuFPS702pyO4A==
        |-----END PRIVATE KEY-----
        |""".stripMargin

    val replaceKey = key.replaceAll("\\n", "").replace("-----BEGIN PRIVATE KEY-----", "").replace("-----END PRIVATE KEY-----", "")

    println(replaceKey)
    val kf = KeyFactory.getInstance("RSA")

    val keySpecPKCS8 = new PKCS8EncodedKeySpec(Base64.getDecoder.decode(replaceKey))
    val privateKey = kf.generatePrivate(keySpecPKCS8)
    println(privateKey)
  }

}
