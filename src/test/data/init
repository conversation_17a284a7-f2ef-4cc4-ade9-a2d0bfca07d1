#!/bin/bash
set -e

psql -v ON_ERROR_STOP=1 --username "$POSTGRES_USER" --dbname "$POSTGRES_DB" <<-<PERSON><PERSON><PERSON>
	create schema if not exists $POSTGRES_SCHEMA;

  CREATE TABLE IF NOT EXISTS core_db.form
  (
      id                    varchar(128)   NOT NULL,
      name                  varchar(500)   NOT NULL,
      description           varchar(1000)         DEFAULT NULL,
      properties            jsonb          NOT NULL,
      status                smallint       NOT NULL DEFAULT '1',
      auth_type             smallint       NOT NULL DEFAULT '0',
      cate                  smallint              DEFAULT NULL,
      created_time          bigint       NOT NULL,
      created_by            varchar(128) NOT NULL,
      updated_time          bigint       NOT NULL,
      updated_by            varchar(128) NOT NULL,
      form_tags             jsonb                  DEFAULT NULL,
      lead_tags             jsonb                  DEFAULT NULL,
      announcement_settings jsonb                  DEFAULT NULL,
      CONSTRAINT form PRIMARY KEY (id)
  );

  CREATE TABLE IF NOT EXISTS core_db.form_submission
  (
      id              bigserial             NOT NULL,
      anonymous_id    varchar(128)          DEFAULT NULL,
      vhm_username    varchar(128)          DEFAULT NULL,
      context         jsonb                 DEFAULT NULL,
      form_id         varchar(128)          NOT NULL,
      form_data       jsonb                 DEFAULT NULL,
      timestamp       bigint                NOT NULL,
      care_status     smallint              NOT NULL DEFAULT '0',
      care_message    TEXT                  DEFAULT NULL,
      request_status  smallint              NOT NULL DEFAULT 1,
      request_message TEXT                  DEFAULT NULL,
      note            TEXT                  DEFAULT NULL,
      assignee        varchar(64)           DEFAULT NULL,
      care_timeline   jsonb                 DEFAULT NULL,
      updated_time    bigint                DEFAULT NULL,
      updated_by      varchar(128)          DEFAULT NULL,
      cate            smallint              DEFAULT NULL,
      phone           varchar(12) GENERATED ALWAYS AS (
                          COALESCE(
                              (form_data->>'phone'),
                              (form_data->>'phone_number'),
                              ''
                          )
                      ) STORED,
      CONSTRAINT form_submission_pkey PRIMARY KEY (id)
  );

  CREATE INDEX IF NOT EXISTS form_submission_phone_idx ON core_db.form_submission USING btree (phone);
  CREATE INDEX IF NOT EXISTS form_submission_vhm_username_idx ON core_db.form_submission USING btree (vhm_username);

EOSQL
