#@namespace scala vn.vhm.service
include "UserProfileDT.thrift"

service TUserProfileService {

    string ping()

    UserProfileDT.TFullUserInfoResp getUserProfileBySessionID(
        1: required string sessionId
    )

    UserProfileDT.TFullUserInfoResp getUserProfileByAccessToken(
        1: required string accessToken
    )

    UserProfileDT.TFullUserInfoResp getUserProfileByUsername(
        1: required string username
    )

    UserProfileDT.TUserProfileResp getUserProfile(
        1: required string username
    )

    UserProfileDT.TMapFullUserInfoResp mgetUserProfileByUsername(
        1: required list<string> listUsername
    )

    UserProfileDT.TMapUserProfileResp mgetUserProfile(
        1: required list<string> listUsername
    )

    UserProfileDT.TFullUserAuthInfoResp checkSession(
        1: required string sessionId
    )

    UserProfileDT.TAccessTokenResp checkAccessToken(
        1: required string accessToken
        2: optional bool uncheckExpireTime
    )

    UserProfileDT.TListUserProfileResp getUserProfiles(
        1: required i32 from
        2: required i32 size
    )

    UserProfileDT.TListUserProfileResp searchUserProfile(
        1: required i32 from
        2: required i32 size
        3: optional UserProfileDT.SearchResultType searchResultType
        4: optional i64 gtCreatedTime
        5: optional i64 ltCreatedTime
        6: optional i64 gtUpdatedTime
        7: optional i64 ltUpdatedTime
        8: optional list<string> orderByFields
        9: optional list<string> orderByTypes
    )
}

