#@namespace scala vn.vhm.domain.thrift.userprofile

enum SearchResultType {

  DEFAULT = 0,

  ONLY_TOTAL = 1,

  ONLY_DATA = 2,

  UNLIMITED_DATA = 3,

}

struct TSessionInfo {
    1:required string key
    2:required string value
    3:required i64 timeoutInMs
    4:required string domain
    5:optional string path
}

struct TUserProfile {
    1:required string username
    2:optional string displayName
    3:optional string email
    4:optional string avatar
    5:optional string phoneNumber
    6:optional string familyName
    7:optional string givenName
    8:optional map<string, string> additionalInfo
    9:optional bool emailVerified
    10:optional string registerChannel
    11:optional string gender
    12:optional string dayOfBirth
    13:optional string maritalStatus
    14:optional string marriedDay
    15:optional string address
    16:optional string job
    17:optional string facebook
    18:optional string linkedin
    19:optional string youtube
    20:optional string personalWebsite
    21:optional list<string> contactType
    22:optional list<string> locations
    23:optional i64 updatedTime
    24:optional i64 createdTime
    25:optional bool phoneVerified
}

struct TUserInfo {
    1:required string username
    2:required bool isActive
    3:required i64 createTime
    4:required list<i32> roles
}

struct TFullUserInfoResp{
    1:required bool exist,
    2:optional TUserInfo userInfo,
    3:optional TUserProfile userProfile
}

struct TMapFullUserInfoResp{
    1:required bool success
    2:optional map<string, TUserInfo> mapUserInfo
    3:optional map<string, TUserProfile> mapUserProfile
    4:optional string message
    5:optional string messageDetail
}

struct TUserProfileResp{
    1: required bool exist,
    2:optional TUserProfile userProfile
}

struct TMapUserProfileResp{
    1:required bool success
    2:optional map<string, TUserProfile> mapUserProfile
    3:optional string message
    4:optional string messageDetail
}

struct TListUserProfileResp{
    1:required i64 total
    2:optional list<TUserProfile> users
    3:optional string error
    4:optional string errorMessage
}

struct TFullUserAuthInfoResp{
    1:required bool exist,
    2:optional TUserInfo userInfo,
    3:optional TUserProfile userProfile
    4:optional TSessionInfo session
}

struct TStringResp{
    1: optional string value
    2: optional string error
    3: optional string errorMessage
}

struct TAccessTokenPayloadValue {
    1: optional string phone
    2: optional i32 phoneVerified
    3: optional string email
    4: optional i32 emailVerified
    5: optional string displayName
    6: optional string avatar
    7: optional map<string, string> additionalInfo
}

struct TAccessTokenResp {
    1: optional string username
    2: optional TAccessTokenPayloadValue payload
    3: optional string error
    4: optional string errorMessage
}
