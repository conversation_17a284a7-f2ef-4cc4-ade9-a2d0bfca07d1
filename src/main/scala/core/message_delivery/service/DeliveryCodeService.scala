package core.message_delivery.service

import com.google.inject.Inject
import com.twitter.inject.Logging
import com.twitter.util.Future
import profile.domain.notificationdelivery.{MessageStatus, StatusMsg}
import profile.exception.{ExceedQuotaException, InvalidPhoneException}
import vn.vhm.common.client.kafka_010.StringKafkaProducer
import vn.vhm.common.domain.profiling.Profiler
import vn.vhm.common.util.{<PERSON><PERSON><PERSON><PERSON><PERSON>, MustacheUtils, ZConfig}
import vn.vhm.notification.domain.NotificationType
import vn.vhm.notification.domain.email.{EmailAddress, EmailMessage, EmailNotificationV2, EmailToV2}
import vn.vhm.notification.domain.sms.SmsNotification
import vn.vhm.notification.service.DeliveryService

/**
 * <AUTHOR>
 */

trait DeliveryCodeService {

  def sendMessageToPhone(phone: String, message: String): Future[Unit]

  def sendEmail(email: String, subjectTemplate: String, bodyTemplate: String, data: Map[String, Any]): Future[Unit]

}

case class DeliveryCodeServiceImpl @Inject()(
                                              deliveryService: DeliveryService,
                                              notificationKafkaClient: StringKafkaProducer,
                                              topicNotification: String
                                            ) extends DeliveryCodeService with Logging {

  val clazz: String = getClass.getCanonicalName

  private val src = ZConfig.getString("sms.src_delivery")
  private val receiversStatusKey = ZConfig.getString("sms.receivers_status_key")
  private val bccEmailsDefault = ZConfig.getStringSeq("email.bcc_defaults", Nil)

  override def sendMessageToPhone(phone: String, message: String): Future[Unit] = Profiler(s"$clazz.sendMessageToPhone") {
    //    incrQuotaPhone(phone)
    val body = SmsNotification(from = src, to = Seq(phone), msg = message)

    deliveryService.deliveryV2(NotificationType.SMS, body)
      .onSuccess(result => info(s"$clazz.sendMessageToPhone($phone, $message)\t${JsonHelper.toJson(result, false)}"))
      .onFailure(ex => error(s"$clazz.sendMessageToPhone($phone, $message)", ex))
      .map(map => {
        if (map.nonEmpty && map.contains(receiversStatusKey)) {
          val receiverStatus: Map[String, MessageStatus] = JsonHelper.fromJson[Map[String, MessageStatus]](map(receiversStatusKey))
          receiverStatus(phone).msg match {
            case "invalid_phone" => throw InvalidPhoneException()
            case StatusMsg.EXCEED_QUOTA => throw ExceedQuotaException()
            case _ => throw new Exception(receiverStatus(phone).msg)
          }
        }
      })
  }

  def sendEmail(email: String, subjectTemplate: String, bodyTemplate: String, data: Map[String, Any]): Future[Unit] = Profiler(s"$clazz.sendEmail") {
    //    incrQuotaEmail(email)

    notificationKafkaClient.sendOnlyValue(
        topic = topicNotification,
        JsonHelper.toJson(
          EmailNotificationV2(
            from = "system",
            to = EmailToV2(to = Seq(EmailAddress(email, None)), bcc = bccEmailsDefault.map(e => EmailAddress(email = e))),
            msg = EmailMessage(
              body = MustacheUtils.renderMustache(bodyTemplate, data),
              subject = Some(MustacheUtils.renderMustache(subjectTemplate, data))
            )), false)
      )
      .onSuccess(record => info(s"$clazz.sendEmail($email, ${JsonHelper.toJson(data, false)})\t${JsonHelper.toJson(record)}"))
      .onFailure(ex => error(s"$clazz.sendEmail($email, ${JsonHelper.toJson(data, false)})", ex))
      .map(_ => {})
  }

}
