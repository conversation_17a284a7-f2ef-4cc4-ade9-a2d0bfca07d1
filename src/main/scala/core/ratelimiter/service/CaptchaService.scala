package core.ratelimiter.service

import com.twitter.util.Future
import vn.vhm.common.client.ssdb.SSDBClient
import vn.vhm.common.domain.OptionImplicits.OptionImplicitAny
import vn.vhm.common.domain.profiling.Profiler
import vn.vhm.common.util.ZConfig

import java.util.UUID
import scala.concurrent.duration.DurationInt

/**
 * <AUTHOR> 2/28/24 10:51
 */
trait CaptchaService {

  def verifyCaptcha(tokenCaptcha: String, withCache: Boolean,
                    captchaVersion: Option[String],
                    numberOfTimes: Option[Int] = None,
                    cacheCaptchaTtlInSecond: Option[Int] = None): Future[Boolean]

  def verifyCaptchaWithCache(tokenCaptcha: String,
                             captchaVersion: Option[String] = None,
                             numberOfTimes: Option[Int] = None,
                             cacheCaptchaTtlInSecond: Option[Int] = None): Future[Boolean]

}

case class CaptchaServiceImpl(
                               externalCaptchaService: ExternalCaptchaService,
                               keyValueRepo: SSDBClient
                             ) extends CaptchaService {

  private val clazz = getClass.getCanonicalName

  private val cacheCaptchaNumOfTimesDefault = ZConfig.getInt("captcha.cache.number", 5)
  private val cacheCaptchaTtlInSecondDefault = ZConfig.getInt("captcha.cache.ttl_second", 10.minutes.toSeconds.toInt)

  override def verifyCaptcha(tokenCaptcha: String, withCache: Boolean,
                             captchaVersion: Option[String],
                             numberOfTimes: Option[Int] = None,
                             cacheCaptchaTtlInSecond: Option[Int] = None): Future[Boolean] = Profiler(s"$clazz.verifyCaptcha") {
    if (withCache) {
      val cacheKey = buildCaptchaCacheKey(tokenCaptcha)
      for {
        optNumRemain <- keyValueRepo.getIntAsync(cacheKey)
        result <- optNumRemain match {
          case Some(numRemain) if numRemain > 0 =>
            keyValueRepo.decrAsync(cacheKey, 1)
            Future.True
          case _ =>
            externalCaptchaService.verifyCaptcha(tokenCaptcha).map {
              case true =>
                keyValueRepo.setInt(
                  cacheKey,
                  cacheCaptchaNumOfTimesDefault,
                  cacheCaptchaTtlInSecondDefault.toSome
                )
                true
              case _ => false
            }
        }
      } yield result
    } else {
      externalCaptchaService.verifyCaptcha(tokenCaptcha)
    }

  }

  override def verifyCaptchaWithCache(tokenCaptcha: String,
                                      captchaVersion: Option[String] = None, numberOfTimes: Option[Int] = None,
                                      cacheCaptchaTtlInSecond: Option[Int] = None): Future[Boolean] = Profiler(s"$clazz.verifyCaptchaWithCache") {
    verifyCaptcha(tokenCaptcha, withCache = true, captchaVersion, numberOfTimes, cacheCaptchaTtlInSecond)
  }

  private def buildCaptchaCacheKey(token: String): String = {
    val first = UUID.nameUUIDFromBytes(token.getBytes("UTF-8")).toString
    val second = if (token.length > 50) token.substring(0, 50) else token
    val third = if (token.length > 50) token.reverse.substring(0, 50) else token.reverse

    s"${ZConfig.appName}-captcha-cache-$first-$second-$third"
  }

}
