package core.ratelimiter.service

import com.fasterxml.jackson.databind.JsonNode
import com.twitter.inject.Logging
import com.twitter.util.Future
import com.typesafe.config.Config
import scalaj.http.Http
import vn.vhm.common.domain.Implicits._
import vn.vhm.common.domain.profiling.Profiler
import vn.vhm.common.util.JsonHelper

/**
 * <AUTHOR>
 */
trait ExternalCaptchaService {
  def verifyCaptcha(token: String): Future[Boolean]
}

case class ReCaptchaServiceImpl(recaptchaConf: Config) extends ExternalCaptchaService with Logging {

  private val clazz = getClass.getCanonicalName

  private val url = recaptchaConf.getString("url_verify")
  private val secretKey = recaptchaConf.getString("secret_key")

  override def verifyCaptcha(token: String): Future[Boolean] = Profiler(s"$clazz.verifyCaptcha") {
    async {
      try {
        val req = Http(url)
          .timeout(30000, 30000)
          .param("secret", secretKey)
          .param("response", token)
        val resp = req.asString
        resp.code match {
          case 200 => JsonHelper.fromJson[JsonNode](resp.body).path("success").asBoolean
          case _ => false
        }
      } catch {
        case e: Exception =>
          error(e.toString, e)
          false
      }
    }

  }
}