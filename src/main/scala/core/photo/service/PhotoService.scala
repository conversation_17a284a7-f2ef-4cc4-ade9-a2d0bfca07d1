//package core.photo.service
//
//import com.fasterxml.jackson.databind.JsonNode
//import com.twitter.inject.Logging
//import com.twitter.util.Future
//import com.typesafe.config.Config
//import profile.domain.FileData
//import profile.exception.InternalException
//import scalaj.http.{Http, HttpOptions, MultiPart}
//import vn.vhm.common.domain.Implicits._
//import vn.vhm.common.domain.profiling.Profiler
//import vn.vhm.common.util.JsonHelper
//
//import scala.util.Random
//
///**
// * <AUTHOR>
// */
//trait PhotoService {
//  def uploadAvatar(username: String, data: FileData): Future[String]
//}
//
//case class PhotoServiceImpl(config: Config) extends PhotoService with Logging {
//
//  val clazz = getClass.getCanonicalName
//
//  val apiUrl = config.getString("api_url")
//  val service = config.getString("service")
//
//  override def uploadAvatar(username: String, data: FileData): Future[String] = Profiler(s"$clazz.uploadAvatar") {
//    async {
//      try {
//        val req = Http(apiUrl)
//          .option(HttpOptions.allowUnsafeSSL)
//          .option(HttpOptions.followRedirects(true))
//          .timeout(60000, 60000)
//          .header("Content_Type", "multipart/form-data")
//          .postMulti(
//            MultiPart(name = "upload", filename = data.fileName, mime = data.mime, data = data.data, numBytes = data.numBytes, _ => {}),
//            MultiPart.apply(name = "service", filename = "", mime = "text/plain", data = service),
//            MultiPart.apply(name = "external_id", filename = "", mime = "text/plain", data = username),
//            MultiPart.apply(name = "file_name", filename = "", mime = "text/plain", data = "avatar.jpg")
//          )
//        val resp = req.asString
//        val bodyJson = JsonHelper.fromJson[JsonNode](resp.body)
//
//        Option(bodyJson.at("/data/public_url").asText(null)) match {
//          case Some(url) => s"$url?_t=${Random.nextInt(1000000)}"
//          case _ =>
//            error(s"Failed when uploadAvatar($username, ${data.fileName}, ${data.mime}): ${resp.body}")
//            throw InternalException(s"failed_upload_avatar", "")
//        }
//      } catch {
//        case e: InternalException => throw e
//        case e: Exception =>
//          error(s"Failed when uploadAvatar($username, ${data.fileName}, ${data.mime})", e)
//          throw InternalException(s"failed_upload_avatar", "")
//      }
//    }
//
//  }
//}