package core.quota.service

import com.twitter.util.logging.Logging
import com.twitter.util.{Future, Return, Throw}
import core.ratelimiter.service.ExternalCaptchaService
import vn.vhm.common.client.ssdb.SSDBClient
import vn.vhm.common.domain.Implicits.async
import vn.vhm.common.domain.profiling.Profiler
import vn.vhm.common.exception.VhmException
import vn.vhm.common.util.ZConfig

import scala.concurrent.duration.Duration

/**
 * <AUTHOR> 2/18/24 16:38
 */

case class InvalidTokenCaptchaException(msg: String = "") extends VhmException(msg) {
  override val error: String = "invalid_token_captcha"
}

case class ExceedQuotaException(message: String = "", cause: Throwable = null) extends Exception(message, cause)

trait HttpQuotaService {

  def calcQuotaEntityMac[T](entityType: String, entityId: String, mac: String,
                            tokenCaptcha: Option[String],
                            ttl: Duration, numCheck: Int, deleteIfSuccess: Boolean)(fn: => Future[T]): Future[T]

}

case class HttpQuotaServiceSSDBImpl(
                                     repository: SSDBClient,
                                     captchaService: ExternalCaptchaService) extends HttpQuotaService with Logging {

  private val clazz = getClass.getCanonicalName

  override def calcQuotaEntityMac[T](entityType: String, entityId: String, mac: String,
                                     tokenCaptcha: Option[String],
                                     ttl: Duration, numCheck: Int, deleteIfSuccess: Boolean)(fn: => Future[T]): Future[T] = Profiler(s"$clazz.calcQuotaEntityMac") {
    for {

      exceedQuotaEntity <- isExceedQuotaEntityMac(entityType, entityId, mac, numCheck)
        .respond(_ => incrQuotaEntityMac(entityType, entityId, mac, ttl.toSeconds.toInt))

      _ <- tokenCaptcha match {
        case None if exceedQuotaEntity => Future.exception(ExceedQuotaException(s"captcha:missing,entity_type=$entityType,entity:true,country:undef"))
        case Some(tokenCaptcha) if exceedQuotaEntity =>
          captchaService.verifyCaptcha(tokenCaptcha).map {
            case true =>
            case _ => throw InvalidTokenCaptchaException(s"captcha:invalid,entity_type=$entityType,entity:true,country:undef")
          }
        case _ => Future.Unit
      }

      result <- {
        fn
          .onSuccess(_ => if (deleteIfSuccess) deleteQuotaEntityMac(entityType, entityId, mac))
      }

    } yield result

  }

  private def isExceedQuotaEntityMac(entityType: String, entityId: String,
                                     mac: String, numCheck: Int): Future[Boolean] = Profiler(s"$clazz.isExceedQuotaUserMac") {
    repository.getIntAsync(buildQuotaEntityKey(entityType, entityId, mac))
      .map(resp => {
        val num = resp.getOrElse(0)
        if (num > numCheck) true else false
      })
  }

  private def incrQuotaEntityMac(entityType: String, entityId: String, mac: String,
                                 expireTimeInSecond: Int): Future[Unit] = Profiler(s"$clazz.incrQuotaEntityMac") {
    val quotaKey = buildQuotaEntityKey(entityType, entityId, mac)
    for {
      _ <- repository.incrAsync(quotaKey, 1)
        .onSuccess(i => {
          if (i.getOrElse(0) <= 1)
            repository.expireAsync(quotaKey, expireTimeInSecond)
              .onFailure(fn => error(s"Failed to expire quota entity ($entityType, $entityId, $mac)", fn))
        })
        .transform {
          case Return(_) => Future.Unit
          case Throw(e) => async(error(s"Failed to incrQuotaEntity ($entityType, $entityId, $mac)", e))
        }
    } yield {}
  }

  private def deleteQuotaEntityMac(entityType: String, entityId: String, mac: String): Future[Unit] = Profiler(s"$clazz.deleteQuotaEntityMac") {
    repository.deleteAsync(buildQuotaEntityKey(entityType, entityId, mac))
      .unit
  }

  private def buildQuotaEntityKey(entityType: String, entityId: String, mac: String = ""): String = {
    s"${ZConfig.appName}-quotaEntity-$entityType-$entityId-$mac"
  }

}
