package core.quota.domain

import com.twitter.finagle.http.Request
import com.twitter.util.Try

/**
 * <AUTHOR> 6/20/24 07:27
 */
case class QuotaFilterKey(checked: Boolean)

object QuotaFilterContext {

  private val QuotaStatusField = Request.Schema.newField[QuotaFilterKey]()

  private val xRealIPHeader = "X-Real-IP"
  private val cfIPCountryHeader = "cf-ipcountry"
  private val cfIPCountryVNCode = "VN"

  def setChecked(request: Request): Unit = {
    request.ctx.update(QuotaStatusField, QuotaFilter<PERSON>ey(true))
  }

  implicit class QuotaFilterContextSyntax(val request: Request) extends AnyVal {
    def quotaChecked: Boolean = Try(request.ctx.apply(QuotaStatusField)).toOption.exists(_.checked)

    def setQuotaChecked(): Unit = setChecked(request)

    def optIP: Option[String] = {
      request.headerMap.find(_._1.equalsIgnoreCase(xRealIPHeader)).map(_._2).filter(_.nonEmpty)
        .orElse(Option(request.remoteAddress).flatMap(v => Option(v.getHostAddress))).filter(_.nonEmpty)
    }

    def optCountry: Option[String] = {
      request.headerMap.find(_._1.equalsIgnoreCase(cfIPCountryHeader)).map(_._2)
    }

    def countryIsVN: Option[Boolean] = optCountry.map(_.equalsIgnoreCase(cfIPCountryVNCode))

  }

}
