package caas.service

import caas.domain.{OAuthInfo, Session, SessionRepository}
import com.twitter.inject.Logging
import com.twitter.util.Future
import jcaas.core.CAAS
import jcaas.model.dal.JUserDAO
import profile.domain.UserStatus
import profile.domain.customer.{Customer, CustomerDAO}
import profile.domain.entity._
import profile.exception.{InvalidCredentialException, UserBlockedException}
import vn.vhm.common.domain.Implicits._
import vn.vhm.common.domain.OptionImplicits.OptionImplicitAny
import vn.vhm.common.domain.profiling.Profiler
import vn.vhm.common.util.{FutureUtils, <PERSON>sonHelper, ZConfig}

import scala.collection.JavaConverters._
import javax.inject.Inject

/**
 * <AUTHOR>
 *         created on 9/19/16.
 */

trait CaasService {

  def getActiveUserByPhoneWithPassState(normPhone: String): Future[(Option[Customer], Int)]

  def getActiveUserByEmailWithPassState(email: String, withNormalize: Boolean = false): Future[(Option[Customer], Int)]

  def registerUser(username: String, password: String): Future[UserAuthInfo]

  def registerUserWithOAuth(oauthInfo: OAuthInfo, password: Option[String]): Future[UserAuthInfo]

  // def registerUser(username: String, phone: String, profile: UserProfile, oauthLinkStorage: Option[(String, String)]): Future[User]

  def linkOAuthToUser(username: String, oauthLinkStorage: (String, String)): Future[Unit]

  def login(username: String, password: String, ssTimeout: Option[Long],
            attributes: Map[String, String]): Future[UserAuthResult]

  def loginOnlyUsername(username: String, ssTimeout: Option[Long],
                        attributes: Map[String, String]): Future[UserAuthResult]

  def loginWithOAuth(oauthInfo: OAuthInfo, ssTimeout: Option[Long], password: Option[String],
                     attributes: Map[String, String]): Future[UserAuthResult]

  def logout(sessionId: String): Future[Unit]

  def getUserBySession(sessionId: String): Future[Option[UserAuthInfo]]

  def getUserByUsername(username: String): Future[Option[UserAuthInfo]]

  def mgetUserByUsername(listUsername: Seq[String]): Future[Map[String, Customer]]

  def resetPasswordUser(username: String, newPassword: String): Future[Unit]

  def updatePasswordUser(username: String, oldPassword: Option[String], newPassword: String): Future[Unit]

  def isPasswordUser(username: String, passwordHashed: String): Future[Unit]

  def logoutAll(username: String): Future[Unit]

  def clearOtherSessions(username: String, toTime: Long, currentSession: String): Future[Unit]

}

case class CaasServiceImpl @Inject()(
                                      caas: CAAS,
                                      jUserDAO: JUserDAO,
                                      customerDAO: CustomerDAO,
                                      sessionRepo: SessionRepository
                                    ) extends CaasService with Logging {

  protected val clazz = getClass.getCanonicalName
  val sessionTimeout: Long = ZConfig.getLong("caas.session.timeout", 30L * 86400L * 1000L)

  //  override def renewSession(oldSessionId: String, ssTimeout: Option[Long]): Future[String] = async {
  //    try {
  //      caas.renewSession(oldSessionId, ssTimeout match {
  //        case Some(x) => x
  //        case _ => sessionTimeout
  //      })
  //    } catch {
  //      case e: Exception =>
  //        error(s"$clazz.renewSession($oldSessionId, $ssTimeout)", e)
  //        throw UnAuthenException("session is empty or not exist")
  //    }
  //  }

  override def registerUser(username: String, password: String): Future[UserAuthInfo] = Profiler(s"$clazz.registerUser") {
    async[UserAuthInfo] {
      if (jUserDAO.isExistUser(username)) {
        throw new Exception("User [" + username + "] already exist.");
      }
      jUserDAO.insertUser(username, password, true)
    }
  }

  override def registerUserWithOAuth(oauthInfo: OAuthInfo, password: Option[String]): Future[UserAuthInfo] = Profiler(s"$clazz.registerUserWithOAuth") {
    async[UserAuthInfo] {
      //    if (!userDAO.isExistUser(oauthInfo.username)) {
      //      userDAO.insertUser(oauthInfo.username, password.getOrElse(oauthInfo.getPass), true)
      //    }
      if (jUserDAO.isExistUser(oauthInfo.username)) {
        throw new Exception("User already exist.");
      }
      jUserDAO.insertUser(oauthInfo.username, password.getOrElse(oauthInfo.getPass), true)

      jUserDAO.getUserInfo(oauthInfo.username)
    }
  }

  //  override def registerUser(username: String, phone: String, profile: UserProfile, oauthLinkStorage: Option[(String, String)]): Future[User] = async {
  //    val time = System.currentTimeMillis().toSome
  //    val user = User(
  //      username = Some(username), password = None, status = 1.toSome,
  //      createdTime = time, updatedTime = time,
  //      phone = Some(phone)
  //    )
  //    user.profile = Some(profile)
  //    oauthLinkStorage.foreach(tuple => user.setValues(tuple._1, tuple._2))
  //    userDAO.insert(user)
  //    user
  //  }

  override def linkOAuthToUser(username: String, oauthLinkStorage: (String, String)): Future[Unit] = Profiler(s"$clazz.linkOAuthToUser") {
    async {
      val time = Some(System.currentTimeMillis())
      val user = Customer(userId = Some(username), updatedTime = time)
      user.setValues(oauthLinkStorage._1, oauthLinkStorage._2)
      customerDAO.update(user)
    }.unit
  }

  override def login(username: String, password: String, ssTimeout: Option[Long],
                     attributes: Map[String, String]): Future[UserAuthResult] = Profiler(s"$clazz.login") {
    async {
      try {
        if (password == null || password.isEmpty) throw new Exception("password is empty")
        val ssid: String = Profiler(s"$clazz.login-internal") {
          caas.login(username, password, true, ssTimeout match {
            case Some(x) => x
            case _ => sessionTimeout
          }, attributes.asJava)
        }
        UserAuthResult(ssid, jUserDAO.getUserInfo(username))
      } catch {
        case e: org.apache.shiro.authc.IncorrectCredentialsException =>
          error(s"login($username, $ssTimeout): ${e.getClass.getCanonicalName}(${e.getMessage})")
          throw InvalidCredentialException("Login info wrong")
        case e: org.apache.shiro.authc.LockedAccountException =>
          error(s"login($username, $ssTimeout): ${e.getClass.getCanonicalName}(${e.getMessage})")
          throw UserBlockedException()
        case e: Exception =>
          error(s"login($username, $ssTimeout)", e)
          throw InvalidCredentialException("Login info wrong")
      }
    }
  }

  override def loginOnlyUsername(username: String, ssTimeout: Option[Long],
                                 attributes: Map[String, String]): Future[UserAuthResult] = Profiler(s"$clazz.loginOnlyUsername") {
    async {
      try {
        val refreshToken: String = caas.loginOAuth(username, true, ssTimeout match {
          case Some(x) => x
          case _ => sessionTimeout
        }, attributes.asJava)
        UserAuthResult(refreshToken, jUserDAO.getUserInfo(username))
      } catch {
        case e: org.apache.shiro.authc.LockedAccountException =>
          error(s"loginOnlyUsername($username, $ssTimeout): ${e.getClass.getCanonicalName}(${e.getMessage})")
          throw UserBlockedException()
        case e: Exception =>
          error(s"loginOnlyUsername($username, $ssTimeout)", e)
          throw InvalidCredentialException("Login info wrong")
      }
    }
  }

  override def loginWithOAuth(oauthInfo: OAuthInfo, ssTimeout: Option[Long], password: Option[String],
                              attributes: Map[String, String]): Future[UserAuthResult] = Profiler(s"$clazz.loginWithOAuth") {
    async {
      try {
        val refreshToken: String = caas.loginOAuth(oauthInfo.username, true, ssTimeout match {
          case Some(x) => x
          case _ => sessionTimeout
        }, attributes.asJava)

        UserAuthResult(refreshToken, jUserDAO.getUserInfo(oauthInfo.username))
      } catch {
        case e: org.apache.shiro.authc.LockedAccountException =>
          error(s"loginWithOAuth(${JsonHelper.toJson(oauthInfo)}, $ssTimeout): ${e.getClass.getCanonicalName}(${e.getMessage})")
          throw UserBlockedException()
        case e: Exception =>
          error(s"loginWithOAuth(${JsonHelper.toJson(oauthInfo)}, $ssTimeout)", e)
          throw InvalidCredentialException("Login info wrong")
      }
    }
  }

  override def logout(sessionId: String): Future[Unit] = Profiler(s"$clazz.logout") {
    async {
      try {
        caas.logout(sessionId)
      } catch {
        case e: Exception =>
          error(s"$clazz.logout($sessionId): ${e.getClass.getCanonicalName} - ${e.getMessage}")
      }
    }
  }

  override def getUserBySession(sessionId: String): Future[Option[UserAuthInfo]] = Profiler(s"$clazz.getUserBySession") {
    async[Option[UserAuthInfo]] {
      try {
        val username = caas.getUser(sessionId)
        Option(jUserDAO.getUserInfo(username))
      } catch {
        case e: org.apache.shiro.authz.UnauthenticatedException =>
          //        error(s"$clazz.getUserBySession($sessionId)\t${e.getClass.getCanonicalName}(${e.getMessage})")
          None
        case e: Exception =>
          error(s"$clazz.getUserBySession($sessionId)", e)
          None
      }
    }
  }

  override def getUserByUsername(username: String): Future[Option[UserAuthInfo]] = Profiler(s"$clazz.getUserByUsername") {
    async[Option[UserAuthInfo]] {
      try {
        Option(jUserDAO.getUserInfo(username))
      } catch {
        case e: Exception =>
          error(s"$clazz.getUserByUsername($username)", e)
          None
      }
    }
  }

  override def mgetUserByUsername(listUsername: Seq[String]): Future[Map[String, Customer]] = Profiler(s"$clazz.mgetUserByUsername") {
    async {
      val mapUser = customerDAO.multiSelect(listUsername)
      //    if (mapUser.nonEmpty) {
      //      val mapRoles = userV2DAO.mgetRolesByUser(listUsername)
      //      mapUser.foreach(p => p._2.roles = mapRoles.get(p._1))
      //    }
      mapUser
    }
  }

  override def resetPasswordUser(username: String, newPassword: String): Future[Unit] = Profiler(s"$clazz.resetPasswordUser") {
    async {
      jUserDAO.resetPasswordUser(username, newPassword)
    }
  }

  override def updatePasswordUser(username: String, oldPasswordHashed: Option[String], newPasswordHashed: String): Future[Unit] = Profiler(s"$clazz.updatePasswordUser") {
    async {
      if (!jUserDAO.isPassword(username, oldPasswordHashed.getOrElse(""))) throw InvalidCredentialException("Old password is wrong")
      jUserDAO.updatePasswordUser(username, oldPasswordHashed.getOrElse(""), newPasswordHashed)
    }
  }

  override def isPasswordUser(username: String, passwordHashed: String): Future[Unit] = Profiler(s"$clazz.isPasswordUser") {
    async {
      if (!jUserDAO.isPassword(username, passwordHashed)) throw InvalidCredentialException("Password is invalid")
    }
  }

  //  override def isCredentialDefault(oauthInfo: OAuthInfo) = async {
  //    userDAO.isPassword(oauthInfo.username, oauthInfo.getPass)
  //  }

  //  override def getActiveUsername(from: Int, size: Int) = async {
  //    val r = jUserDAO.getUsernameActive(from, size)
  //    Pageable(r.getTotal, r.getData.asScala)
  //  }

  implicit private def fromJava(userInfo: jcaas.model.entity.UserInfo): UserAuthInfo = {
    //UserAuthInfo(userInfo.getUsername, userInfo.getRoles.asScala.map(v => v.getId), userInfo.isActive, userInfo.getCreateTime)
    UserAuthInfo(userInfo.getUsername)
  }

  override def logoutAll(username: String): Future[Unit] = Profiler(s"$clazz.logoutAll") {
    for {
      sessions <- async {
        sessionRepo.selectByUser(username)
      }

      _ = _logoutAll(sessions)

    } yield {}
  }

  override def clearOtherSessions(username: String, toTime: Long, currentSession: String): Future[Unit] = Profiler(s"$clazz.clearOtherSessions") {
    for {
      sessions <- async {
        sessionRepo.selectByUser(username, toTime.toSome, excludeSessionId = Seq(currentSession))
      }
      _ = _logoutAll(sessions)
    } yield {}
  }

  private def _logoutAll(sessions: Seq[Session]): Unit = Profiler(s"$clazz._logoutAll") {

    FutureUtils.processUntilEmpty[Session, Unit](sessions, session => {
      logout(session.sessionId.get)
    })

  }

  override def getActiveUserByPhoneWithPassState(normPhone: String): Future[(Option[Customer], Int)] = Profiler(s"$clazz.getActiveUserByPhoneWithPassState") {
    async {
      customerDAO.selectUserByPhoneWithPassState(normPhone, status = UserStatus.Active)
    }
  }

  override def getActiveUserByEmailWithPassState(email: String, withNormalize: Boolean): Future[(Option[Customer], Int)] = Profiler(s"$clazz.getActiveUserByEmailWithPassState") {
    async {
      customerDAO.selectUserByEmailWithPassState(email, status = UserStatus.Active, withNormalize)
    }
  }
}