package caas.repository

import java.util.concurrent.TimeUnit

import com.google.common.cache.{CacheBuilder, CacheLoader}
import com.twitter.cache.guava.GuavaCache

/**
 * <AUTHOR>
 */
abstract class GuavaLRUCache[K <: Object, V <: Object](maxSize: Long, expiredTimeInMs: Long) {
  protected val cacheLoader = new CacheLoader[K, V] {
    override def load(key: K): V = loadData(key)
  }
  val cache = CacheBuilder.newBuilder()
    .expireAfterWrite(expiredTimeInMs, TimeUnit.MILLISECONDS)
    .maximumSize(maxSize).build[K, V](cacheLoader)

  def loadData(key: K): V

  def getIfPresent(key: K): Option[V] = Option(cache.getIfPresent(key))

  def get(key: K): Option[V] = {
    cache.getIfPresent(key) match {
      case null => forceGet(key)
      case x => Some(x)
    }
  }

  def forceGet(key: K): Option[V] = Option(put(key, cacheLoader.load(key)))

  def put(k: K, v: V): V = {
    if (v != null) {
      cache.put(k, v)
    }
    v
  }

  def delete(k: K): Unit = {
    cache.invalidate(k)
  }
}

abstract class AsyncGuavaLRUCache[K <: Object, V <: Object](maxSize: Long, expiredTimeInMs: Long) {

  import java.util.concurrent.TimeUnit

  import com.google.common.cache.{LoadingCache, _}
  import com.twitter.util.{Future, Return, Throw}

  protected val cacheLoader = new CacheLoader[K, Future[V]] {
    override def load(key: K): Future[V] = loadData(key)
  }

  def loadData(key: K): Future[V]

  private val gCache: LoadingCache[K, Future[V]] = CacheBuilder.newBuilder()
    .maximumSize(maxSize)
    .expireAfterWrite(expiredTimeInMs, TimeUnit.MILLISECONDS)
    .build[K, Future[V]](cacheLoader)

  val cache = GuavaCache.fromLoadingCache(gCache)

  def get(key: K): Future[V] = {
    gCache.getIfPresent(key) match {
      case null => cache(key)
      // reload in case Future.exception to ignore cache exception
      case x => x.rescue { case _: Throwable =>
        gCache.invalidate(key)
        cache(key)
      }
    }
  }

  // return some if result already cached, in other hand return none.
  def getIfPresent(key: K): Future[Option[V]] = gCache.getIfPresent(key) match {
    case null => Future.value(None)
    case x => x.transform({
      case Return(v) => Future.value(Some(v))
      case Throw(_) => Future.value(None)
    })
  }

  def getAllPresent(keys: Seq[K]): Future[Map[K, V]] = {
    Future.collect(keys.map(k => getIfPresent(k).map(_.map(v => k -> v)))).map(_.flatten.toMap)
  }

  def set(key: K, value: Future[V]): Future[Option[V]] = {
    value.map({
      case x if x != null =>
        gCache.put(key, value)
        Some(x)
      case _ => None
    })
  }

  def forceGet(key: K): Future[Option[V]] = {
    set(key, cacheLoader.load(key))
  }

  def delete(k: K): Unit = {
    gCache.invalidate(k)
  }
}