package caas.repository

import caas.domain.{Session, SessionRepository}
import com.twitter.inject.Logging
import org.apache.shiro.session.mgt.SimpleSession
import org.apache.shiro.session.mgt.eis.AbstractSessionDAO
import org.apache.shiro.subject.PrincipalCollection
import org.apache.shiro.subject.support.DefaultSubjectContext
import vn.vhm.common.domain.Implicits._
import vn.vhm.common.domain.OptionImplicits._
import vn.vhm.common.domain.profiling.Profiler
import vn.vhm.common.util.Utils

import java.io.{ByteArrayInputStream, ByteArrayOutputStream, ObjectInputStream, ObjectOutputStream}
import java.time.Instant
import java.util.{Base64, Date}
import java.{io, util}
import javax.inject.{Inject, Singleton}
import scala.collection.JavaConverters._
import scala.concurrent.duration.DurationInt

/**
 * <AUTHOR>
 */

@Singleton
case class MySqlSessionDAO @Inject()(sessionRepo: SessionRepository) extends AbstractSessionDAO with Logging {

  private val maxSize = 1000
  private val expiredTimeInMs = 1.hour.toMillis
  private val clazz = this.getClass.getCanonicalName

  private val sessionCache = new GuavaLRUCache[String, org.apache.shiro.session.Session](maxSize, expiredTimeInMs) {
    override def loadData(key: String) = Profiler(s"$clazz.sessionCache#load") {
      sessionRepo.select(Session(sessionId = key.toSome)).flatMap(s => {
        deserialize(s)
      }).orNull
    }
  }

  override def doCreate(session: org.apache.shiro.session.Session): String = {
    val sessionId = generateSessionId(session).asInstanceOf[String]
    assignSessionId(session, sessionId)
    saveSession(sessionId, session)
    sessionCache.put(sessionId, session)
    sessionId
  }

  override def doReadSession(serializable: io.Serializable): org.apache.shiro.session.Session = {
    val sessionId = serializable.asInstanceOf[String]
    Profiler(s"$clazz.sessionCache#readOrLoad") {
      sessionCache.get(sessionId).orNull
    }
  }

  private def getUsername(session: org.apache.shiro.session.Session): Option[String] = {
    Option(session.getAttribute(DefaultSubjectContext.PRINCIPALS_SESSION_KEY))
      .map(_.asInstanceOf[PrincipalCollection].getPrimaryPrincipal.asInstanceOf[String])
  }

  private def saveSession(sessionId: String, session: org.apache.shiro.session.Session): AnyVal = {
    try {
      sessionRepo.insertOrUpdate(Session(
        sessionId = sessionId.toSome,
        userId = getUsername(session),
        createdTime = session.getStartTimestamp.getTime.toSome,
        lastAccessTime = session.getLastAccessTime.getTime.toSome,
        timeout = session.getTimeout.toSome,
        serialize = serialize(session).toSome
      ))
    } catch {
      case e: Exception => error("Failed when saveSession", e)
    }
  }

  override def update(session: org.apache.shiro.session.Session): Unit = {
    val sessionId = session.getId.asInstanceOf[String]

    async {
      try {
        if (!sessionRepo.update(Session(
          sessionId = sessionId.toSome,
          userId = getUsername(session),
          lastAccessTime = session.getLastAccessTime.getTime.toSome,
          timeout = session.getTimeout.toSome,
          serialize = serialize(session).toSome
        ))) sessionCache.delete(sessionId)
      } catch {
        case _: Exception => sessionCache.delete(sessionId)
      }
    }
  }

  override def delete(session: org.apache.shiro.session.Session): Unit = {
    val sessionId = session.getId.asInstanceOf[String]

    sessionRepo.delete(Session(sessionId = sessionId.toSome))
    sessionCache.delete(sessionId)
  }

  override def getActiveSessions: util.Collection[org.apache.shiro.session.Session] = {
    sessionRepo.selectList(0, 100, sortFields = Seq((Session.LAST_ACCESS_TIME, false)))
      .flatMap(_.serialize.map(deserialize)).asJavaCollection
  }

  private def serialize(session: org.apache.shiro.session.Session): String = {
    try {
      Utils.using(new ByteArrayOutputStream()) {
        bos =>
          Utils.using(new ObjectOutputStream(bos)) {
            oos =>
              oos.writeObject(session)
              Base64.getUrlEncoder.encodeToString(bos.toByteArray)
          }
      }
    } catch {
      case e: Exception =>
        //        error(s"serialize(${session.getId}): ${e.getClass.getCanonicalName} - ${e.getMessage}")
        throw new RuntimeException("serialize session error", e)
    }
  }

  private def deserialize(sessionStr: String): org.apache.shiro.session.Session = {
    try {
      Utils.using(new ByteArrayInputStream(Base64.getUrlDecoder.decode(sessionStr))) {
        bis =>
          Utils.using(new ObjectInputStream(bis)) {
            ios =>
              ios.readObject().asInstanceOf[org.apache.shiro.session.Session]
          }
      }
    } catch {
      case e: Exception =>
        //        error(s"deserialize($sessionStr): ${e.getClass.getCanonicalName} - ${e.getMessage}", e)
        throw new RuntimeException("deserialize session error", e)
    }
  }

  private def deserialize(session: Session): Option[org.apache.shiro.session.Session] = Profiler(s"$clazz.deserialize-Obj"){
    session.serialize.map(s => {
      try {
        Profiler(s"$clazz.deserialize-Obj-normal") {
          deserialize(s)
        }
      } catch {
        case _: Exception =>
          Profiler(s"$clazz.deserialize-Obj-inEx") {
            val ss = new SimpleSession()
            ss.setId(session.sessionId.get)
            ss.setTimeout(session.timeout.get)
            ss.setStartTimestamp(Date.from(Instant.ofEpochMilli(session.createdTime.get)))
            ss.setLastAccessTime(Date.from(Instant.ofEpochMilli(session.lastAccessTime.get)))
            ss
          }
      }
    })

  }

}
