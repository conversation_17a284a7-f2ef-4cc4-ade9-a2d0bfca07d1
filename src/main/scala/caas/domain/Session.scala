package caas.domain

import profile.util.VHMConfig
import vn.vhm.common.domain.Implicits._
import vn.vhm.common.util.Utils
import vn.vhm.jdbc.postgres.PostgreSqlDAO
import vn.vhm.jdbc.{JdbcRecord, SqlFieldMissing}

import javax.sql.DataSource

/**
 * <AUTHOR>
 */
object Session {
  val TBL_NAME = "session"

  def field(f: String): String = f

  val SESSION_ID = field("session_id")
  val USER_ID = field("user_id")
  val CREATED_TIME = field("created_time")
  val LAST_ACCESS_TIME = field("last_access_time")
  val TIMEOUT = field("timeout")
  val SERIALIZE = field("serialize")

  val primaryKeys = Seq(SESSION_ID)
  val fields = Seq(SESSION_ID, USER_ID, CREATED_TIME, LAST_ACCESS_TIME, TIMEOUT, SERIALIZE)
}

import caas.domain.Session._

case class Session(
                    var sessionId: Option[String] = None,
                    var userId: Option[String] = None,
                    var createdTime: Option[Long] = None,
                    var lastAccessTime: Option[Long] = None,
                    var timeout: Option[Long] = None,
                    var serialize: Option[String] = None
                  ) extends JdbcRecord {
  override def getPrimaryKeys(): Seq[String] = primaryKeys

  override def getFields(): Seq[String] = fields

  override def setValues(field: String, value: Any): Unit = field match {
    case SESSION_ID => sessionId = value.asOpt
    case USER_ID => userId = value.asOpt
    case CREATED_TIME => createdTime = value.asOpt
    case LAST_ACCESS_TIME => lastAccessTime = value.asOpt
    case TIMEOUT => timeout = value.asOpt
    case SERIALIZE => serialize = value.asOpt
    case _ => throw SqlFieldMissing(field, value)
  }

  override def getValue(field: String): Option[Any] = field match {
    case SESSION_ID => sessionId
    case USER_ID => userId.map(_.toLong)
    case CREATED_TIME => createdTime
    case LAST_ACCESS_TIME => lastAccessTime
    case TIMEOUT => timeout
    case SERIALIZE => serialize
    case _ => throw SqlFieldMissing(field)
  }
}

case class SessionRepository(ds: DataSource) extends PostgreSqlDAO[Session] {
  override def createRecord(): Session = Session()

  override val table: String = Session.TBL_NAME

  override val sqlLogger = VHMConfig.sqlLogger.getOrElse(super.sqlLogger)

  private val fieldsAsString = fields.mkString(",")

  def existSession(sessionId: String): Boolean = {
    val query = s"SELECT 1 FROM ${Session.TBL_NAME} WHERE ${Session.SESSION_ID}=?"
    execute(ds) {
      conn =>
        Utils.using(conn.prepareStatement(query)) {
          ps =>
            ps.setString(1, sessionId)
            Utils.using(ps.executeQuery()) {
              rs => rs.next()
            }
        }
    }
  }

  def selectByUser(username: String): Seq[Session] = {
    import vn.vhm.jdbc.JdbcImplicits._
    val query =
      s"""
         |SELECT $fieldsAsString
         |FROM $table
         |WHERE $USER_ID=?
         |""".stripMargin
    execute(executeQuery(query, Seq(username.toLong))(rs => {
      rs.map(rs => parseResult(rs))
    })(_))
  }


  def selectByUser(username: String,
                   toTime: Option[Long],
                   excludeSessionId: Seq[String]): Seq[Session] = {
    import vn.vhm.jdbc.JdbcImplicits._
    val conditions = scala.collection.mutable.ListBuffer[String](s"$USER_ID = ?")
    val conditionsValue = scala.collection.mutable.ListBuffer[Any](username.toLong)

    excludeSessionId.foreach(value => {
      conditions.append(s"$SESSION_ID != ?")
      conditionsValue.append(value)
    })

    toTime.foreach(toTime => {
      conditions.append(s"$CREATED_TIME < ?")
      conditionsValue.append(toTime)
    })

    val query =
      s"""
         |SELECT $fieldsAsString
         | FROM $table
         | WHERE (${conditions.mkString(" AND ")})
         |""".stripMargin

    execute(executeQuery(query, conditionsValue)(rs => {
      rs.map(rs => parseResult(rs))
    })(_))
  }

  def deleteSessionByUser(username: String, toTime: Option[Long], excludeSessionId: Seq[String]): Seq[Session] = {
    import vn.vhm.jdbc.JdbcImplicits._

    val conditions = scala.collection.mutable.ListBuffer[String](s"$USER_ID = ?")
    val conditionsValue = scala.collection.mutable.ListBuffer[Any](username.toLong)

    toTime.foreach(toTime => {
      conditions.append(s"$CREATED_TIME < ?")
      conditionsValue.append(toTime)
    })

    if (excludeSessionId.nonEmpty) {
      conditions.append(s"$SESSION_ID NOT IN (${excludeSessionId.map(_ => "?").mkString(",")})")
      conditionsValue.appendAll(excludeSessionId)
    }

    val query =
      s"""
         |DELETE FROM $table
         | WHERE (${conditions.mkString(" AND ")})
         |""".stripMargin

    execute(executeQuery(query, conditionsValue)(rs => {
      rs.map(rs => parseResult(rs))
    })(_))
  }

}
