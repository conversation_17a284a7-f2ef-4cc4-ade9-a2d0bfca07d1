package caas.domain

import org.apache.commons.codec.digest.HmacUtils
import profile.domain.LoginType

/**
 * <AUTHOR>
 *         created on 9/19/16.
 */

object OAuthInfo {
  private val PASS_SECRET_KEY = "nQK3GlaT412PasJV7Qtj40acZcbc7yc0"
}

case class OAuthInfo(oauthType: String, username: String, id: Option[String]) {

  private val _oauthType = LoginType.get(oauthType)
  if (_oauthType != LoginType.OAUTH_FACEBOOK &&
    _oauthType != LoginType.OAUTH_GOOGLE &&
    _oauthType != LoginType.OAUTH_APPLE) throw new Exception(s"Unsupported oauth $oauthType")

  def getPass: String = HmacUtils.hmacSha1Hex(username + OAuthInfo.PASS_SECRET_KEY, OAuthInfo.PASS_SECRET_KEY)

}
