package profile.controller.http

import com.twitter.finatra.http.Controller
import com.twitter.inject.Logging
import com.twitter.util.Future
import profile.controller.http.filter.user.CmsUserPermissionFilter
import profile.controller.http.request._
import profile.domain.entity.RegisterChannelType
import profile.domain.response.BaseResponse
import profile.exception.InvalidCredentialException
import profile.service.{AuthenJwtService, HttpQuotaService, ProfileService}
import vn.vhm.common.domain.OptionImplicits.OptionImplicitAny
import vn.vhm.common.domain.profiling.Profiler

import javax.inject.Inject
import scala.concurrent.duration.DurationInt

/**
 * <AUTHOR>
 */
class AdminUserController @Inject()(
                                     authService: AuthenJwtService,
                                     quotaService: HttpQuotaService,
                                     profileService: ProfileService,
                                   ) extends Controller with Logging {

  private val apiVersion = "v1"

  filter[CmsUserPermissionFilter]
    .post(s"/admin/$apiVersion/customers/register") {
      request: RegisterByAdminRequest => {
        Profiler(s"/admin/$apiVersion/customers/register HTTP-POST") {
          authService.registerByAdmin(request.request, request.toRegisterByAdminData)
            .map(data => response.ok(BaseResponse(code = 0, data = Some(data))))
        }
      }
    }

  filter[CmsUserPermissionFilter]
    .post(s"/admin/$apiVersion/customers/register/confirm") {
      req: RegisterByAdminRequest => {
        Profiler(s"/admin/$apiVersion/customers/register/confirm HTTP-POST") {
          for {
            result <- {
              if (req.phone.exists(_.nonEmpty)) Profiler(s"HTTP-POST /admin/$apiVersion/customers/register/confirm - phone") {
                quotaService.calcQuotaPhoneVerifyCode(req, "register-phone-verify", 5.minutes, 5) {
                  authService.confirmRegisterByAdmin(req.toRegisterByAdminData, RegisterChannelType.PHONE)
                    .map(data => response.ok(BaseResponse(code = 0, data = Some(data))))
                }
              }
              else if (req.email.exists(_.nonEmpty)) Profiler(s"HTTP-POST /admin/$apiVersion/customers/register/confirm - email") {
                quotaService.calcQuotaEmailVerifyCode(req, "register-email-verify", 5.minutes, 5) {
                  authService.confirmRegisterByAdmin(req.toRegisterByAdminData, RegisterChannelType.EMAIL)
                    .map(data => response.ok(BaseResponse(code = 0, data = Some(data))))
                }
              }
              else Profiler(s"HTTP-POST /admin/$apiVersion/customers/register/confirm - unknown") {
                Future.exception(InvalidCredentialException(message = "register verify confirm info invalid"))
              }
            }
          } yield result
        }
      }
    }

  filter[CmsUserPermissionFilter]
    .put(s"/admin/$apiVersion/customers/:id/reinit_pnl_mapping") {
      req: ScanPnlMappingByAdminRequest => {
        Profiler(s"/admin/$apiVersion/customers/:id/reinit_pnl_mapping HTTP-PUT") {
          profileService.reInitPnlMapping(req.id, req.cmsUsername.toSome, req.requestBy.toSome, req.requestReason.toSome)
            .map(optResult => response.ok(BaseResponse(code = 0, data = Some(optResult))))
        }
      }
    }

  filter[CmsUserPermissionFilter]
    .post(s"/admin/$apiVersion/customers/:id/deactivate") {
      req: DeactivateByAdminRequest => {
        Profiler(s"/admin/$apiVersion/customers/:id/deactivate HTTP-POST") {
          authService.deactivateUserByAdmin(req)
            .map(_ => response.ok(BaseResponse(0)))
        }
      }
    }

}
