package profile.controller.http

import com.twitter.finatra.http.Controller
import com.twitter.finatra.http.annotations.RouteParam
import com.twitter.finatra.validation.{MethodValidation, ValidationResult}
import com.twitter.inject.Logging
import profile.controller.http.filter.common.{InternalIdentityVerificationSecretKeyFilter, InternalSecretKeyFilter}
import profile.domain.customer.{CustomerIdentityDocType, CustomerIdentityVerifyStatus}
import profile.service.ProfileService
import vn.vhm.common.domain.profiling.Profiler
import vn.vhm.common.exception.{NotFoundException, UnsupportedException}

import javax.inject.Inject

/**
 * <AUTHOR>
 */
class InternalUserManagementController @Inject()(
                                                  profileService: ProfileService,
                                                ) extends Controller with Logging {

  filter[InternalSecretKeyFilter]
    .put("/internal/user/:id/rescan_pnl_mapping") {
      request: InternalUserScanPnlMappingRequest => {
        Profiler("HTTP-PUT /internal/user/:id/rescan_pnl_mapping") {
          profileService.getPnlMappings(request.id, true, Nil)
            .map(optResult => Map("result" -> optResult))
        }
      }
    }

  filter[InternalSecretKeyFilter]
    .put("/internal/user/:id/reinit_pnl_mapping") {
      request: InternalUserScanPnlMappingRequest => {
        Profiler("HTTP-PUT /internal/user/:id/reinit_pnl_mapping") {
          profileService.reInitPnlMapping(request.id)
            .map(optResult => Map("result" -> optResult))
        }
      }
    }

  filter[InternalIdentityVerificationSecretKeyFilter]
    .put(s"/internal/user/:customer_id/verify-customer-identity") {
      request: InternalIdentityVerificationCustomerRequest => {
        Profiler("HTTP-PUT /internal/user/:customer_id/verify-customer-identity") {
          profileService.updateIdentifyVerifyStatus(
              request.customerId,
              CustomerIdentityVerifyStatus.get(request.verifyStatus),
              request.verifyStatusMessage.getOrElse(""),
              request.identityType, request.identityDocumentVerifyId
            )
            .map(_ => Map("result" -> true))
        }
      }
    }

  filter[InternalSecretKeyFilter]
    .post("/internal/re-normalize-user-emails") {
      request: ReNormalizeUserEmailsRequest => {
        Profiler("HTTP-POST /internal/re-normalize-user-emails") {
          profileService.normalizeEmailsInRange(request.batchSize, request.fromId, request.toId)
            .map {
              case count if count >= 0 => 
                Map("result" -> true, "processed_count" -> count)
              case -1 => 
                Map("result" -> false, "message" -> "Error when execute normalize email")
              case _ => 
                Map("result" -> false, "message" -> "Email normalization is already running, skipping")
            }
        }
      }
    }

  filter[InternalSecretKeyFilter]
    .post("/internal/re-sync-first-last-login") {
      request: ReSyncLoginTimeUsersRequest => {
        Profiler("HTTP-POST /internal/re-sync-first-last-login") {
          profileService.syncLoginTimeInRange(request.batchSize, request.fromId, request.toId)
            .map {
              case count if count >= 0 =>
                Map("result" -> true, "processed_count" -> count)
              case -1 =>
                Map("result" -> false, "message" -> "Error when execute sync first last login")
              case _ =>
                Map("result" -> false, "message" -> "Sync first last login is already running, skipping")
            }
        }
      }
    }

  filter[InternalSecretKeyFilter]
    .post("/internal/migrate-identity-verification-data") {
      request: MigrateIdentityVerificationDataRequest => {
        Profiler("HTTP-POST /internal/migrate-identity-verification-data") {
          profileService.migrateIdentityVerificationDataInRange(request.batchSize, request.fromId, request.toId)
            .map {
              case count if count >= 0 =>
                Map("result" -> true, "processed_count" -> count)
              case -1 =>
                Map("result" -> false, "message" -> "Error when execute identity verification data migration")
              case _ =>
                Map("result" -> false, "message" -> "Identity verification data migration is already running, skipping")
            }
        }
      }
    }

}

case class InternalUserScanPnlMappingRequest(
                                             @RouteParam id: String
                                           )

case class InternalIdentityVerificationCustomerRequest(
                                                        @RouteParam customerId: String,
                                                        verifyStatus: String, // CustomerIdentityVerifyStatus
                                                        verifyStatusMessage: Option[String] = None,
                                                        identityType: String, // CustomerIdentityDocType
                                                        identityDocumentVerifyId: Long
                                                      ) {
  @MethodValidation
  def checkValid: ValidationResult = try {

    CustomerIdentityVerifyStatus.opt(verifyStatus) match {
      case Some(v) if v == CustomerIdentityVerifyStatus.REJECTED || v == CustomerIdentityVerifyStatus.VERIFIED =>
      case Some(v) =>
        throw UnsupportedException(s"Unsupported verify_status=${v.toString}")
      case _ =>
        throw NotFoundException(s"Invalid verify_status=$verifyStatus")
    }

    if (CustomerIdentityDocType.opt(identityType).isEmpty) throw NotFoundException(s"Invalid identity_type=$identityType")

    ValidationResult.Valid()
  } catch {
    case e: Exception => ValidationResult.Invalid(e.getMessage)
  }
}

case class ReNormalizeUserEmailsRequest(
                                             batchSize: Int,
                                             fromId: Long,
                                             toId: Long
                                           ) {
  @MethodValidation
  def checkValid: ValidationResult = try {
    if (batchSize <= 0) {
      throw UnsupportedException("batch_size must be greater than 0")
    }

    if (fromId < -1) {
      throw UnsupportedException("from_id must be greater than or equal to -1 (where -1 means no lower limit)")
    }

    if (toId < -1) {
      throw UnsupportedException("to_id must be greater than or equal to -1 (where -1 means no upper limit)")
    }

    if (toId > 0 && toId < fromId) {
      throw UnsupportedException("to_id must be greater than or equal to from_id")
    }

    ValidationResult.Valid()
  } catch {
    case e: Exception => ValidationResult.Invalid(e.getMessage)
  }
}

case class ReSyncLoginTimeUsersRequest(
                                             batchSize: Int,
                                             fromId: Long,
                                             toId: Long
                                           ) {
  @MethodValidation
  def checkValid: ValidationResult = try {
    if (batchSize <= 0) {
      throw UnsupportedException("batch_size must be greater than 0")
    }

    if (fromId < -1) {
      throw UnsupportedException("from_id must be greater than or equal to -1 (where -1 means no lower limit)")
    }

    if (toId < -1) {
      throw UnsupportedException("to_id must be greater than or equal to -1 (where -1 means no upper limit)")
    }

    if (toId > 0 && toId < fromId) {
      throw UnsupportedException("to_id must be greater than or equal to from_id")
    }

    ValidationResult.Valid()
  } catch {
    case e: Exception => ValidationResult.Invalid(e.getMessage)
  }
}

case class MigrateIdentityVerificationDataRequest(
                                                   batchSize: Int,
                                                   fromId: Long,
                                                   toId: Long
                                                 ) {
  @MethodValidation
  def checkValid: ValidationResult = try {
    if (batchSize <= 0) {
      throw UnsupportedException("batch_size must be greater than 0")
    }

    if (fromId < -1) {
      throw UnsupportedException("from_id must be greater than or equal to -1 (where -1 means no lower limit)")
    }

    if (toId < -1) {
      throw UnsupportedException("to_id must be greater than or equal to -1 (where -1 means no upper limit)")
    }

    if (toId > 0 && toId < fromId) {
      throw UnsupportedException("to_id must be greater than or equal to from_id")
    }

    ValidationResult.Valid()
  } catch {
    case e: Exception => ValidationResult.Invalid(e.getMessage)
  }
}
