package profile.controller.http

import com.twitter.finagle.http.Request
import com.twitter.finatra.http.Controller
import com.twitter.inject.Logging
import profile.controller.http.filter.user.UserContext._
import profile.controller.http.filter.user.UserSignedInFilter
import profile.controller.http.request._
import profile.domain.request.{SetOAuthPasswordRequest, UpdateAvatarRequest, UpdateUserRequest}
import profile.domain.response.{BaseResponse, UserResponse}
import profile.service.{AuthorizeService, HttpQuotaService, ProfileService}
import profile.util.CustomUtils
import vn.vhm.common.domain.OptionImplicits.OptionImplicitAny
import vn.vhm.common.domain.profiling.Profiler

import javax.inject.Inject
import scala.concurrent.duration.DurationInt

/**
 * <AUTHOR>
 */
class UserController @Inject()(
                                authorService: AuthorizeService,
                                profileService: ProfileService,
                                quotaService: HttpQuotaService
                              ) extends Controller with Logging {

  // @formatter:off
  /**
   * @api {get} /profile Get current profile
   * @apiVersion 1.0.0
   * @apiName GetProfile
   * @apiGroup User
   * @apiHeader {String} Authorization Authorization with Bearer token
   * @apiHeaderExample {json} Header-Example:
   *     {
   *       "Authorization": "Bearer eyJhbGciOiJS..."
   *     }
   * @apiSuccess {Int} code success: 0, failed: < 0
   * @apiSuccess {Object} user_info
   * @apiSuccess {Object} user_profile
   */
  // @formatter:on
  filter[UserSignedInFilter]
    .get("/user/profile") {
      request: Request => {
        Profiler("HTTP-GET /user/profile") {
          profileService.get(request.getUsername(), rescanPnl = true)
            .map(userObj => response.ok(UserResponse(
              code = 0,
              userInfo = userObj.getAuth,
              userProfile = userObj.toDto.toSome,
            )))
        }
      }
    }

  // @formatter:off
  /**
   * @api {get} /pnl-mappings Get list mapping from PnLs for current user
   * @apiVersion 1.0.0
   * @apiName GetPnlMapping
   * @apiGroup User
   * @apiHeader {String} Authorization Authorization with Bearer token
   * @apiHeaderExample {json} Header-Example:
   *     {
   *       "Authorization": "Bearer eyJhbGciOiJS..."
   *     }
   * @apiSuccess {Int} code success: 0, failed: < 0
   * @apiSuccess {Object} data
   */
  // @formatter:on
  filter[UserSignedInFilter]
    .get("/user/pnl-mappings") {
      request: Request => {
        Profiler("HTTP-GET /user/pnl-mappings") {
          profileService.getPnlMappings(request.getUsername(), forceFetch = false, Nil)
        }
      }
    }

  // @formatter:off
  /**
   * @api {post} /pnl-mappings/confirm Confirm mapping from suggestion
   * @apiVersion 1.0.0
   * @apiName GetPnlMappingSuggestion
   * @apiGroup User
   * @apiHeader {String} Authorization Authorization with Bearer token
   * @apiHeaderExample {json} Header-Example:
   *     {
   *       "Authorization": "Bearer eyJhbGciOiJS..."
   *     }
   * @apiSuccess {Int} code success: 0, failed: < 0
   * @apiSuccess {Object} data
   */
  // @formatter:on
  filter[UserSignedInFilter]
    .post("/user/pnl-mappings/confirm") {
      request: Request => {
        Profiler("HTTP-POST /user/pnl-mappings/confirm") {
          profileService.confirmMappingToPnl(request.getUsername())
        }
      }
    }

  // @formatter:off
  /**
   * @apiIgnore
   * @api {post} /user/set-oauth-password Set OAuth password
   * @apiVersion 1.0.0
   * @apiName SetOAuthPassword
   * @apiGroup User
   *
   * @apiParam (Request body) {String} password Phải được mã hóa phía client
   *
   * @apiSuccess {Int} code success: 0, failed: < 0
   *
   */
  // @formatter:on
  filter[UserSignedInFilter]
    //    .filter[UserOAuthDefaultFilter]
    .post("/user/set-oauth-password") {
      req: SetOAuthPasswordRequest => {
        //        userAuthorService.resetPassword(req.request.user.username.get, req.password).map(_ => response.ok)
        ???
      }
    }

  // @formatter:off
  /**
   * @api {put} /change-password Update password
   * @apiVersion 1.0.0
   * @apiName ChangePassword
   * @apiGroup User
   *
   * @apiParam (Request body) {String} old_password
   * @apiParam (Request body) {String} new_password
   * @apiHeader {String} Authorization Authorization with Bearer token
   * @apiHeaderExample {json} Header-Example:
   *     {
   *       "Authorization": "Bearer eyJhbGciOiJSUzI1Ni..."
   *     }
   *
   * @apiSuccess {Int} code success: 0, failed: < 0
   * @apiDescription
   * Một số lỗi:
   *   + error = `invalid_credential` password cũ sai
   */
  // @formatter:on
  filter[UserSignedInFilter]
    .put("/user/change-password") {
      req: ChangePasswordRequest => {
        authorService.changePassword(
          req.request.user.username.get,
          req.oldPasswordHashed,
          req.newPasswordHashed
        ).map(_ => response.ok(BaseResponse(0)))
      }
    }

  // @formatter:off
  /**
   * @apiIgnore
   * @api {post} /user/update-avatar Update avatar
   * @apiVersion 1.0.0
   * @apiName UpdateAvatar
   * @apiGroup User
   * @apiParam (Request body) {MultiPart} upload
   *                                        + name=file
   *                                        + contentType: image/jpeg, image/png
   *                                        + content:
   * @apiSuccess {Int} code success: 0, failed: < 0
   * @apiSuccess {String} data avatar url
   *
   */
  // @formatter:on
  filter[UserSignedInFilter]
    .post("/user/update-avatar") {
      req: UpdateAvatarRequest => {
        Profiler("/user/update-avatar HTTP-POST") {

          quotaService.calcQuotaUserMac(req, "user-update-avatar", 1.hour, 5, false) {
              CustomUtils.exec {
                authorService.updateAvatar(req.req.user.username.get, req.file)
                  .map(url => response.ok(BaseResponse(0, data = Some(url))))
              }
            }
            .respond(_ => req.close())

        }
      }
    }

  // @formatter:off
  /**
   * @api {put} /user/update Update profile info
   * @apiVersion 1.0.0
   * @apiName UpdateProfile
   * @apiGroup User
   *
   * @apiParam (Request body) {String} [first_name]
   * @apiParam (Request body) {String} [last_name]
   * @apiParam (Request body) {String} [full_name]
   * @apiParam (Request body) {String} [gender] value in "male", "female", "other"
   * @apiParam (Request body) {String} [birthday] value in format yyyy-MM-dd
   *
   * @apiHeader {String} Authorization Authorization with Bearer token
   * @apiHeaderExample {json} Header-Example:
   *     {
   *       "Authorization": "Bearer eyJhbGciOiJSUzI1Ni..."
   *     }
   *
   * @apiSuccess {Int} code success: 0, failed: < 0
   *
   */
  // @formatter:on
  filter[UserSignedInFilter]
    .put("/user/update") {
      req: UpdateUserRequest => {
        Profiler("/user/update HTTP-PUT") {
          authorService.updateUserProfile(req.request.user.username.get, req)
            .map(_ => response.ok(BaseResponse(0)))
        }
      }
    }

  // @formatter:off
  /**
   * @apiIgnore
   * @api {post} /user/verify-email/send-code Send code to verify current email value
   * @apiVersion 1.0.0
   * @apiName SendCodeToVerifyEmail
   * @apiGroup User
   *
   * @apiParam (Request body) {String} [token_captcha]
   * @apiHeaderExample {json} Header-Example:
   *     {
   *       "Authorization": "Bearer eyJhbGciOiJSUzI1Ni..."
   *     }
   *
   * @apiSuccess {Int} code success: 0, failed: < 0
   *
   *
   */
  // @formatter:on
  filter[UserSignedInFilter]
    .post("/user/verify-email/send-code") {
      req: SendCodeToVerifyEmailReq => {
        Profiler("/user/verify-email/send-code HTTP-POST") {
          quotaService.calcQuotaUserMac(req, "user-verify-email", 30.minutes, 5, deleteIfSuccess = true) {
              authorService.sendCodeToVerifyEmail(req.request.user.username.get)
            }
            .map(_ => response.ok(BaseResponse(0)))
        }
      }
    }

  // @formatter:off
  /**
   * @apiIgnore
   * @api {post} /user/verify-email Verify current email value
   * @apiVersion 1.0.0
   * @apiName VerifyEmail
   * @apiGroup User
   *
   * @apiParam (Request body) {String} email
   * @apiParam (Request body) {String} code
   * @apiParam (Request body) {String} [token_captcha]
   * @apiHeader {String} Authorization Authorization with Bearer token
   * @apiHeaderExample {json} Header-Example:
   *     {
   *       "Authorization": "Bearer eyJhbGciOiJSUzI1Ni..."
   *     }
   * @apiSuccess {Int} code success: 0, failed: < 0
   *
   *
   */
  // @formatter:on
  filter[UserSignedInFilter]
    .post("/user/verify-email") {
      req: VerifyEmailReq => {
        Profiler("/user/verify-email HTTP-POST") {
          quotaService.calcQuotaEmailVerifyCode(req, "user-verify-email", 5.minutes, 5) {
              authorService.verifyEmail(req.request.user.username.get, req.email, req.code)
            }
            .map(_ => response.ok(BaseResponse(0)))
        }
      }
    }

  // @formatter:off
  /**
   * @api {post} /user/update-email Send code to update new email
   * @apiVersion 1.0.0
   * @apiName SendCodeToUpdateNewEmail
   * @apiGroup User
   *
   * @apiParam (Request body) {String} email
   * @apiParam (Request body) {String} [token_captcha]
   * @apiHeader {String} Authorization Authorization with Bearer token
   * @apiHeaderExample {json} Header-Example:
   *     {
   *       "Authorization": "Bearer eyJhbGciOiJSUzI1Ni..."
   *     }
   * @apiSuccess {Int} code success: 0, failed: < 0
   *
   * @apiDescription
   * Một số lỗi:
   *   + error = `already_exist_email` email đã tồn tại trong hệ thống
   *   + error = `already_exist_user_email` email đã tồn tại trong user rồi
   *   + error = `locked_error` đã bị lock
   */
  // @formatter:on
  filter[UserSignedInFilter]
    .post("/user/update-email") {
      req: SendCodeToUpdateNewEmailReq => {
        Profiler("/user/update-email HTTP-POST") {
          quotaService.calcQuotaEmailSendCode(req.request,
              CalcQuotaEmailRequest(req.email, lockInDuration = 30.minutes.toSome),
              checkQuotaCountry = true, checkQuotaIP = true
            ) {
              authorService.sendCodeToUpdateNewEmail(req.request.user.username.get, req.email)
            }
            .map(_ => response.ok(BaseResponse(0)))
        }
      }
    }

  // @formatter:off
  /**
   * @api {post} /user/update-email/verify Update new email
   * @apiVersion 1.0.0
   * @apiName UpdateNewEmail
   * @apiGroup User
   *
   * @apiParam (Request body) {String} email
   * @apiParam (Request body) {String} code
   * @apiParam (Request body) {String} [token_captcha]
   * @apiHeader {String} Authorization Authorization with Bearer token
   * @apiHeaderExample {json} Header-Example:
   *     {
   *       "Authorization": "Bearer eyJhbGciOiJSUzI1Ni..."
   *     }
   * @apiSuccess {Int} code success: 0, failed: < 0
   * @apiDescription
   * Một số lỗi:
   *   + error = `already_exist_email` email đã tồn tại trong hệ thống
   *   + error = `already_exist_user_email` email đã tồn tại trong user
   *   + error = `invalid_email_verify_code` opt không đúng
   *   + error = `locked_error` đã bị lock nhập otp
   *
   * Flow khi verify otp sai nhiều lần
   *   + error = `invalid_email_verify_code` và data trả về is_expired_code=true thì thông báo otp hết hạn
   *   + nếu error = `invalid_email_verify_code` opt không đúng sẽ trả về và kèm theo một số data để show UI
   *   + example: data = {"verify_email_code":false, "num_failed":1, "max_failed":2, "lock_in_millis":600000}
   *   + error = `locked_error` và data chứa `locked=true` nếu nhập sai otp quá số lần cho phép và sẽ trả về data để show UI
   *   + example: data = {"locked":true,"locked_until_time":1719467185599,"max_failed":2}
   */
  // @formatter:on
  filter[UserSignedInFilter]
    .post("/user/update-email/verify") {
      req: UpdateNewEmailReq => {
        Profiler("HTTP-POST /user/update-email/verify") {
          quotaService.calcQuotaEmailVerifyCode(req, "user-update-email-verify", 5.minutes, 5) {
              authorService.updateNewEmail(req.request.user.username.get, req.email, req.code)
            }
            .map(_ => response.ok(BaseResponse(0)))
        }
      }
    }

  // @formatter:off
  /**
   * @api {post} /user/update-phone Send code to update new phone
   * @apiVersion 1.0.0
   * @apiName SendCodeToUpdateNewPhone
   * @apiGroup User
   *
   * @apiParam (Request body) {String} phone
   * @apiParam (Request body) {String} [token_captcha]
   * @apiHeader {String} Authorization Authorization with Bearer token
   * @apiHeaderExample {json} Header-Example:
   *     {
   *       "Authorization": "Bearer eyJhbGciOiJSUzI1Ni..."
   *     }
   * @apiSuccess {Int} code success: 0, failed: < 0
   *
   * @apiDescription
   * Một số lỗi:
   *   + error = `already_exist_phone` phone đã tồn tại trong hệ thống
   *   + error = `already_exist_user_phone` phone đã tồn tại trong user
   *   + error = `locked_error` đã bị lock
   */
  // @formatter:on
  filter[UserSignedInFilter]
    .post("/user/update-phone") {
      req: SendCodeToUpdatePhoneReq => {
        Profiler("/user/update-phone HTTP-POST") {
          quotaService.calcQuotaPhoneSendCode(req.request,
              CalcQuotaPhoneRequest(req.normPhone, lockInDuration = 30.minutes.toSome),
              checkQuotaCountry = true, checkQuotaIP = true
            ) {
              authorService.sendCodeToUpdateNewPhone(req.request.user.username.get, req.normPhone, req.getPlatform)
            }
            .map(_ => response.ok(BaseResponse(0)))
        }
      }
    }

  // @formatter:off
  /**
   * @api {post} /user/update-phone/verify Update new phone
   * @apiVersion 1.0.0
   * @apiName UpdateNewPhone
   * @apiGroup User
   *
   * @apiParam (Request body) {String} phone
   * @apiParam (Request body) {String} code
   * @apiParam (Request body) {String} [token_captcha]
   * @apiHeader {String} Authorization Authorization with Bearer token
   * @apiHeaderExample {json} Header-Example:
   *     {
   *       "Authorization": "Bearer eyJhbGciOiJSUzI1Ni..."
   *     }
   * @apiSuccess {Int} code success: 0, failed: < 0
   * @apiDescription
   * Một số lỗi:
   *   + error = `already_exist_phone` phone đã tồn tại trong hệ thống
   *   + error = `already_exist_user_phone` phone đã tồn tại trong user
   *   + error = `invalid_phone_verify_code` opt không đúng
   *   + error = `locked_error` đã bị lock nhập otp
   *
   * Flow khi verify otp sai nhiều lần
   *   + nếu error = `invalid_phone_verify_code` và data trả về is_expired_code=true thì thông báo otp hết hạn
   *   + nếu error = `invalid_phone_verify_code` opt không đúng sẽ trả về và kèm theo một số data để show UI
   *   + example: data = {"verify_phone_code":false, "num_failed":1, "max_failed":2, "lock_in_millis":600000}
   *   + error = `locked_error` và data chứa `locked=true` nếu nhập sai otp quá số lần cho phép và sẽ trả về data để show UI
   *   + example: data = {"locked":true,"locked_until_time":1719467185599,"max_failed":2}
   */
  // @formatter:on
  filter[UserSignedInFilter]
    .post("/user/update-phone/verify") {
      req: UpdateNewPhoneReq => {
        Profiler("HTTP-POST /user/update-phone/verify") {
          quotaService.calcQuotaPhoneVerifyCode(req, "user-update-phone-verify", 5.minutes, 5) {
              authorService.updateNewPhone(req.request.user.username.get, req.normPhone, req.code)
            }
            .map(_ => response.ok(BaseResponse(0)))
        }
      }
    }

  filter[UserSignedInFilter]
    .post("/user/verify-customer-identity") {
      req: VerifyCustomerIdentityReq => {
        Profiler("HTTP-POST /user/verify-customer-identity") {

          import profile.controller.http.filter.user.UserContext.UserContextSyntax

          quotaService.calcQuotaUserMac(req, "user-verify-identity", 5.minutes, 20, false) {
            profileService.verifyIdentity(req.req.getUsername(),
              req.identityDoc, req.identityFiles, req.faceImageFile, req.stageMetadata,
              req.customAddress
            )
          }
        }
      }
    }

//  filter[UserSignedInFilter]
//    .post("/user/verify-customer-identity/status") {
//      req: VerifyCustomerIdentityReq => {
//        Profiler("HTTP-POST /user/verify-customer-identity/status") {
//
//          import profile.controller.http.filter.user.UserContext.UserContextSyntax
//
//          quotaService.calcQuotaUserMac(req, "user-verify-identity", 5.minutes, 5, false) {
//            profileService.verifyIdentity(req.req.getUsername(), req.identityDoc, req.identityFiles, req.faceImageFile, req.stageMetadata)
//          }
//        }
//      }
//    }


}
