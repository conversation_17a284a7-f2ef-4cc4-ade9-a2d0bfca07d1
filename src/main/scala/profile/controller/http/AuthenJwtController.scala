package profile.controller.http

import com.twitter.finatra.http.Controller
import com.twitter.util.{Future, Try}
import profile.controller.http.filter.user.{AuthUserParser, UserSignedInFilter}
import profile.controller.http.request._
import profile.domain.request._
import profile.domain.response.{BaseResponse, UserResponse}
import profile.exception.InvalidCredentialException
import profile.service.{Action, AuthenJwtService, CustomerIdentity, HttpQuotaService, ShieldService}
import vn.vhm.common.domain.OptionImplicits.OptionImplicitAny
import vn.vhm.common.domain.profiling.Profiler
import vn.vhm.common.exception.RCustomException
import vn.vhm.common.util.ZConfig

import javax.inject.Inject
import scala.collection.mutable
import scala.concurrent.duration.DurationInt

/**
 * <AUTHOR> 8/21/20 3:24 PM
 */
class AuthenJwtController @Inject()(
                                     authenService: AuthenJwtService,
                                     quotaService: HttpQuotaService,
                                     shieldService: ShieldService
                                   ) extends Controller {
  //region common
  private val checkRecentSendOTP = ZConfig.getBoolean("common.check_recent", default = true)
  private val mapRecentPhoneOTP = new mutable.WeakHashMap[String, Long]
  private val recentDelayTime = ZConfig.getLong("common.recent_delay_in_millis", 5000L)

  private def checkRecentSendOTP[T](cacheKey: String)(ifTrue: => Future[T])(ifFalse: => Future[T]): Future[T] = {
    if (checkRecentSendOTP) {
      val currTime = System.currentTimeMillis()
      mapRecentPhoneOTP.get(cacheKey) match {
        case Some(time) if currTime - time < recentDelayTime => ifTrue
        case _ =>
          mapRecentPhoneOTP.put(cacheKey, currTime)
          ifFalse
            .onFailure(_ => mapRecentPhoneOTP.remove(cacheKey))
            .onSuccess(_ => Try {
              mapRecentPhoneOTP.foreach(pair => if (currTime - pair._2 > recentDelayTime) mapRecentPhoneOTP.remove(pair._1))
            }.toOption)
      }
    } else ifFalse
  }

  // @formatter:off
  /**
   * @api {post} /register Register by phone or email
   * @apiVersion 1.0.0
   * @apiName Register
   * @apiGroup AuthenJwt
   * @apiParam (Request body) {String} identify BE tự detect là phone or email
   * @apiParam (Request body) {String} device_id
   * @apiParam (Request body) {String} [token_captcha]
   *
   * @apiSuccess {Boolean} otp opt=true đã gửi opt tới số phone
   * @apiSuccess {String} channel[phone, email]
   *
   * @apiDescription
   * Một số lỗi
   *   + error = `already_exist_phone` và `data.exist_phone=true` => số phone đã tồn tại trong hệ thống
   *   + error = `already_exist_email` và `data.exist_email=true` => email đã tồn tại trong hệ thống
   *   + error = `locked_error` đã bị lock
   *
   * Flow:
   *   + nếu trả về error = `already_exist_phone` hoặc error = `already_exist_email` chuyển sang màn hình đăng nhập
   *   + nếu trả về response `otp=true` đã gửi opt và chuyến sang màn hình đăng ký
   */
  // @formatter:on
  post("/user/register") {
    request: RegisterJwtRequest => {
      shieldService.protectRequest(request.getShieldDataRequest, Action.REGISTER_CHECK, request.identify, CustomerIdentity.fromIdentify(request.identify)) {
        Profiler("/user/register HTTP-POST") {
          for {
            result <- {
              if (request.isEmail) Profiler(s"HTTP-POST /user/register - email") {
                for {
                  result <- quotaService.calcQuotaEmailSendCode(request.request,
                    CalcQuotaEmailRequest(request.getEmail, lockInDuration = 30.minutes.toSome),
                    checkQuotaCountry = true, checkQuotaIP = true
                  ) {
                    checkRecentSendOTP(s"register-${request.identify}") {
                      Future.value(Map("otp" -> true, "channel" -> "email"))
                    } {
                      authenService.registerByEmail(request.getEmail, request.deviceId)
                        .map(_ => Map("otp" -> true, "channel" -> "email"))
                    }
                  }
                } yield result
              }
              else if (request.isPhone) Profiler(s"HTTP-POST /user/register - phone") {
                for {
                  result <- quotaService.calcQuotaPhoneSendCode(request.request,
                    CalcQuotaPhoneRequest(request.getNormPhone, lockInDuration = 30.minutes.toSome),
                    checkQuotaCountry = true, checkQuotaIP = true
                  ) {
                    checkRecentSendOTP(s"register-${request.identify}") {
                      Future.value(Map("otp" -> true, "channel" -> "phone"))
                    } {
                      authenService.registerByPhone(request.getNormPhone, request.deviceId, request.getPlatform)
                        .map(_ => Map("otp" -> true, "channel" -> "phone"))
                    }
                  }
                } yield result
              }
              else Profiler(s"HTTP-POST /user/register - unknown") {
                Future.exception(InvalidCredentialException(message = "user register invalid"))
              }
            }
          } yield result
        }
      }
    }
  }

  // @formatter:off
  /**
   * @api {post} /register/verify Verify register by phone or email
   * @apiVersion 1.0.0
   * @apiName VerifyRegister
   * @apiGroup AuthenJwt
   * @apiParam (Request body) {String} identify BE tự detect là phone or email
   * @apiParam (Request body) {String} code
   * @apiParam (Request body) {String} device_id
   *
   * @apiSuccess {String} [token]
   * @apiSuccess {Object} [user_auth]
   *
   * @apiDescription
   * Một số lỗi:
   *  + error = `invalid_phone_verify_code` opt không đúng
   *  + error = `invalid_email_verify_code` opt không đúng
   *  + error = `locked_error` đã bị lock nhập otp
   *
   * Flow:
   *  + nếu trả về verify_context=1 thì lấy token để verify khi gọi api /user/register/confirm
   *  + nếu trả về verify_context=2 thì lấy user_auth dùng để login
   *  + nếu trả về verify_context=3 thì lấy userProfile để fill vào form register và dùng token để verify khi gọi api [PUT] /user/onboarding/confirm
   *
   * Flow khi verify otp sai nhiều lần
   *   + nếu error = `invalid_phone_verify_code` hoặc error = `invalid_email_verify_code` và data trả về is_expired_code=true thì thông báo otp hết hạn
   *   + nếu error = `invalid_phone_verify_code` hoặc error = `invalid_email_verify_code` opt không đúng sẽ trả về và kèm theo một số data để show UI
   *   + example: data = {"num_failed":1, "max_failed":2, "lock_in_millis":600000}
   *   + error = `locked_error` và data chứa `locked=true` nếu nhập sai otp quá số lần cho phép và sẽ trả về data để show UI
   *   + example: data = {"locked":true,"locked_until_time":1719467185599,"max_failed":2}
   */
  // @formatter:on
  post("/user/register/verify") {
    req: VerifyRegisterJwtRequest => {
      Profiler("/user/register/verify HTTP-POST") {
        for {
          result <- {
            if (req.isEmail) Profiler(s"HTTP-POST /user/register/verify - email") {
              quotaService.calcQuotaEmailVerifyCode(req, "register-email-verify", 5.minutes, 5) {
                authenService.verifyRegisterByEmail(req.getEmail, req.code, req.deviceId)
              }
            }
            else if (req.isPhone) Profiler(s"HTTP-POST /user/register/verify - phone") {
              quotaService.calcQuotaPhoneVerifyCode(req, "register-phone-verify", 5.minutes, 5) {
                authenService.verifyRegisterByPhone(req.getNormPhone, req.code, req.deviceId)
              }
            }
            else Profiler(s"HTTP-POST /user/register/verify - unknown") {
              Future.exception(InvalidCredentialException(message = "register verify info invalid"))
            }
          }

          // block process next step for old mobile app no app check
          _ <- {
            if (result.verifyContext != 2) {
              shieldService.checkAppVersionSupported(req.getShieldDataRequest)
            } else {
              Future.Unit
            }
          }
        } yield result
      }
    }
  }

  // @formatter:off
  /**
   * @api {post} /user/register/confirm Confirm register by phone or email
   * @apiVersion 1.0.0
   * @apiName ConfirmRegister
   * @apiGroup AuthenJwt
   *
   * @apiParam (Request body) {String} identify
   * @apiParam (Request body) {String} token
   * @apiParam (Request body) {String} device_id
   * @apiParam (Request body) {String} first_name
   * @apiParam (Request body) {String} last_name
   * @apiParam (Request body) {String} full_name
   * @apiParam (Request body) {String} password
   *
   * @apiSuccess {Int} code success: 0, failed: < 0
   * @apiSuccess {Object} user_info
   * @apiSuccess {Object} user_profile
   * @apiSuccess {Object} jwt
   * @apiSuccess {String} jwt.token
   * @apiSuccess {String} jwt.expire_in token expire in second
   * @apiSuccess {String} jwt.refresh_token
   *
   * @apiDescription
   * Một số lỗi:
   *   + error = `already_exist_phone` và `data.exist_phone=true` => số phone đã tồn tại
   *   + error = `already_exist_email` và `data.exist_email=true` => email đã tồn tại trong hệ thống
   *   + error = `need_token` => token không đúng
   */
  // @formatter:on
  post("/user/register/confirm") {
    req: ConfirmRegisterJwtRequest => {
      shieldService.protectRequest(req.getShieldDataRequest, Action.REGISTER_CONFIRM, req.identify, CustomerIdentity.fromIdentify(req.identify)) {
        Profiler("/user/register/confirm HTTP-POST") {
          for {
            result <- {
              if (req.isEmail) Profiler(s"HTTP-POST /user/register/confirm - email") {
                for {
                  user <- authenService.confirmRegisterByEmail(req.getEmail, req.token, req.registerData)
                  result <- authenService.loginAfterRegisterSuccess(user, req.registerData)
                } yield {
                  result
                }
              }
              else if (req.isPhone) Profiler(s"HTTP-POST /user/register/confirm - phone") {
                for {
                  user <- authenService.confirmRegisterByPhone(req.getNormPhone, req.token, req.registerData)
                  result <- authenService.loginAfterRegisterSuccess(user, req.registerData)
                } yield {
                  result
                }
              }
              else Profiler(s"HTTP-POST /user/register/confirm - unknown") {
                Future.exception(InvalidCredentialException(message = "register verify confirm info invalid"))
              }
            }
          } yield {
            UserResponse(
              code = 0,
              userInfo = result.userInfo,
              userProfile = result.userProfile,
              jwt = result.jwt
            )
          }
        }
      }
    }
  }

  // Deprecated
  put("/user/register-by-pnl/confirm") {
    req: RegisterUserOnboardJwtRequest => {
      shieldService.protectRequest(req.getShieldDataRequest, Action.REGISTER_ONBOARDING, req.userId, CustomerIdentity.fromUseId(req.userId)) {
        Profiler("/user/register-by-pnl/confirm HTTP-PUT") {
          for {
            user <- authenService.registerUserOnboard(req)
            result <- authenService.loginAfterRegisterSuccess(user, req.registerData)
          } yield {
            UserResponse(
              code = 0,
              userInfo = result.userInfo,
              userProfile = result.userProfile,
              jwt = result.jwt
            )
          }
        }
      }
    }
  }

  put("/user/register/onboard") {
    req: RegisterUserOnboardJwtRequest => {
      shieldService.protectRequest(req.getShieldDataRequest, Action.REGISTER_ONBOARDING, req.userId, CustomerIdentity.fromUseId(req.userId)) {
        Profiler("/user/register/onboard HTTP-PUT") {
          for {
            user <- authenService.registerUserOnboard(req)
            result <- authenService.loginAfterRegisterSuccess(user, req.registerData)
          } yield {
            UserResponse(
              code = 0,
              userInfo = result.userInfo,
              userProfile = result.userProfile,
              jwt = result.jwt
            )
          }
        }
      }
    }
  }


  // @formatter:off
  /**
   * @api {post} /login Login
   * @apiVersion 1.0.0
   * @apiName Login
   * @apiGroup AuthenJwt
   *
   * @apiParam (Request body) {String} identify BE tự động detect là đăng nhập email hoặc phone
   * @apiParam (Request body) {String} password
   * @apiParam (Request body) {String} device_id
   * @apiParam (Request body) {String} [code]
   * @apiParam (Request body) {String} [token_captcha]
   *
   * @apiSuccess {Int} code success: 0, failed: < 0
   * @apiSuccess {Object} user_info
   * @apiSuccess {Object} user_profile
   * @apiSuccess {Object} jwt
   *
   * @apiDescription
   * Một số lỗi:
   *   + error = `not_exist_phone` số phone không tồn tại trong hệ thống
   *   + error = `invalid_phone_verify_code` opt phone không đúng
   *   + error = `not_exist_user_by_email` email không tồn tại trong hệ thống
   *   + error = `invalid_email_verify_code` opt email không đúng
   *   + error = `invalid_credential` sai user/pass
   *
   * Flow:
   *   + nếu trả về error = `invalid_credential` và data chứa `need_verify_other_device=true` thì hiển thị UI cho user nhập otp
   *   + sau khi nhập xong gọi lại api `/login` nhưng có truyền thêm code(là otp user nhập)
   *
   * Flow đăng nhập sai mật khẩu nhiều lần
   *   + error = `invalid_credential` nếu nhập sai mật khẩu và sẽ trả về và kèm theo một số data để show UI
   *   + example: data = {"invalid_credential":true,"num_login_failed":1,"max_login_failed":2,"lock_login_in_millis":600000}
   *   + error = `invalid_credential` và data chứa `exceed_login_wrong_pass_quota=true` nếu nhập sai mật khẩu quá số lần cho phép và sẽ trả về data để show UI
   *   + example: data = {"exceed_login_wrong_pass_quota":true,"locked_until_time":1719467185599,"max_login_failed":2}
   */
  // @formatter:on
  post("/user/login") {
    req: LoginBodyRequest => {
      shieldService.protectRequest(req.getShieldDataRequest, Action.LOGIN, req.identify, CustomerIdentity.fromIdentify(req.identify)) {
        Profiler("/user/login HTTP-POST") {
          for {
            result <- {
              if (req.loginType.contains("email") || req.isEmail) Profiler(s"HTTP-POST /user/login - email") {
                authenService.loginEmail(LoginByEmailRequest(req.identify, req.password, req.deviceId.getOrElse(""), req.code, req.tokenCaptcha))
              }
              else if (req.loginType.contains("phone") || req.isPhone) Profiler(s"HTTP-POST /user/login - phone") {
                authenService.loginPhone(LoginByPhoneRequest(req.identify, req.password, req.deviceId.getOrElse(""), req.code, req.getPlatform, req.tokenCaptcha))
              }
              else Profiler(s"HTTP-POST /user/login - unknown") {
                Future.exception(InvalidCredentialException(message = "login info invalid"))
              }
            }
          } yield {
            response.ok(UserResponse(
              code = 0,
              userInfo = result.userInfo,
              userProfile = result.userProfile,
              jwt = result.jwt
            ))
          }
        }
      }
    }
  }

  // @formatter:off
  /**
   * @api {post} /refresh-token Refresh access token
   * @apiVersion 1.0.0
   * @apiName RefreshAccessToken
   * @apiGroup AuthenJwt
   *
   * @apiParam (Request body) {String} refresh_token
   *
   * @apiSuccess {Int} code success: 0, failed: < 0
   * @apiSuccess {Object} user_info
   * @apiSuccess {Object} user_profile
   * @apiSuccess {Object} jwt
   */
  // @formatter:on
  post("/user/refresh-token") {
    req: RefreshTokenRequest => {
      Profiler("HTTP-POST /user/refresh-token") {
        authenService.refreshAccessToken(req.refreshToken)
          .map(result => {
            response.ok(
              UserResponse(
                code = 0,
                userInfo = result.userInfo,
                userProfile = result.userProfile,
                jwt = result.jwt
              )
            )
          })
      }
    }
  }

  // @formatter:off
  /**
   * @api {post} /logout Logout
   * @apiVersion 1.0.0
   * @apiName Logout
   * @apiGroup AuthenJwt
   *
   * @apiParam (Request body) {String} refresh_token
   *
   * @apiSuccess {Int} code success: 0, failed: < 0
   */
  // @formatter:on
  post("/user/logout") {
    req: RefreshTokenRequest => {
      Profiler("/user/logout HTTP-POST") {
        authenService.logout(req.refreshToken)
          .map(_ => response.ok(UserResponse(0, null, null)))
      }
    }
  }

  // @formatter:off
  /**
   * @api {put} /reset-password Reset password
   * @apiVersion 1.0.0
   * @apiName ResetPassword
   * @apiGroup User
   *
   * @apiParam (Request body) {String} identify BE tự động detect là đăng nhập email hoặc phone
   * @apiParam (Request body) {String} [token_captcha]
   *
   * @apiSuccess {Boolean} channel channel=phone đã gửi opt tới số phone
   * @apiSuccess {Boolean} channel channel=email đã gửi opt tới email
   *
   * @apiDescription
   * Một số lỗi
   *   + error = `locked_error` đã bị lock
   */
  put("/user/reset-password") {
    req: ResetPasswordRequest => {
      if (req.isPhone) {
        quotaService.checkQuotaPhone(req.request, needCheckExist = true, applyToExist = true,
            CalcQuotaPhoneRequest(req.getNormPhone, lockInDuration = 30.minutes.toSome), checkQuotaCountry = true, checkQuotaIP = true
          )
          .flatMap(_ => authenService.forgotPasswordByPhone(req.getNormPhone, req.getPlatform))
          .map(_ => Map("channel" -> "phone"))
      }
      else if (req.isEmail) {
        quotaService.checkQuotaEmail(req.request, needCheckExist = true, applyToExist = true,
            CalcQuotaEmailRequest(req.getEmail, lockInDuration = 30.minutes.toSome), checkQuotaCountry = true, checkQuotaIP = true
          )
          .flatMap(_ => authenService.forgotPasswordByEmail(req.identify))
          .map(_ => Map("channel" -> "email"))
      }
      else Future.exception(RCustomException("invalid_identify", "Invalid identify value, must be phone or email"))
    }
  }

  // @formatter:off
  /**
   * @api {put} /reset-password/verify Verify reset password
   * @apiVersion 1.0.0
   * @apiName VerifyResetPassword
   * @apiGroup User
   *
   * @apiParam (Request body) {String} identify
   * @apiParam (Request body) {String} code
   * @apiParam (Request body) {String} [token_captcha]
   *
   * @apiSuccess {String} token
   *
   * @apiDescription
   * Một số lỗi:
   *  + error = `invalid_phone_verify_code` opt phone không đúng
   *  + error = `invalid_email_verify_code` opt email không đúng
   *  + error = `locked_error` đã bị lock nhập otp
   *
   * Flow:
   *  + trả về token dùng để verify khi gọi api  /user/reset-password/confirm
   *
   * Flow khi verify otp sai nhiều lần:
   *   + nếu error = `invalid_phone_verify_code` hoặc error = `invalid_email_verify_code` và data trả về is_expired_code=true thì thông báo otp hết hạn
   *   + nếu error = `invalid_phone_verify_code` hoặc error = `invalid_email_verify_code` opt không đúng sẽ trả về và kèm theo một số data để show UI
   *   + example: data = {"num_failed":1, "max_failed":2, "lock_in_millis":600000}
   *   + error = `locked_error` và data chứa `locked=true` nếu nhập sai otp quá số lần cho phép và sẽ trả về data để show UI
   *   + example: data = {"locked":true,"locked_until_time":1719467185599,"max_failed":2}

   */
  // @formatter:on
  put("/user/reset-password/verify") {
    req: VerifyResetPasswordRequest => {
      if (req.isPhone) {
        quotaService.calcQuotaPhoneVerifyCode(req, "reset-password-phone-verify", 5.minutes, 5) {
            authenService.verifyForgotPasswordByPhone(req.getNormPhone, req.code)
          }
          .map(token => Map("token" -> token))
      }
      else if (req.isEmail) {
        quotaService.calcQuotaEmailVerifyCode(req, "reset-password-email-verify", 5.minutes, 5) {
            authenService.verifyForgotPasswordByEmail(req.getEmail, req.code)
          }
          .map(token => Map("token" -> token))
      }
      else Future.exception(new Exception("Invalid user"))
    }
  }

  // @formatter:off
  /**
   * @api {put} /user/reset-password/confirm confirm reset password
   * @apiVersion 1.0.0
   * @apiName ConfirmResetPassword
   * @apiGroup User
   *
   * @apiParam (Request body) {String} identify
   * @apiParam (Request body) {String} token
   * @apiParam (Request body) {String} new_password
   * @apiParam (Request body) {String} [device_id]
   *
   * @apiSuccess {String} empty {}
   *
   * @apiDescription
   * Một số lỗi:
   *   + error = `need_token` => token không đúng
   */
  // @formatter:on
  put("/user/reset-password/confirm") {
    req: ConfirmResetPasswordRequest => {
      if (req.isPhone) {
        authenService.confirmForgotPasswordByPhone(req.getNormPhone, req.getToken, req.newPassword, req.deviceId.getOrElse(""))
          .map(_ => "{}")
      }
      else if (req.isEmail) {
        authenService.confirmForgotPasswordByEmail(req.getEmail, req.getToken, req.newPassword, req.deviceId.getOrElse(""))
          .map(_ => "{}")
      }
      else Future.exception(new Exception("Invalid user"))
    }
  }


  // @formatter:off
  /**
   * @api {post} /deactivate Deactivate user
   * @apiVersion 1.0.0
   * @apiName DeactivateUser
   * @apiGroup User
   *
   * @apiParam (Request body) {String} password
   * @apiHeader {String} Authorization Authorization with Bearer token
   * @apiHeaderExample {json} Header-Example:
   *     {
   *       "Authorization": "Bearer eyJhbGciOiJSUzI1Ni..."
   *     }
   *
   * @apiSuccess {Int} code success: 0, failed: < 0
   *
   */
  // @formatter:on
  filter[AuthUserParser]
    .filter[UserSignedInFilter]
    .post("/user/deactivate") {
      req: DeactivateRequest => {
        authenService.deactivateUser(req.username, req.passwordHashed).map(_ => {
          response.ok(BaseResponse(0))
        })
      }
    }

  //  // @formatter:off
//  /**
//   * @apiIgnore
//   * @api {post} /login/oauth Login via OAuth
//   * @apiVersion 1.0.0
//   * @apiName LoginViaOAuth
//   * @apiGroup AuthenJwt
//   * @apiParam (Request body) {String} oauth_type google, facebook, apple
//   * @apiParam (Request body) {String} id
//   * @apiParam (Request body) {String} token
//   * @apiSuccess {Int} code success: 0, failed: < 0
//   * @apiSuccess {Object} user_info
//   * @apiSuccess {Object} user_profile
//   * @apiSuccess {Object} jwt
//   *
//   * Một số lỗi:
//   *   + Nếu account chưa đăng ký: code=-1 và data.need_register=true
//   *
//   */
//  // @formatter:on
  //  filter[UserLoginOAuthParser]
  //    .post("/user/login/oauth") {
  //      request: Request => {
  //        Profiler("/user/login/oauth HTTP-POST") {
  //          //          val loginOAuthReq = request.requestData[LoginOAuthReq]
  //          //          authenService.loginWithOAuth(loginOAuthReq)
  //          //            .map(result => response.ok(UserResponse(
  //          //                code = 0, userInfo = result.userInfo,
  //          //                userProfile = result.userProfile,
  //          //                session = Some(result.sessionInfo),
  //          //                jwt = result.token
  //          //              ))
  //          //              .cookie(createSessionCookie(result.sessionInfo))
  //          //              .cookie(createCdpUserCookie(result.userInfo.username))
  //          //            )
  //          ???
  //        }
  //      }
  //    }


  //  private def createSessionCookie(sessionInfo: SessionInfo): Cookie = {
  //    val cookie = new Cookie(sessionInfo.key, sessionInfo.value)
  //      .maxAge(Some(Duration(sessionInfo.timeoutInMS, TimeUnit.MILLISECONDS)))
  //      .path(Some(sessionInfo.path))
  //      .domain(Some(sessionInfo.domain))
  //      .httpOnly(true)
  //    ZConfig.getString("caas.session.same_site", "") match {
  //      case "" => cookie
  //      case x => cookie.sameSite(SameSite.fromString(x)).secure(true)
  //    }
  //  }
  //
  //  private def createCdpUserCookie(username: String): Cookie = {
  //    val cookie = new Cookie("cdp_user", username)
  //      .maxAge(Some(Duration.Top))
  //      .path(Some("/"))
  //      .domain(Some(ZConfig.getString("session.domain")))
  //
  //    ZConfig.getString("caas.session.same_site", "") match {
  //      case "" => cookie
  //      case x => cookie.sameSite(SameSite.fromString(x)).secure(true)
  //    }
  //  }
  //
  //  private val sessionDomain = ZConfig.getString("session.domain")
  //  private val sessionKey = ZConfig.getString("session.name")
  //
  //  private def deleteSessionCookie(): Cookie = {
  //    val sessionInfo = SessionInfo(sessionKey, "", sessionDomain, -1)
  //    val cookie = new Cookie(sessionInfo.key, sessionInfo.value)
  //      .maxAge(Some(Duration(sessionInfo.timeoutInMS, TimeUnit.MILLISECONDS)))
  //      .path(Some(sessionInfo.path))
  //      .domain(Some(sessionInfo.domain))
  //      .httpOnly(true)
  //    ZConfig.getString("caas.session.same_site", "") match {
  //      case "" => cookie
  //      case x => cookie.sameSite(SameSite.fromString(x)).secure(true)
  //    }
  //  }
}
