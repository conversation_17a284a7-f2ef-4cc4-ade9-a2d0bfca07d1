//package profile.controller.http.reader
//
//import java.io.{ByteArrayInputStream, InputStream}
//
//import com.twitter.finagle.http.{Message, Method, Request}
//import com.twitter.finatra.http.fileupload.{FileUploadException, MultipartItem, RequestContext}
//import com.twitter.finatra.http.marshalling.MessageBodyReader
//import com.twitter.finatra.http.request.RequestUtils
//import com.twitter.io.{BufInputStream, StreamIO}
//import org.apache.commons.fileupload.{FileItemFactory, FileItemIterator, FileUploadBase}
//import profile.domain.FileData
//import profile.domain.request.UpdateAvatarRequest
//import vn.vhm.common.exception.{NotFoundException, RCustomException}
//
//import scala.collection.mutable
//
///**
// * <AUTHOR>
// */
//case class UpdateAvatarReqReader() extends MessageBodyReader[UpdateAvatarRequest] {
//
//  //  override def parse[M <: UpdateAvatarRequest](request: Request)(implicit evidence$1: Manifest[M]): UpdateAvatarRequest = {
//  //
//  //    val multipart = RequestUtils.multiParams(request)
//  //    val fileBody: MultipartItem = multipart.getOrElse("file", throw RCustomException("not_found", "file"))
//  //
//  //    val data = FileData(
//  //      data = new ByteArrayInputStream(fileBody.data),
//  //      numBytes = fileBody.data.length,
//  //      mime = fileBody.contentType.get,
//  //      fileName = fileBody.filename.getOrElse(throw NotFoundException("filename not found"))
//  //    )
//  //
//  //    UpdateAvatarRequest(data = data, request = request)
//  //  }
//  override def parse(message: Message): UpdateAvatarRequest = {
//    val multipart = RequestUtils.multiParams(message.asInstanceOf[Request])
//    val fileBody: MultipartItem = multipart.getOrElse("file", throw RCustomException("not_found", "file"))
//
//    val data = FileData(
//      data = new ByteArrayInputStream(fileBody.data),
//      numBytes = fileBody.data.length,
//      mime = fileBody.contentType.get,
//      fileName = fileBody.filename.getOrElse(throw NotFoundException("filename not found"))
//    )
//
//    UpdateAvatarRequest(data = data, request = message.asInstanceOf[Request])
//  }
//
//  //  def multiParams(message: Message): Map[String, MultipartItem] = {
//  //    FinagleMessageFileUpload().parseMultipartItems(message)
//  //  }
//
//}
//
////case class FinagleMessageFileUpload() extends FileUploadBase {
////
////  def parseMultipartItems(message: Message): Map[String, MultipartItem] = {
////    val multipartMap = mutable.Map[String, MultipartItem]()
////
////    fileItemIterator(message) foreach { itr =>
////      while (itr.hasNext) {
////        val multipartItemStream = itr.next()
////
////        val multipartItemInMemory = MultipartItem(
////          data = StreamIO.buffer(multipartItemStream.openStream()).toByteArray,
////          fieldName = multipartItemStream.getFieldName,
////          isFormField = multipartItemStream.isFormField,
////          contentType = Option(multipartItemStream.getContentType),
////          filename = Option(multipartItemStream.getName),
////          headers = multipartItemStream.getHeaders
////        )
////
////        multipartMap += multipartItemInMemory.fieldName -> multipartItemInMemory
////      }
////    }
////
////    multipartMap.toMap
////  }
////
////  def fileItemIterator(message: Message): Option[FileItemIterator] = {
////    //    if (isPostOrPut(message) && isMultipart(message))
////    Some(getItemIterator(new MessageContext(message)))
////    //    else
////    //      None
////  }
////
////  override def setFileItemFactory(factory: FileItemFactory): Unit = {
////    throw new UnsupportedOperationException("FileItemFactory is not supported.")
////  }
////
////  override def getFileItemFactory: FileItemFactory = {
////    throw new UnsupportedOperationException("FileItemFactory is not supported.")
////  }
////
////}
////
////case class MessageContext(message: Message) extends org.apache.commons.fileupload.RequestContext {
////
////  override def getCharacterEncoding: String = {
////    message.charset.orNull
////  }
////
////  override def getContentLength: Int = {
////    val contentLengthLong = message.contentLength getOrElse (throw new FileUploadException(
////      "Content length must be provided."
////    ))
////    contentLengthLong.toInt
////  }
////
////  override def getContentType: String = {
////    message.contentType getOrElse (throw new FileUploadException("Content type must be provided."))
////  }
////
////  override def getInputStream: InputStream = {
////    new BufInputStream(message.content)
////  }
////}
