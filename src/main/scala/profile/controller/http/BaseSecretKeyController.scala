package profile.controller.http

import com.twitter.finagle.http.Request
import com.twitter.inject.Logging
import vn.vhm.common.util.ZConfig

/**
 * <AUTHOR> 8/1/24 16:45
 */
trait BaseSecretKeyController extends Logging {

  private lazy val mapSecretKeyPerTopic = ZConfig.getMapStringList("internal_secret_key")

  protected val secretKeyRetrieve = Seq("integrationkey", "apikey")

  protected def getSecretKey(req: Request): Option[String] = {
    secretKeyRetrieve.flatMap(key => req.headerMap.get(key).orElse(Option(req.getParam(key)))).headOption
  }

  protected def verifySecretKey(req: Request, topic: String): String = {
    val opiSecretKey = getSecretKey(req)
    if (opiSecretKey.exists(k => mapSecretKeyPerTopic.getOrElse(topic, Nil).contains(k))) {
      opiSecretKey.get
    } else throw new IllegalArgumentException("Not authorize")
  }

}
