package profile.controller.http

import com.twitter.finagle.http.Request
import com.twitter.finatra.http.Controller
import com.twitter.inject.Logging
import com.twitter.util.Future
import profile.controller.http.filter.UserQuotaJwtReq
import profile.controller.http.filter.user.UserSignedInFilter
import profile.exception.InvalidCredentialException
import profile.service.{AuthenJwtService, HttpQuotaService}
import profile.util.{Constant, PasswordHelper}
import vn.vhm.common.domain.profiling.Profiler

import javax.inject.Inject
import scala.concurrent.duration.DurationInt

/**
 * <AUTHOR> 10/18/24 10:39
 */
class OwnerVerificationController @Inject()(
                                             authenService: AuthenJwtService,
                                             quotaService: HttpQuotaService
                                           ) extends Controller with Logging {

  filter[UserSignedInFilter]
    .post("/user/validate-owner") {
      req: ValidateOwnerRequest => {
        Profiler("HTTP-POST /user/validate-owner") {
          quotaService.calcQuotaUserMac2(req, "user-validate-owner", 1.minutes, 10, false)((num, max) => {
            authenService.validateOwner(req) rescue {
              case e: InvalidCredentialException => Future.exception(InvalidCredentialException(
                message = e.message, data = e.data ++ Map("num_validate_failed" -> num, "max_validate_failed" -> max)
              ))
            }
          })
        }
      }
    }

}

case class ValidateOwnerRequest(
                                 password: String,
                                 deviceId: String,
                                 data: Map[String, AnyRef],
                                 tokenCaptcha: Option[String],
                                 @Inject request: Request
                               ) extends UserQuotaJwtReq {

  import profile.controller.http.filter.user.UserContext.UserContextSyntax

  def passwordHashed: String = PasswordHelper.hashPasswordBCrypt(request.user.username.get, password)

  def username: String = request.user.username.get

  override def quotaUsername(): String = username

  override def optTokenCaptcha(): Option[String] = tokenCaptcha

  def jwtRequestedBy: Option[String] = {
    request.getClaimAsString(Constant.JWT_CLAIM_REQUESTED_BY_F)
  }

}