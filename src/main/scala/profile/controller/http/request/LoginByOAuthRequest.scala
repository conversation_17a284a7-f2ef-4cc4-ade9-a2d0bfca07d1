package profile.controller.http.request

import com.twitter.finatra.http.annotations.RouteParam
import com.twitter.finatra.validation.{MethodValidation, ValidationResult}
import profile.domain.LoginType

/**
 * <AUTHOR> 8/23/20 3:48 PM
 */
case class LoginByOAuthRequest(
                                  @RouteParam oauthType: String,
                                  oauthId: String,
                                  oauthToken: String,
                                  oauthClientId: Option[String]
                                ) {
  @MethodValidation
  def checkValid: ValidationResult = {
    if (!LoginType.isOAuthType(oauthType)) ValidationResult.Invalid(s"Invalid oauth_type: $oauthType")
    else ValidationResult.Valid()
  }

  def getOAuthData: OAuthData = OAuthData(
    oauthType = oauthType,
    oauthId = oauthId,
    oauthToken = oauthToken,
    oauthClientId = oauthClientId
  )
}