package profile.controller.http.request

import com.twitter.finagle.http.Request
import com.twitter.finatra.http.request.RequestUtils
import com.twitter.finatra.validation.{MethodValidation, ValidationResult}
import com.twitter.util.Try
import org.apache.commons.io.FileUtils
import profile.controller.http.filter.UserQuotaJwtReq
import profile.domain.BytesFile
import profile.domain.customer.{CustomerIdentityDocType, CustomerIdentityDocument}
import profile.util.FileFormatUtils
import vn.vhm.common.domain.OptionImplicits.OptionImplicitAny
import vn.vhm.common.exception.VhmCustomException

import java.io.File
import java.util.UUID
import javax.inject.Inject

/**
 * <AUTHOR> 9/19/24 04:27
 */
case class VerifyCustomerIdentityReq(@Inject req: Request) extends UserQuotaJwtReq {

  private lazy val multiParams = RequestUtils.multiParams(req)

  private def getString(field: String): Option[String] = {
    multiParams.get(field).map(v => new String(v.data))
  }

  lazy val identityType = Try(getString("identity_type")).toOption.flatten
    .flatMap(CustomerIdentityDocType.opt)
    .getOrElse(throw VhmCustomException("invalid_param", "identity_type"))

  lazy val identityFiles: Seq[BytesFile] = {
    val files = scala.collection.mutable.ListBuffer.empty[BytesFile]
    for (idx <- 1 to 2) {
      val docKey = s"identity_document_$idx"
      multiParams.get(docKey).foreach(data => {

        val fileExt = data.contentType.flatMap(FileFormatUtils.getFileFormat) match {
          case Some(x) if x == "jpg" || x == "png" || x == "pdf" => x
          case x => throw VhmCustomException("invalid_param", s"$docKey is not supported content-type [${x.fold("")(_)}]")
        }

        val id = UUID.randomUUID().toString
        val localFilePath = s"/tmp/$id/${identityType}_$docKey.$fileExt"
        FileUtils.writeByteArrayToFile(new java.io.File(localFilePath), data.data)

        files += BytesFile(
          id = id, localPath = localFilePath,
          filePath = s"$docKey.$fileExt",
          contentType = data.contentType.getOrElse("image/jpg"),
          originFileName = data.filename,
          idx = idx - 1
        )
      })
    }
    //TODO: PhongPQ bổ sung thêm check nếu CCCD thì tối đa 2 hình, passport thì tối đa 1 hình
    identityType match {
      case CustomerIdentityDocType.CCCD if files.size > 2 => throw VhmCustomException("invalid_param", s"the number of updated images is invalid")
      case CustomerIdentityDocType.CCCD =>
      case CustomerIdentityDocType.PASSPORT if files.size > 1 => throw VhmCustomException("invalid_param", s"the number of updated images is invalid")
      case CustomerIdentityDocType.PASSPORT =>
      case _ => throw VhmCustomException("invalid_param", "identity_type")
    }
    files
  }

  lazy val faceImageFile: Option[BytesFile] = {
    val docKey = s"face_image"
    multiParams.get(docKey).map(data => {
      val fileExt = data.contentType.flatMap(FileFormatUtils.getFileFormat) match {
        case Some(x) if x == "jpg" || x == "png" => x
        case x => throw VhmCustomException("invalid_param", s"$docKey is not supported content-type [${x.fold("")(_)}]")
      }

      val id = UUID.randomUUID().toString
      val localFilePath = s"/tmp/$id/$docKey.$fileExt"
      FileUtils.writeByteArrayToFile(new java.io.File(localFilePath), data.data)

      BytesFile(
        id = id, localPath = localFilePath,
        filePath = s"$docKey.$fileExt",
        contentType = data.contentType.getOrElse("image/jpg"),
        originFileName = data.filename,
        idx = 0
      )
    })
  }

  lazy val identityDoc = {
    CustomerIdentityDocument(
      `type` = identityType.toString.toSome,
      no = None,
      verifiedStatus = None, verifiedStatusMessage = None,
      identityDocumentVerifyId = None
    )
  }

  lazy val stageMetadata = getString("stage_metadata")

  lazy val customAddress = getString("address").filter(_.nonEmpty)

  def close(): Unit = try {
    identityFiles.foreach(file => FileUtils.deleteQuietly(new File(file.localPath)))
    faceImageFile.foreach(file => FileUtils.deleteQuietly(new File(file.localPath)))
  } catch {
    case _: Exception =>
  }

  @MethodValidation
  def checkValid: ValidationResult = try {

    if (identityFiles.isEmpty) throw new Exception("identity_document_1 is required")

    ValidationResult.Valid()
  } catch {
    case e: Exception => ValidationResult.Invalid(e.getMessage)
  }

  override def quotaUsername(): String = {
    import profile.controller.http.filter.user.UserContext.UserContextSyntax
    req.getUsername()
  }

  override def optTokenCaptcha(): Option[String] = {
    getString("token_captcha")
  }

}
