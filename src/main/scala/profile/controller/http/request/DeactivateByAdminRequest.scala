package profile.controller.http.request

import com.fasterxml.jackson.annotation.JsonProperty
import com.twitter.finagle.http.Request
import com.twitter.finatra.http.annotations.RouteParam
import profile.controller.http.filter.user.CmsUserContext.UserContextSyntax
import profile.util.Constant

import javax.inject.Inject

/**
 * <AUTHOR> 2025-07-21
 */
case class DeactivateByAdminRequest(@RouteParam id: String,
                                    @JsonProperty("requestBy") requestBy: String,
                                    @JsonProperty("requestReason") requestReason: String,
                                    @Inject request: Request) {

  def cmsUsername: String = request.cmsUser.username.get

  def pnl: String = request.cmsUser.pnl.getOrElse(Constant.JWT_CLAIM_PNL_VINCLUB)
}
