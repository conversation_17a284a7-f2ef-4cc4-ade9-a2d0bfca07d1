package profile.controller.http.request

import com.twitter.finagle.http.Request
import com.twitter.finatra.validation.{MethodValidation, ValidationResult}
import profile.controller.http.filter.EmailQuotaJwtReq
import profile.controller.http.filter.phone.PhoneQuotaJwtReq
import profile.controller.http.filter.user.CmsUserContext._
import profile.domain.request.UpdateUserRequest.{checkTimeFormat, dayOfBirthFormat, enumGenders}
import profile.util.{Constant, CustomUtils, EmailHelper, PhoneUtils}
import vn.vhm.common.domain.OptionImplicits.OptionImplicitAny

import javax.inject.Inject
import scala.concurrent.duration.Duration

/**
 * <AUTHOR>
 */
case class RegisterByAdminRequest(
                                   phone: Option[String],
                                   email: Option[String],
                                   fullName: Option[String],
                                   firstName: Option[String],
                                   lastName: Option[String],
                                   gender: Option[String] = None,
                                   birthday: Option[String] = None,
                                   phoneVerified: Boolean = false,
                                   emailVerified: Boolean = false,
                                   otp: Option[String] = None,
                                   otpChannel: Option[String] = None,
                                   @Inject request: Request,

                                   //enhance
                                   var pnl: Option[String] = None,
                                   var referralCode: Option[String] = None
                                 ) extends PhoneQuotaJwtReq with EmailQuotaJwtReq {

  override def optLockInDuration(): Option[Duration] = None

  override def optTokenCaptcha(): Option[String] = None

  override def optQuotaEmail(): Option[String] = email

  override def optNormPhone(): Option[String] = phone.map(PhoneUtils.normalizePhone)

  def cmsUsername: String = request.cmsUser.username.get

  // if pnl is not exist in jwt claim, then get from request
  pnl = {
    request.cmsUser.pnl match {
      case Some(jwtPnl) if !Constant.JWT_CLAIM_PNL_VINCLUB.equals(jwtPnl) => jwtPnl.toSome
      case _ => pnl.orElse(Constant.JWT_CLAIM_PNL_VINCLUB.toSome)
    }
  }

  @MethodValidation
  def checkValid: ValidationResult = {
    if (CustomUtils.isEmpty(pnl) || (!Constant.listPnl.contains(pnl.get) && !Constant.JWT_CLAIM_PNL_VINCLUB.equals(pnl.get))) return ValidationResult.Invalid("pnl is invalid")
    if (CustomUtils.isEmpty(phone) && CustomUtils.isEmpty(email)) return ValidationResult.Invalid("phone or email: field is required")
    if (phoneVerified && CustomUtils.isEmpty(phone)) return ValidationResult.Invalid("phone: phone is required if phoneVerified is true")
    if (emailVerified && CustomUtils.isEmpty(email)) return ValidationResult.Invalid("email: email is required if emailVerified is true")
    if (phone.exists(p => p.nonEmpty && !vn.vhm.common.util.PhoneUtils.isPhoneNumber(p))) return ValidationResult.Invalid("phone: field is invalid")
    if (!CustomUtils.isEmpty(email)) {
      val emailData = EmailHelper.isValidEmail(email.get)
      if (!emailData.isValid) {
        return ValidationResult.Invalid(s"email: field is invalid. Reason: ${emailData.reason.get}")
      }
    }
    if (CustomUtils.isEmpty(fullName) && CustomUtils.isEmpty(firstName) && CustomUtils.isEmpty(lastName)) return ValidationResult.Invalid("full_name or first_name or last_name: field is required")
    if (fullName.exists(_.length > 255)) return ValidationResult.Invalid("full_name: field is too long")
    if (firstName.exists(_.length > 100)) return ValidationResult.Invalid("first_name: field is too long")
    if (lastName.exists(_.length > 100)) return ValidationResult.Invalid("last_name: field is too long")

    if (!CustomUtils.isEmpty(gender) && !enumGenders.contains(gender.get)) return ValidationResult.Invalid(s"gender: field is invalid value. Must be one of '${enumGenders.mkString(", ")}'")
    if (!CustomUtils.isEmpty(birthday) && !checkTimeFormat(birthday.get, dayOfBirthFormat)) return ValidationResult.Invalid(s"birthday: field is invalid for format `$dayOfBirthFormat`")

    ValidationResult.Valid()
  }

  def toRegisterByAdminData: RegisterByAdminData = RegisterByAdminData(
    phone = phone,
    email = email,
    fullName = fullName,
    firstName = firstName,
    lastName = lastName,
    gender = gender,
    birthday = birthday,
    phoneVerified = phoneVerified,
    emailVerified = emailVerified,
    otp = otp,
    otpChannel = otpChannel,
    referralCode = referralCode,
    pnl = pnl,
    cmsUsername = cmsUsername
  )
}

case class RegisterByAdminData(
                                phone: Option[String],
                                email: Option[String],
                                fullName: Option[String] = None,
                                firstName: Option[String] = None,
                                lastName: Option[String] = None,
                                gender: Option[String] = None,
                                birthday: Option[String] = None,
                                phoneVerified: Boolean = false,
                                emailVerified: Boolean = false,
                                otp: Option[String] = None,
                                otpChannel: Option[String] = None,
                                referralCode: Option[String] = None,
                                pnl: Option[String] = None,
                                cmsUsername: String
                              ) extends PhoneQuotaJwtReq with EmailQuotaJwtReq {
  override def optNormPhone(): Option[String] = phone.map(PhoneUtils.normalizePhone)

  override def optQuotaEmail(): Option[String] = email

  override def optTokenCaptcha(): Option[String] = None

  override def optLockInDuration(): Option[Duration] = None

  def optEmail(): Option[String] = email

  def buildRequestToken(): String = {
    val token = s"${pnl}-${phone.getOrElse("")}-${phoneVerified}-${email.getOrElse("")}-${emailVerified}-${firstName.getOrElse("")}-${lastName.getOrElse("")}-${fullName.getOrElse("")}-${gender.getOrElse("")}-${birthday.getOrElse("")}"
    java.util.Base64.getEncoder.encodeToString(token.getBytes("UTF-8"))
  }

  def isValid: Boolean = {
    if (CustomUtils.isEmpty(pnl) || (!Constant.listPnl.contains(pnl.get) && !Constant.JWT_CLAIM_PNL_VINCLUB.equals(pnl.get))) return false
    if (CustomUtils.isEmpty(phone) && CustomUtils.isEmpty(email)) return false
    if (phoneVerified && CustomUtils.isEmpty(phone)) return false
    if (emailVerified && CustomUtils.isEmpty(email)) return false
    if (phone.exists(p => p.nonEmpty && !vn.vhm.common.util.PhoneUtils.isPhoneNumber(p))) return false
    if (!CustomUtils.isEmpty(email)) {
      val emailData = EmailHelper.isValidEmail(email.get)
      if (!emailData.isValid) {
        return false
      }
    }
    if (CustomUtils.isEmpty(fullName) && CustomUtils.isEmpty(firstName) && CustomUtils.isEmpty(lastName)) return false
    if (fullName.exists(_.length > 255)) return false
    if (firstName.exists(_.length > 100)) return false
    if (lastName.exists(_.length > 100)) return false

    if (!CustomUtils.isEmpty(gender) && !enumGenders.contains(gender.get)) return false
    if (!CustomUtils.isEmpty(birthday) && !checkTimeFormat(birthday.get, dayOfBirthFormat)) return false

    true
  }
}

