package profile.controller.http.request

import com.twitter.finatra.http.annotations.RouteParam
import com.twitter.finatra.validation.constraints.NotEmpty
import com.twitter.finatra.validation.{MethodValidation, ValidationResult}
import profile.domain.LoginType
import profile.util.PhoneUtils

/**
 * <AUTHOR> 8/23/20 4:42 PM
 */

case class OAuthData(
                      oauthType: String,
                      oauthId: String,
                      oauthToken: String,
                      oauthClientId: Option[String]
                    )

case class RegisterData(
                         email: Option[String] = None,
                         firstName: Option[String],
                         lastName: Option[String]
                       )

case class RegisterByOAuthRequest(
                                   @RouteParam oauthType: String,
                                   oauthId: String,
                                   oauthToken: String,
                                   oauthClientId: Option[String] = None,
                                   phone: String
                                 ) {

  val phoneIgnorePlus = PhoneUtils.normalizePhone(phone)

  @MethodValidation
  def checkValid: ValidationResult = {
    if (!LoginType.isOAuthType(oauthType)) ValidationResult.Invalid(s"Invalid oauth_type: $oauthType")
    else ValidationResult.Valid()
  }

  def getOAuthData: OAuthData = OAuthData(
    oauthType = oauthType,
    oauthId = oauthId,
    oauthToken = oauthToken,
    oauthClientId = oauthClientId
  )
}

case class VerifyRegisterByOAuthRequest(
                                         @RouteParam oauthType: String,
                                         oauthId: String,
                                         oauthToken: String,
                                         oauthClientId: Option[String] = None,
                                         phone: String,
                                         code: String
                                       ) {
  val phoneIgnorePlus = PhoneUtils.normalizePhone(phone)

  @MethodValidation
  def checkValid: ValidationResult = {
    if (!LoginType.isOAuthType(oauthType)) ValidationResult.Invalid(s"Invalid oauth_type: $oauthType")
    else ValidationResult.Valid()
  }

  def getOAuthData: OAuthData = OAuthData(
    oauthType = oauthType,
    oauthId = oauthId,
    oauthToken = oauthToken,
    oauthClientId = oauthClientId
  )
}

case class ConfirmRegisterByOAuthRequest(
                                          @RouteParam oauthType: String,
                                          oauthId: String,
                                          oauthToken: String,
                                          oauthClientId: Option[String],
                                          phone: String,
                                          tokenPhone: String,
                                          data: RegisterData
                                        ) {
  val phoneIgnorePlus = PhoneUtils.normalizePhone(phone)

  @MethodValidation
  def checkValid: ValidationResult = {
    if (!LoginType.isOAuthType(oauthType)) ValidationResult.Invalid(s"Invalid oauth_type: $oauthType")
    else ValidationResult.Valid()
  }

  def getOAuthData: OAuthData = OAuthData(
    oauthType = oauthType,
    oauthId = oauthId,
    oauthToken = oauthToken,
    oauthClientId = oauthClientId
  )
}
