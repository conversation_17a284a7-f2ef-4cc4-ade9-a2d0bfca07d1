package profile.controller.http

import com.twitter.finagle.http.Request
import com.twitter.finatra.http.Controller
import com.twitter.finatra.http.annotations.RouteParam
import com.twitter.finatra.validation.{MethodValidation, ValidationResult}
import com.twitter.util.Future
import profile.controller.http.filter.UserQuotaJwtReq
import profile.controller.http.request.{CalcQuotaEmailRequest, CalcQuotaPhoneRequest}
import profile.domain.response.BaseResponse
import profile.exception.{InvalidCredentialException, InvalidEmailException, InvalidPhoneException}
import profile.service.{AuthenJwtService, HttpQuotaService, ProfileService, VerifyDataService}
import profile.util.PasswordHelper
import vn.vhm.common.domain.OptionImplicits.OptionImplicitAny
import vn.vhm.common.domain.profiling.Profiler
import vn.vhm.common.exception.UnsupportedException

import javax.inject.Inject
import scala.concurrent.duration.DurationInt

/**
 * <AUTHOR>
 */
class InternalUserController @Inject()(
                                        authService: AuthenJwtService,
                                        profileService: ProfileService,
                                        quotaService: HttpQuotaService,
                                        verifyDataService: VerifyDataService
                                      ) extends Controller with BaseSecretKeyController {

  get("/internal/user/:id/profile") {
    req: InternalGetProfileRequest => {
      Profiler("HTTP-GET /internal/user/:id/profile") {

        verifySecretKey(req.request, "internal_auth")

        profileService.get(req.id, rescanPnl = false).map(r => {
          BaseResponse(code = 0, data = Some(r.toInternalDto))
        })
      }
    }
  }

  post("/internal/user/profile") {
    req: InternalGetProfileByIdentifyRequest => {
      Profiler("HTTP-POST /internal/user/profile") {

        verifySecretKey(req.request, "internal_auth")

        profileService.getActiveUserByPhoneOrEmail(normPhone = req.phone, email = req.email, rescanPnl = false).map(r => {
          BaseResponse(code = 0, data = Some(r.toInternalDto))
        })

      }
    }
  }

  post("/internal/user/validate-auth") {
    req: InternalValidateAuthRequest => {
      Profiler("HTTP-POST /internal/user/validate-auth") {

        verifySecretKey(req.request, "internal_auth")

        quotaService.calcQuotaUserMac2(req, "user-validate-auth", 6.hour, 30, false)((num, max) => {
          authService.validateAuth(req)
            .rescue {
              case e: InvalidCredentialException => Future.exception(InvalidCredentialException(
                message = e.message, data = e.data ++ Map("num_validate_failed" -> num, "max_validate_failed" -> max)
              ))
            }
            .map(result => BaseResponse(code = 0, data = Some(result)))
        })

      }
    }
  }

  post("/internal/user/send-code") {
    req: InternalSendCodeRequest => {
      Profiler("HTTP-POST /internal/user/send-code") {

        verifySecretKey(req.request, "internal_auth")

        if (req.phone.isDefined) {
          for {
            result <- quotaService.calcQuotaPhoneSendCode(req.request,
              CalcQuotaPhoneRequest(req.getPhone, lockInDuration = 30.minutes.toSome),
              checkQuotaCountry = false, checkQuotaIP = false
            ) {
              verifyDataService.sendCodeToPhone(req.getPhone, req.code, req.platform)
                .map(_ => Map("otp" -> true, "channel" -> "phone"))
            }
          } yield BaseResponse(code = 0, data = Some(result))

        } else if (req.email.isDefined) {

          for {
            result <- quotaService.calcQuotaEmailSendCode(req.request,
              CalcQuotaEmailRequest(req.getEmail, lockInDuration = 30.minutes.toSome),
              checkQuotaCountry = false, checkQuotaIP = false
            ) {
              verifyDataService.sendCodeToEmail(req.getEmail, req.code, req.getFullName, false)
                .map(_ => Map("otp" -> true, "channel" -> "email"))
            }
          } yield BaseResponse(code = 0, data = Some(result))

        } else Future.exception(UnsupportedException("Not exist phone or email"))

      }
    }
  }

}

case class InternalGetProfileRequest(@RouteParam id: String, @Inject request: Request)

case class InternalValidateAuthRequest(
                                        username: String,
                                        password: String,
                                        channelDeviceId: String,
                                        tokenCaptcha: Option[String],
                                        verifyChannel: String, // SSO, VOUCHER, SPENDING POINT
                                        @Inject request: Request,
                                      ) extends UserQuotaJwtReq {

  def passwordHashed: String = PasswordHelper.hashPasswordBCrypt(username, password)

  override def quotaUsername(): String = username

  override def optTokenCaptcha(): Option[String] = tokenCaptcha

}

case class InternalGetProfileByIdentifyRequest(
                                                phone: Option[String],
                                                email: Option[String],
                                                @Inject request: Request,
                                              ) {

  @MethodValidation
  def checkValid: ValidationResult = try {

    if (phone.isEmpty && email.isEmpty) {
      throw new Exception("phone or email is required")
    }

    ValidationResult.Valid()
  } catch {
    case e: Exception => ValidationResult.Invalid(e.getMessage)
  }

}

case class InternalSendCodeRequest(
                                    phone: Option[String],
                                    email: Option[String],
                                    code: String,
                                    platform: Option[String],
                                    renderData: Option[Map[String, Any]],
                                    @Inject request: Request,
                                  ) {

  def getEmail: String = email.filter(_.nonEmpty).getOrElse(throw InvalidEmailException())

  def getPhone: String = phone.filter(_.nonEmpty).getOrElse(throw InvalidPhoneException())

  def getFullName: Option[String] = renderData.flatMap(_.get("full_name")).map(_.toString)

}