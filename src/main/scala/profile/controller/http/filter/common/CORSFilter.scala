package profile.controller.http.filter.common

import com.twitter.finagle.http.filter.Cors._

/**
 * <AUTHOR>
 */
class CORSFilter extends HttpFilter(Policy(
  allowsOrigin = { origin => Some(origin) },
  allowsMethods = { _ => Some(Seq("GET", "POST", "PUT", "DELETE", "OPTIONS", "HEAD")) },
  allowsHeaders = { _ => Some(Seq("origin", "content-type", "accept", "authorization", "X-Requested-With", "X-Codingpedia", "cookie")) },
  supportsCredentials = true)) {
}
