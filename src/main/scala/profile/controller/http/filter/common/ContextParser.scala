package profile.controller.http.filter.common

import com.twitter.finagle.http.{Request, Response}
import com.twitter.finagle.{Service, SimpleFilter}
import com.twitter.util.Future
import profile.service.ContextHolder
import profile.util.CustomUtils

import javax.inject.Inject

/**
 * <AUTHOR>
 */
class ContextParser @Inject()(contextHolder: ContextHolder) extends SimpleFilter[Request, Response] {

  private val headers = Seq("Accept-Language", "accept-language")

  override def apply(request: Request, service: Service[Request, Response]): Future[Response] = {
    val lang = CustomUtils.getLang(
      headers.flatMap(h => request.headerMap.get(h))
        .headOption
        .getOrElse("")
    )

    contextHolder.setContext(lang = lang) {
      service(request)
    }

  }

}
