package profile.controller.http.filter.common

import com.twitter.finagle.{Service, SimpleFilter}
import com.twitter.finagle.http.{Request, Response}
import com.twitter.util.Future
import vn.vhm.common.exception.UnAuthentication
import vn.vhm.common.util.ZConfig

class InternalIdentityVerificationSecretKeyFilter extends SimpleFilter[Request, Response] {

  private val secretKeyRetrieve = Seq("integrationkey")
  private val secretKey = ZConfig.getString("main.internal_identify_verification_secret_key", "")

  protected def getSecretKey(req: Request): Option[String] = {
    secretKeyRetrieve.flatMap(key => req.headerMap.get(key).orElse(Option(req.getParam(key)))).headOption
  }

  override def apply(request: Request, service: Service[Request, Response]): Future[Response] = {
    if (secretKey.nonEmpty && getSecretKey(request).contains(secretKey)) {
      service(request)
    } else Future.exception(UnAuthentication("Invalid secret key"))
  }

}