package profile.controller.http.filter.user

import com.twitter.finagle.http.{Request, Response}
import com.twitter.finagle.{Service, SimpleFilter}
import com.twitter.util.Future
import profile.exception.AccessTokenInvalid
import profile.service.AuthenJwtService
import vn.vhm.common.domain.Implicits.async
import vn.vhm.common.domain.OptionImplicits.OptionImplicitAny
import vn.vhm.common.exception.UnAuthorize

import javax.inject.Inject

/**
 * <AUTHOR>
 */

case class CmsUserAuthRequestInfo(
                                   username: Option[String],
                                   email: Option[String],
                                   tenantId: Option[Long],
                                   orgId: Option[Long],
                                   builtInRole: Option[String],
                                   pnl: Option[String],
                                   accessToken: Option[String],
                                   claims: Option[Map[String, Any]]
                                 )

object CmsAuthUserParser {

  private def getAccessToken(request: Request): Option[String] = {
    request.headerMap.get("Authorization").map(_.replace("Bearer ", "")).filter(_.nonEmpty)
  }

}

class CmsAuthUserParser extends SimpleFilter[Request, Response] {

  import CmsAuthUserParser._

  override def apply(request: Request, service: Service[Request, Response]): Future[Response] = {
    for {
      userAuthInfo <- getAccessToken(request) match {
        case Some(accessToken) => async {
          val claims = parseClaims(accessToken)
          CmsUserAuthRequestInfo(
            username = claims.get("sub").map(_.toString.toLowerCase()),
            email = claims.get("email").map(_.toString),
            tenantId = claims.get("tenant_id").map(_.toString.toLong),
            orgId = claims.get("organization_id").map(_.toString.toLong),
            builtInRole = claims.get("builtin_role").map(_.toString),
            pnl = claims.get("pnl").map(_.toString.toUpperCase),
            accessToken = accessToken.toSome,
            claims = claims.toSome
          )
        }
        case _ =>
          Future.value(CmsUserAuthRequestInfo(None, None, None, None, None, None, None, None))
      }

      _ = CmsUserContext.setCmsUser(request, userAuthInfo)


      result <- service(request)
    } yield result

  }

  private def parseClaims(accessToken: String): Map[String, Any] = {
    try {
      val lastIndexOfDot = accessToken.lastIndexOf('.')
      val partialToken = accessToken.substring(0, lastIndexOfDot + 1)
      val claims = io.jsonwebtoken.Jwts.parserBuilder()
        .build()
        .parse(partialToken)
        .getBody
        .asInstanceOf[io.jsonwebtoken.Claims]
      // validate token not VClub OAuth
      if (claims.get("tenant_id") == null) throw AccessTokenInvalid()
      claims.entrySet().toArray.map { entryObj =>
        val entry = entryObj.asInstanceOf[java.util.Map.Entry[String, Object]]
        entry.getKey -> entry.getValue
      }.toMap
    } catch {
      case _: Exception => throw AccessTokenInvalid()
    }
  }

}

object CmsUserContext {

  private val CmsUserAuthRequestField = Request.Schema.newField[CmsUserAuthRequestInfo]()

  def setCmsUser(request: Request, authInfo: CmsUserAuthRequestInfo): Unit = {
    request.ctx.update(CmsUserAuthRequestField, authInfo)
  }

  implicit class UserContextSyntax(val request: Request) extends AnyVal {

    def cmsUser: CmsUserAuthRequestInfo = request.ctx(CmsUserAuthRequestField)

    def getCmsClaimAsString(field: String): Option[String] = {
      cmsUser.claims.flatMap(_.get(field)).map {
        case x: String => x
        case x => x.toString
      }
    }

    def getCmsClaimAsLong(field: String): Option[Long] = {
      cmsUser.claims.flatMap(_.get(field)).map {
        case x: Long => x
        case x: java.lang.Long => x
        case x => x.toString.toLong
      }
    }

    def getCmsUsername(): String = cmsUser.username.getOrElse(throw UnAuthorize())
  }

}
