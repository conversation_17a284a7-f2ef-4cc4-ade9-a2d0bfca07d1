package profile.controller.http.filter.user

import profile.controller.http.filter.parser.DataRequestContext._

/**
 * <AUTHOR>
 */

trait UsernameFilterRequest {
  def getUsername: String
}

//class UserTypeUPFilter @Inject()(
//                                  userProfileService: UserProfileService
//                                ) extends SimpleFilter[Request, Response] {
//  override def apply(request: Request, service: Service[Request, Response]): Future[Response] = {
//    val usernameRequest: UsernameFilterRequest = request.requestData
//    userProfileService.getUserProfile(usernameRequest.getUsername).flatMap({
//      case Some(x) if x.oauthType.isEmpty || x.oauthType.get.equals(LoginType.U_P.toString) => service(request)
//      case Some(_) => Future.exception(new NotAuthTypeUserPassException)
//      case _ => service(request)
//    })
//  }
//}