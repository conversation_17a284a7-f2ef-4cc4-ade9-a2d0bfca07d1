package profile.controller.http.filter.user

import com.twitter.finagle.http.{Request, Response}
import com.twitter.finagle.{Service, SimpleFilter}
import com.twitter.util.Future
import profile.service.AuthenJwtService
import vn.vhm.common.domain.Implicits.async
import vn.vhm.common.domain.OptionImplicits.OptionImplicitAny
import vn.vhm.common.exception.UnAuthorize

import javax.inject.Inject

/**
 * <AUTHOR>
 */

case class UserAuthRequestInfo(
                                sessionId: Option[String],
                                username: Option[String],
                                accessToken: Option[String],
                                claims: Option[Map[String, Any]]
                              )

object AuthUserParser {

   private def getAccessToken(request: Request): Option[String] = {
    request.headerMap.get("Authorization").map(_.replace("Bearer ", "")).filter(_.nonEmpty)
  }

}

class AuthUserParser @Inject()(authService: AuthenJwtService) extends SimpleFilter[Request, Response] {

  import AuthUserParser._

  override def apply(request: Request, service: Service[Request, Response]): Future[Response] = {
    for {
      userAuthInfo <- getAccessToken(request) match {
        case Some(accessToken) => async {
          val parseResult = authService.parseAccessToken(accessToken, false)
          val username = parseResult._1
          UserAuthRequestInfo(None, username.toSome, accessToken = accessToken.toSome, claims = parseResult._2.toSome)
        }
        case _ =>
          Future.value(UserAuthRequestInfo(None, None, None, None))
      }

      _ = UserContext.setUser(request, userAuthInfo)

      result <- service(request)
    } yield result
  }

}

object UserContext {

  private val UserAuthRequestField = Request.Schema.newField[UserAuthRequestInfo]()

  def setUser(request: Request, authInfo: UserAuthRequestInfo): Unit = {
    request.ctx.update(UserAuthRequestField, authInfo)
  }

  implicit class UserContextSyntax(val request: Request) extends AnyVal {

    def user: UserAuthRequestInfo = request.ctx(UserAuthRequestField)

    def getClaimAsString(field: String): Option[String] = {
      user.claims.flatMap(_.get(field)).map {
        case x: String => x
        case x => x.toString
      }
    }

    def getClaimAsLong(field: String): Option[Long] = {
      user.claims.flatMap(_.get(field)).map {
        case x: Long => x
        case x: java.lang.Long => x
        case x => x.toString.toLong
      }
    }

    def getUsername(): String = user.username.getOrElse(throw UnAuthorize())

  }

}
