package profile.controller.http.filter.user

import com.twitter.finagle.http.{Request, Response}
import com.twitter.finagle.{Service, SimpleFilter}
import com.twitter.util.Future
import profile.controller.http.filter.user.CmsUserContext._
import profile.exception.UnAuthenException

import javax.inject.Inject

/**
 * <AUTHOR>
 */
class CmsUserPermissionFilter @Inject()() extends SimpleFilter[Request, Response] {
  override def apply(request: Request, service: Service[Request, Response]): Future[Response] = {
    if (request.cmsUser.username.isEmpty || request.cmsUser.tenantId.isEmpty) Future.exception(UnAuthenException("token is missing or invalid"))
    else service(request)
  }
}
