package profile.controller.http.filter.user

/**
 * <AUTHOR>
 */
//class UserOAuthDefaultFilter @Inject()(authenService: AuthenService) extends SimpleFilter[Request, Response] {
//  override def apply(request: Request, service: Service[Request, Response]): Future[Response] = {
//    authenService.isUserOAuthDefault(request.user.username.get).map({
//      case true => service(request)
//      case _ => Future.exception(UnAuthorizeException("Not allow"))
//    }).flatten
//  }
//}
