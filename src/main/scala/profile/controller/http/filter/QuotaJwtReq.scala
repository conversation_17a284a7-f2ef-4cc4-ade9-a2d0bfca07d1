package profile.controller.http.filter

import scala.concurrent.duration.Duration

/**
 * <AUTHOR> 2/14/24 13:35
 */
trait EmailQuotaJwtReq {

  def optQuotaEmail(): Option[String]

  def optTokenCaptcha(): Option[String]

  def optLockInDuration(): Option[Duration] = None
}

trait UserQuotaJwtReq {

  def quotaUsername(): String

  def optTokenCaptcha(): Option[String]

  def optLockInDuration(): Option[Duration] = None

}
