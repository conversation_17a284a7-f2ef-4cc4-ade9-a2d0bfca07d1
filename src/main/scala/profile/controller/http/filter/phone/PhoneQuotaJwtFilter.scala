package profile.controller.http.filter.phone

import com.twitter.finagle.http.{Request, Response}
import com.twitter.finagle.{Service, SimpleFilter}
import com.twitter.util.{Future, Return, Throw}
import core.quota.domain.QuotaFilterContext.QuotaFilterContextSyntax
import core.ratelimiter.service.CaptchaService
import profile.controller.http.filter.parser.DataRequestContext.MainRequestContextSyntax
import profile.exception.{ExceedQuotaException, IgnoreProcess, InvalidTokenCaptchaException}
import profile.service.{ProfileService, VerifyDataService}
import vn.vhm.common.domain.profiling.Profiler

import javax.inject.Inject
import scala.concurrent.duration.Duration

/**
 * <AUTHOR> 1/29/24 06:01
 */

trait PhoneQuotaJwtReq {

  def optNormPhone(): Option[String]

  def optTokenCaptcha(): Option[String]

  def optLockInDuration(): Option[Duration] = None
}



//trait PhoneQuotaJwtReq {
//
//  def optNormPhone(): Option[String]
//
//  def optTokenCaptcha(): Option[String]
//
//}

//trait PhoneQuotaJwtReq {
//
//  def optNormPhone(): Option[String]
//
//}

abstract class BasePhoneQuotaJwtFilter extends SimpleFilter[Request, Response] {

  val profileService: ProfileService
  val verifyDataService: VerifyDataService
  val captchaService: CaptchaService

  val needCheckExistPhone: Boolean
  val applyToExistPhone: Boolean


  private def checkQuota(request: Request): Future[Unit] = Profiler(s"${getClass.getCanonicalName}.checkQuota") {
    //    val reqNode = JsonHelper.readTree(request.contentString)
    val reqData = request.requestData[PhoneQuotaJwtReq]

    //    val optPhone = Option(reqNode.path("phone").asText(null)).filter(_.nonEmpty).map(PhoneUtils.normalizePhoneIgnorePlus)
    val optPhone = reqData.optNormPhone()

    val optCountry = request.headerMap.find(_._1.equalsIgnoreCase("cf-ipcountry")).map(_._2)
    val countryIsVN = optCountry.map(_.equalsIgnoreCase("VN"))

    val optIp = request.headerMap.find(_._1.equalsIgnoreCase("X-Real-IP")).map(_._1).filter(_.nonEmpty)
      .orElse(Option(request.remoteAddress).flatMap(v => Option(v.getHostAddress))).filter(_.nonEmpty)

    //    val optTokenCaptcha = Option(JsonHelper.readTree(request.contentString).path("token_captcha").asText(null)).filter(_.nonEmpty)
    val optTokenCaptcha = reqData.optTokenCaptcha()

    optPhone match {
      case Some(phone) if needCheckExistPhone =>
        profileService.getActiveUsernameByPhone(phone).flatMap {
          case Some(_) if applyToExistPhone => _checkQuota(phone, optTokenCaptcha, optIp, optCountry, countryIsVN)
          case None if !applyToExistPhone => _checkQuota(phone, optTokenCaptcha, optIp, optCountry, countryIsVN)
          case _ => Future.Unit
        }

      case Some(phone) => _checkQuota(phone, optTokenCaptcha, optIp, optCountry, countryIsVN)

      case _ => Future.Unit // Future.exception(ExceedQuotaException())
    }
  }

  private def _checkQuota(phone: String, optTokenCaptcha: Option[String], optIp: Option[String], optCountry: Option[String], countryIsVN: Option[Boolean]): Future[Unit] = Profiler(s"${getClass.getCanonicalName}._checkQuota") {
    val fn = for {
      // check quota phone
      exceedQuotaPhone <- verifyDataService.isExceedQuotaPhone(phone, None)
      _ <- optTokenCaptcha match {
        case None if exceedQuotaPhone => Future.exception(ExceedQuotaException(s"captcha:missing,phone:true,country:${optCountry.getOrElse("undef")}"))
        case Some(tokenCaptcha) if exceedQuotaPhone =>
          captchaService.verifyCaptchaWithCache(tokenCaptcha).flatMap {
            case true =>
              optIp.foreach(realIP => verifyDataService.incrQuotaIP(realIP))
              Future.exception(IgnoreProcess())
            case _ => Future.exception(InvalidTokenCaptchaException(s"captcha:invalid,phone:true,country:${optCountry.getOrElse("undef")}"))
          }
        case _ => Future.Unit
      }

      // check quota ip
      _ <- optIp match {
        case Some(ip) =>
          verifyDataService.isExceedQuotaIP(ip) flatMap {
            case true if optTokenCaptcha.isEmpty => Future.exception(ExceedQuotaException(s"captcha:missing,ip:true,country:${optCountry.getOrElse("undef")}"))
            case true if optTokenCaptcha.isDefined =>
              captchaService.verifyCaptchaWithCache(optTokenCaptcha.get).flatMap {
                case true =>
                  optIp.foreach(realIP => verifyDataService.incrQuotaIP(realIP).liftToTry)
                  Future.exception(IgnoreProcess())
                case _ => Future.exception(InvalidTokenCaptchaException(s"captcha:invalid,ip:true,country:${optCountry.getOrElse("undef")}"))
              }
            case _ =>
              optIp.foreach(realIP => verifyDataService.incrQuotaIP(realIP).liftToTry)
              Future.Unit
          }
        case _ => Future.Unit
      }

      // check country
      _ <- {
        if (countryIsVN.forall(_ == true)) Future.Unit
        else if (optTokenCaptcha.isEmpty) Future.exception(ExceedQuotaException(s"captcha:missing,country:${optCountry.getOrElse("undef")}"))
        else {
          captchaService.verifyCaptchaWithCache(optTokenCaptcha.get).flatMap {
            case true => Future.exception(IgnoreProcess())
            case _ => Future.exception(InvalidTokenCaptchaException(s"captcha:invalid,country:${optCountry.getOrElse("undef")}"))
          }
        }
      }

    } yield {}

    fn.rescue {
      case _: IgnoreProcess => Future.Unit
    } respond {
      case Return(_) => Profiler(s"${getClass.getCanonicalName}.apply-true") {}
      case Throw(e) => e match {
        case e1: ExceedQuotaException => Profiler(s"${getClass.getCanonicalName}.apply-false-${e1.message}") {}
        case _ => Profiler(s"${getClass.getCanonicalName}.apply-false-${e.getClass.getCanonicalName}-${e.getMessage}") {}
      }
    }

  }

  override def apply(request: Request, service: Service[Request, Response]): Future[Response] = {
    request.quotaChecked match {
      case true => service(request)
      case _ =>
        checkQuota(request).flatMap(_ => {
          request.setQuotaChecked()
          service(request)
        })
    }
  }

}

case class PhoneQuotaJwtIfNotExistPhoneFilter @Inject()(
                                                         profileService: ProfileService,
                                                         verifyDataService: VerifyDataService,
                                                         captchaService: CaptchaService
                                                       ) extends BasePhoneQuotaJwtFilter {

  override val needCheckExistPhone: Boolean = true
  override val applyToExistPhone: Boolean = false
}

case class PhoneQuotaJwtIfExistPhoneFilter @Inject()(
                                                      profileService: ProfileService,
                                                      verifyDataService: VerifyDataService,
                                                      captchaService: CaptchaService
                                                    ) extends BasePhoneQuotaJwtFilter {

  override val needCheckExistPhone: Boolean = true
  override val applyToExistPhone: Boolean = true

}

case class PhoneQuotaJwtFilter @Inject()(
                                          profileService: ProfileService,
                                          verifyDataService: VerifyDataService,
                                          captchaService: CaptchaService
                                        ) extends BasePhoneQuotaJwtFilter {

  override val needCheckExistPhone: Boolean = false
  override val applyToExistPhone: Boolean = true

}

//case class RegisterByPhoneJwtRequest(
//                                      phoneNumber: String,
//                                      email: Option[String],
//                                      tokenCaptcha: Option[String] = None
//                                    )