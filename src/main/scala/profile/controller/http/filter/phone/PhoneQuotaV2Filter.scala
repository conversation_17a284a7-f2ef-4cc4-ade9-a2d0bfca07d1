package profile.controller.http.filter.phone

import com.twitter.finagle.http._

/**
 * <AUTHOR>
 */
//abstract class BasePhoneQuotaV2Filter extends SimpleFilter[Request, Response] {
//
//  val authenService: AuthenJwtService
//  val verifyDataService: VerifyDataService
//  val captchaService: CaptchaService
//
//  val needCheckExistPhone: Boolean
//  val applyToExistPhone: Boolean
//
//
//  private def checkQuota(request: Request): Future[Unit] = Profiler(s"${getClass.getCanonicalName}.checkQuota") {
//    val reqNode = JsonHelper.readTree(request.contentString)
//    val optPhone = Option(reqNode.path("phone").asText(null)).filter(_.nonEmpty).map(PhoneUtils.normalizePhoneIgnorePlus)
//
//    val optCountry = request.headerMap.find(_._1.equalsIgnoreCase("cf-ipcountry")).map(_._2)
//    val countryIsVN = optCountry.map(_.equalsIgnoreCase("VN"))
//
//    val optIp = request.headerMap.find(_._1.equalsIgnoreCase("X-Real-IP")).map(_._1).filter(_.nonEmpty)
//      .orElse(Option(request.remoteAddress).flatMap(v => Option(v.getHostAddress))).filter(_.nonEmpty)
//
//    val optTokenCaptcha = Option(JsonHelper.readTree(request.contentString).path("token_captcha").asText(null)).filter(_.nonEmpty)
//
//    optPhone match {
//      case Some(phone) if needCheckExistPhone =>
//        authenService.isExistPhone(phone).flatMap {
//          case x if x == applyToExistPhone => _checkQuota(phone, optTokenCaptcha, optIp, optCountry, countryIsVN)
//          case _ => Future.Unit
//        }
//
//      case Some(phone) => _checkQuota(phone, optTokenCaptcha, optIp, optCountry, countryIsVN)
//
//      case _ => Future.exception(ExceedQuotaException())
//    }
//  }
//
//  private def _checkQuota(phone: String, optTokenCaptcha: Option[String], optIp: Option[String], optCountry: Option[String], countryIsVN: Option[Boolean]): Future[Unit] = Profiler(s"${getClass.getCanonicalName}._checkQuota") {
//    val fn = for {
//      // check quota phone
//      exceedQuotaPhone <- verifyDataService.isExceedQuotaWithPhone(phone)
//      _ <- optTokenCaptcha match {
//        case None if exceedQuotaPhone => Future.exception(ExceedQuotaException(s"captcha:missing,phone:true,country:${optCountry.getOrElse("undef")}"))
//        case Some(tokenCaptcha) if exceedQuotaPhone =>
//          captchaService.verifyCaptcha(tokenCaptcha).flatMap {
//            case true =>
//              optIp.foreach(realIP => verifyDataService.increaseQuotaWithIP(realIP))
//              Future.exception(IgnoreProcess())
//            case _ => Future.exception(ExceedQuotaException(s"captcha:invalid,phone:true,country:${optCountry.getOrElse("undef")}"))
//          }
//        case _ => Future.Unit
//      }
//
//      // check quota ip
//      _ <- optIp match {
//        case Some(ip) =>
//          verifyDataService.isExceedQuotaWithIP(ip) flatMap {
//            case true if optTokenCaptcha.isEmpty => Future.exception(ExceedQuotaException(s"captcha:missing,ip:true,country:${optCountry.getOrElse("undef")}"))
//            case true if optTokenCaptcha.isDefined =>
//              captchaService.verifyCaptcha(optTokenCaptcha.get).flatMap {
//                case true =>
//                  optIp.foreach(realIP => verifyDataService.increaseQuotaWithIP(realIP).liftToTry)
//                  Future.exception(IgnoreProcess())
//                case _ => Future.exception(ExceedQuotaException(s"captcha:invalid,ip:true,country:${optCountry.getOrElse("undef")}"))
//              }
//            case _ =>
//              optIp.foreach(realIP => verifyDataService.increaseQuotaWithIP(realIP).liftToTry)
//              Future.Unit
//          }
//        case _ => Future.Unit
//      }
//
//      // check country
//      _ <- {
//        if (countryIsVN.forall(_ == true)) Future.Unit
//        else if (optTokenCaptcha.isEmpty) Future.exception(ExceedQuotaException(s"captcha:missing,country:${optCountry.getOrElse("undef")}"))
//        else {
//          captchaService.verifyCaptcha(optTokenCaptcha.get).flatMap {
//            case true => Future.exception(IgnoreProcess())
//            case _ => Future.exception(ExceedQuotaException(s"captcha:invalid,country:${optCountry.getOrElse("undef")}"))
//          }
//        }
//      }
//
//    } yield {}
//
//    fn.rescue {
//      case _: IgnoreProcess => Future.Unit
//    } respond {
//      case Return(_) => Profiler(s"${getClass.getCanonicalName}.apply-true") {}
//      case Throw(e) => e match {
//        case e1: ExceedQuotaException => Profiler(s"${getClass.getCanonicalName}.apply-false-${e1.message}") {}
//        case _ => Profiler(s"${getClass.getCanonicalName}.apply-false-${e.getClass.getCanonicalName}-${e.getMessage}") {}
//      }
//    }
//
//  }
//
//  override def apply(request: Request, service: Service[Request, Response]): Future[Response] = {
//    request.quotaChecked match {
//      case true => service(request)
//      case _ =>
//        checkQuota(request).flatMap(_ => {
//          request.setQuotaChecked()
//          service(request)
//        })
//    }
//  }
//
//}

//case class PhoneQuotaV2Filter @Inject()(
//                                         authenV2Service: AuthenV2Service,
//                                         verifyDataService: VerifyDataService,
//                                         captchaService: CaptchaService
//                                       ) extends BasePhoneQuotaV2Filter {
//
//  override val needCheckExistPhone: Boolean = false
//  override val applyToExistPhone = false
//
//}
//
//case class PhoneQuotaV2IfExistPhoneFilter @Inject()(
//                                                     authenV2Service: AuthenV2Service,
//                                                     verifyDataService: VerifyDataService,
//                                                     captchaService: CaptchaService
//                                                   ) extends BasePhoneQuotaV2Filter {
//
//  override val needCheckExistPhone: Boolean = true
//  override val applyToExistPhone: Boolean = true
//
//}
//
//case class PhoneQuotaV2IfNotExistPhoneFilter @Inject()(
//                                                        authenService: AuthenV2Service,
//                                                        verifyDataService: VerifyDataService,
//                                                        captchaService: CaptchaService
//                                                      ) extends BasePhoneQuotaV2Filter {
//
//  override val needCheckExistPhone: Boolean = true
//  override val applyToExistPhone: Boolean = false
//}