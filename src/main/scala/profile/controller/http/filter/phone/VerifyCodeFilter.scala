package profile.controller.http.filter.phone

import javax.inject.Inject

import com.twitter.finagle.http.{Request, Response}
import com.twitter.finagle.{Service, SimpleFilter}
import com.twitter.util.Future
import profile.service.VerifyDataService
import profile.controller.http.filter.parser.DataRequestContext._

/**
 * <AUTHOR>
 */
trait VerifyCodeFilterRequest {
  def getPhone(): String

  def getVerifyCode(): String
}

class VerifyCodeFilter @Inject()(phoneAccountService: VerifyDataService) extends SimpleFilter[Request, Response] {
  override def apply(request: Request, service: Service[Request, Response]): Future[Response] = {

    val bodyReq: VerifyCodeFilterRequest = request.requestData
    phoneAccountService.verifyCodeWithPhone(bodyReq.getPhone(), "",bodyReq.getVerifyCode(), true).flatMap(f => service(request))
  }
}