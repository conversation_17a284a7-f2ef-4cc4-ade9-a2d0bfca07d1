package profile.controller.http.filter.phone

import profile.controller.http.filter.parser.DataRequestContext._

/**
 * <AUTHOR>
 */
trait PhoneQuotaFilterRequest {

  def getPhoneForQuota(): String

  def optTokenCaptcha(): Option[String]

}

//class PhoneQuotaFilter @Inject()(verifyDataService: VerifyDataService, captchaService: CaptchaService) extends SimpleFilter[Request, Response] {
//
//  override def apply(request: Request, service: Service[Request, Response]): Future[Response] = {
//    val phoneRequest: PhoneQuotaFilterRequest = request.requestData
//
//    verifyDataService.isExceedQuotaWithPhone(phoneRequest.getPhoneForQuota(), None).flatMap({
//      case true if request.quotaChecked => service(request)
//      case true if phoneRequest.optTokenCaptcha().isEmpty => Future.exception(ExceedQuotaException())
//      case true => captchaService.verifyCaptcha(phoneRequest.optTokenCaptcha().get).flatMap({
//        case true =>
//          request.setQuotaChecked()
//          service(request)
//        case _ => Future.exception(InvalidTokenCaptchaException("Captcha invalid"))
//      })
//      case _ => service(request)
//    })
//  }
//}
