package profile.controller.http.filter.phone

/**
 * <AUTHOR>
 */

trait PhoneFilterRequest {
  def getPhone(): String
}

trait PhoneAndEmailFilterRequest {
  def getNormPhone(): String

  def optEmail(): Option[String]
}

//@Singleton
//class MustNotExistPhoneAndEmailFilter @Inject()(caasService: CaasService) extends SimpleFilter[Request, Response] {
//
//  private val lockCreateUser = new AsyncMutexLockManager(1)
//
//  override def apply(request: Request, service: Service[Request, Response]): Future[Response] = {
//    try {
//      val filterRequest: PhoneAndEmailFilterRequest = request.requestData
//
//      lockCreateUser.withLock("") {
//        caasService.getActiveUserWithPhoneOrEmail(filterRequest.getNormPhone(), filterRequest.optEmail()).flatMap({
//          case Some(user) =>
//            if (user.phone.contains(filterRequest.getNormPhone())) Future.exception(AlreadyExistPhoneException(filterRequest.getNormPhone()))
//            else if (user.email.isDefined && user.email == filterRequest.optEmail()) Future.exception(AlreadyExistEmailException(filterRequest.optEmail().get))
//            else Future.exception(AlreadyExistUserException(user.username.getOrElse("")))
//          case _ => service(request)
//        })
//      }
//    } catch {
//      case e: Exception => Future.exception(e)
//    }
//  }
//}

//
//class MustExistPhoneFilter @Inject()(userProfileService: UserProfileService) extends SimpleFilter[Request, Response] {
//
//  override def apply(request: Request, service: Service[Request, Response]): Future[Response] = {
//    try {
//      val phoneRequest: PhoneFilterRequest = request.requestData
//      if (!PhoneUtils.isPhoneNumber(phoneRequest.getPhone())) throw InvalidPhoneException()
//      userProfileService.isExistPhoneNumber(phoneRequest.getPhone()).flatMap({
//        case false => Future.exception(NotExistPhoneException())
//        case _ => service(request)
//      })
//    } catch {
//      case e: Exception => Future.exception(e)
//    }
//  }
//}