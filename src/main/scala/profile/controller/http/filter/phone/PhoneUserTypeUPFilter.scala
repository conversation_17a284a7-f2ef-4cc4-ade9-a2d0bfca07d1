package profile.controller.http.filter.phone

import profile.controller.http.filter.parser.DataRequestContext._

/**
 * <AUTHOR>
 */
//class PhoneUserTypeUPFilter @Inject()(
//  userProfileService: UserProfileService
//) extends SimpleFilter[Request, Response] {
//  override def apply(request: Request, service: Service[Request, Response]): Future[Response] = {
//    val phoneRequest: PhoneFilterRequest = request.requestData
//
//    userProfileService.getUsernameByPhone(phoneRequest.getPhone()).flatMap({
//      case Some(x) =>
//        userProfileService.getUserProfile(x).flatMap({
//          case Some(x) if x.oauthType.isEmpty || x.oauthType.get.equals(LoginType.U_P.toString) => service(request)
//          case _ => Future.exception(new NotAuthTypeUserPassException)
//        })
//      case _ => Future.exception(new NotExistPhoneException)
//    })
//  }
//}