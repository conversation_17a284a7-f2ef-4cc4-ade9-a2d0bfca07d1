package profile.controller.http.filter.parser

import com.twitter.finagle.http._
import com.twitter.finagle.{Service, SimpleFilter}
import com.twitter.inject.Logging
import com.twitter.util.Future
import profile.controller.http.request.RegisterByPhoneJwtRequest
import profile.util.HttpRequestHelper

/**
 * <AUTHOR> 1/29/24 06:40
 */

case class RegisterUserByPhoneJwtParser() extends SimpleFilter[Request, Response] with Logging {
  override def apply(request: Request, service: Service[Request, Response]): Future[Response] = {
    val bodyRequest = HttpRequestHelper.parse[RegisterByPhoneJwtRequest](request)
    DataRequestContext.setDataRequest(request, bodyRequest)
    service(request)
  }
}