package profile.controller.http.filter.parser

import com.twitter.finagle.http.{Request, Response}
import com.twitter.finagle.{Service, SimpleFilter}
import com.twitter.inject.Logging
import com.twitter.util.Future
import profile.domain.request.ResetPasswordRequest
import profile.util.HttpRequestHelper

/**
 * <AUTHOR> 1/29/24 07:08
 */

case class ResetPasswordJwtParser() extends SimpleFilter[Request, Response] with Logging {
  override def apply(request: Request, service: Service[Request, Response]): Future[Response] = {
    try {
      val bodyRequest = HttpRequestHelper.parse[ResetPasswordRequest](request)
      DataRequestContext.setDataRequest(request, bodyRequest)
      service(request)
    } catch {
      case e: Throwable => Future.exception(e)
    }
  }
}