package profile.controller.http.filter.parser

import caas.service.CaasService
import com.twitter.finagle.http.{Request, Response}
import com.twitter.finagle.{Service, SimpleFilter}
import com.twitter.util.{Future, Try}

import javax.inject.Inject
import profile.domain.LoginType
import profile.domain.request.UserOAuthBodyRequest
import profile.exception.{InvalidParamException, NeedPhoneException, NeedRegisterException}
import profile.repository._
import profile.service.ProfileService
import profile.util.VHMConfig
import vn.vhm.common.util.JsonHelper

/**
 * <AUTHOR>
 */

trait LoginOAuthReq

case class LoginOAuthRequest(oauthType: String, id: String, token: String, oauthInfo: OAuthRepository) extends LoginOAuthReq

case class LoginOAuthByUsernameRequest(username: String) extends LoginOAuthReq

class UserLoginOAuthParser @Inject()(
                                      userProfileService: ProfileService,
                                      caasService: CaasService
                                    ) extends SimpleFilter[Request, Response] {
  override def apply(request: Request, service: Service[Request, Response]): Future[Response] = {

    if (!Option(request.contentString).exists(_.nonEmpty)) throw InvalidParamException("request_payload_empty")
    val oAuthReq = Try {
      JsonHelper.fromJson[UserOAuthBodyRequest](request.contentString)
    }.getOrElse(throw InvalidParamException("invalid_json_format"))

    for {
      // valid oauth data
      oauthInfo <- {
        // get oauth info
        LoginType.get(oAuthReq.oauthType) match {
          case LoginType.OAUTH_GOOGLE => Future.value(GoogleOAuthRepository(oAuthReq.id, oAuthReq.token))
          case LoginType.OAUTH_FACEBOOK => Future.value(FacebookOAuthRepository(oAuthReq.id, oAuthReq.token))
          case LoginType.OAUTH_APPLE => Future.value(AppleOAuthRepository(oAuthReq.id, oAuthReq.token, oAuthReq.fullName, None))
          case _ => Future.exception(new UnsupportedOperationException(s"Unsupported oauthType=${oAuthReq.oauthType}"))
        }
      }
      req <- VHMConfig.isNeedVerifyPhone(oauthInfo.oauthType) match {
//        case true => userProfileService.optUserProfile(oauthInfo.getUsername).flatMap {
//          case Some(x) if x.phoneNumber.isEmpty => Future.exception(NeedPhoneException())
//          case Some(_) => Future.value(LoginOAuthRequest(oAuthReq.oauthType, oAuthReq.id, oAuthReq.token, oauthInfo))
//          case _ =>
//            // Nếu ko có user nào với id của oauth thì xem thử nó có email ko, có thì cho login luôn
//            if (oauthInfo.verifiedEmail && oauthInfo.getEmail.isDefined) {
//              caasService.getActiveUsernameByEmail(oauthInfo.getEmail.get).map {
//                case Some(username) => LoginOAuthByUsernameRequest(username)
//                case _ => throw NeedRegisterException(info = Option(oauthInfo))
//              }
//            } else Future.exception(NeedRegisterException(info = Option(oauthInfo)))
//        }
        case _ => Future.value(oauthInfo)
      }
      _ = DataRequestContext.setDataRequest(request, req)
      resp <- service(request)
    } yield resp
  }
}
