package profile.controller.http.filter.parser

import com.fasterxml.jackson.annotation.JsonProperty
import com.twitter.finagle.http.{Request, Response}
import com.twitter.finagle.{Service, SimpleFilter}
import com.twitter.finatra.validation.constraints.NotEmpty
import com.twitter.inject.Logging
import com.twitter.util.Future
import profile.controller.http.filter.email.EmailFilterRequest
import profile.controller.http.filter.phone.PhoneFilterRequest
import profile.service.VerifyDataService
import profile.util.HttpRequestHelper

import javax.inject.Inject

/**
 * <AUTHOR>
 */

case class RegisterUserBodyRequest(
                                    @JsonProperty("full_name") fullName: String,
                                    @NotEmpty email: Option[String],
                                    password: String,
                                    @JsonProperty("phone_token") phoneToken: String
                                  )

case class RegisterUserRequest(
                                @NotEmpty email: Option[String],
                                password: String,
                                normalizedPhone: String,
                                fullName: String,
                                rawPassword: Option[String] = None // Using in case admin create account for user
                              ) extends PhoneFilterRequest with EmailFilterRequest {

  override def getPhone(): String = normalizedPhone

  override def getEmail(): Option[String] = email
}

case class RegisterUserParser @Inject()(phoneAccountService: VerifyDataService) extends SimpleFilter[Request, Response] with Logging {

  override def apply(request: Request, service: Service[Request, Response]): Future[Response] = {
    val bodyRequest = HttpRequestHelper.parse[RegisterUserBodyRequest](request)
    for {
      phoneNumber <- phoneAccountService.verifyTokenPhone(bodyRequest.phoneToken, delete = false)
      _ = DataRequestContext.setDataRequest(request, RegisterUserRequest(bodyRequest.email, bodyRequest.password, phoneNumber, bodyRequest.fullName))
      resp <- service(request)
      _ = phoneAccountService.deleteTokenPhone(bodyRequest.phoneToken).rescue { case e: Exception => Future.value(error("", e)) }
    } yield resp
  }
}

//case class InternalRegisterUserBodyRequest(
//                                            @JsonProperty("full_name") fullName: String,
//                                            phone: String,
//                                            email: Option[String] = None
//                                          ) {
//  val normalizedPhone = PhoneUtils.normalizePhoneIgnorePlus(phone)
//  val rawPassword = UUID.randomUUID().toString.substring(0, 12)
//
//  // Giả lập ra password như phía frontend
//  val password: String = PasswordUtils.hashPasswordLikeFromFrontend(rawPassword)
//}

//case class InternalRegisterUserParser() extends SimpleFilter[Request, Response] with Logging {
//  override def apply(request: Request, service: Service[Request, Response]): Future[Response] = {
//    val bodyRequest = JsonHelper.fromJson[InternalRegisterUserBodyRequest](request.contentString)
//    DataRequestContext.setDataRequest(
//      request,
//      RegisterUserRequest(
//        email = bodyRequest.email,
//        password = bodyRequest.password,
//        normalizedPhone = bodyRequest.normalizedPhone,
//        fullName = bodyRequest.fullName,
//        rawPassword = Some(bodyRequest.rawPassword)
//      )
//    )
//    service(request)
//  }
//}