package profile.controller.http.filter.parser

import com.twitter.finagle.http.{Request, Response}
import com.twitter.finagle.{Service, SimpleFilter}
import com.twitter.util.Future
import profile.controller.http.filter.email.EmailFilterRequest
import profile.domain.request.UpdateEmailBodyRequest
import vn.vhm.common.util.JsonHelper

/**
 * <AUTHOR>
 */

case class UpdateEmailRequest(email: String) extends EmailFilterRequest {
  override def getEmail(): Option[String] = Some(email)
}

class UpdateEmailParser extends SimpleFilter[Request, Response] {
  override def apply(request: Request, service: Service[Request, Response]): Future[Response] = {
    val bodyRequest = JsonHelper.fromJson[UpdateEmailBodyRequest](request.contentString)
    DataRequestContext.setDataRequest(request, UpdateEmailRequest(bodyRequest.email))
    service(request)
  }
}
