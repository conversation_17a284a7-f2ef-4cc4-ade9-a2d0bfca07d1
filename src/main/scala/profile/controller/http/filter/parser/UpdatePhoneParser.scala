package profile.controller.http.filter.parser

import com.twitter.finagle.http.{Request, Response}
import com.twitter.finagle.{Service, SimpleFilter}
import com.twitter.inject.Logging
import com.twitter.util.Future
import javax.inject.Inject
import profile.controller.http.filter.phone.PhoneFilterRequest
import profile.domain.request.UpdatePhoneBodyRequest
import profile.service.VerifyDataService
import vn.vhm.common.util.JsonHelper

/**
 * <AUTHOR>
 */

case class UpdatePhoneRequest(phone: String) extends PhoneFilterRequest {
  override def getPhone(): String = phone
}

class UpdatePhoneParser @Inject()(verifyDataService: VerifyDataService) extends SimpleFilter[Request, Response] with Logging {
  override def apply(request: Request, service: Service[Request, Response]): Future[Response] = {
    val bodyRequest = JsonHelper.fromJson[UpdatePhoneBodyRequest](request.contentString)
    for {
      _ <- verifyDataService.verifyCodeWithPhone(bodyRequest.normalizedPhoneNumber, "", bodyRequest.verifyCode, deleteIfEqual = false)
      _ = DataRequestContext.setDataRequest(request, UpdatePhoneRequest(bodyRequest.normalizedPhoneNumber))
      resp <- service(request)
      _ <- verifyDataService.deleteVerifyCodeWithPhone(bodyRequest.normalizedPhoneNumber).rescue { case e: Exception => Future.value(error("", e)) }
    } yield resp
  }
}