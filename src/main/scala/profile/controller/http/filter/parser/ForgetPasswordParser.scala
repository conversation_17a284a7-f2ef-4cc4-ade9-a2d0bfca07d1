package profile.controller.http.filter.parser

/**
 * <AUTHOR>
 */
//case class ResetPasswordRequest(normPhone: String, newPassword: String) extends PhoneFilterRequest {
//  override def getPhone(): String = normPhone
//}

//class ForgetPasswordParser @Inject()(verifyDataService: VerifyDataService) extends SimpleFilter[Request, Response] with Logging {
//  override def apply(request: Request, service: Service[Request, Response]): Future[Response] = {
//    val bodyRequest = JsonHelper.fromJson[ForgetPasswordBodyRequest](request.contentString)
//    for {
//      _ <- verifyDataService.verifyCodeWithPhone(bodyRequest.normalizedPhone, "", bodyRequest.verifyCode, deleteIfEqual = false)
//      _ = DataRequestContext.setDataRequest(request, ResetPasswordRequest(bodyRequest.normalizedPhone, bodyRequest.password))
//      resp <- service(request)
//      _ <- verifyDataService.deleteVerifyCodeWithPhone(bodyRequest.normalizedPhone).rescue { case e: Exception => Future.value(error("", e)) }
//    } yield resp
//  }
//}
