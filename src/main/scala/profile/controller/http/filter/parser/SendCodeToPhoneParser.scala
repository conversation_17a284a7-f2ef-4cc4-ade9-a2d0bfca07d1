package profile.controller.http.filter.parser

import com.twitter.finagle.http.{Request, Response}
import com.twitter.finagle.{Service, SimpleFilter}
import com.twitter.finatra.validation.constraints.NotEmpty
import com.twitter.util.Future
import profile.controller.http.filter.phone.{PhoneAndEmailFilterRequest, PhoneQuotaFilterRequest}
import profile.exception.InvalidPhoneException
import profile.util.PhoneUtils
import vn.vhm.common.util.JsonHelper

/**
 * <AUTHOR>
 */
case class SendCodeToPhoneInternalReq(
                                       phoneNumber: String,
                                       @NotEmpty email: Option[String],
                                       tokenCaptcha: Option[String]
                                     ) extends PhoneQuotaFilterRequest with PhoneAndEmailFilterRequest {

  if (!vn.vhm.common.util.PhoneUtils.isPhoneNumber(phoneNumber)) throw InvalidPhoneException()

  val normPhone = PhoneUtils.normalizePhone(phoneNumber)

  override def getPhoneForQuota(): String = normPhone

  override def optTokenCaptcha(): Option[String] = tokenCaptcha

  override def getNormPhone(): String = normPhone

  override def optEmail(): Option[String] = email

}

case class SendCodeToPhoneBodyRequest(
                                       phoneNumber: String,
                                       @NotEmpty email: Option[String],
                                       tokenCaptcha: Option[String] = None
                                     )

class SendCodeToPhoneParser extends SimpleFilter[Request, Response] {
  override def apply(request: Request, service: Service[Request, Response]): Future[Response] = {
    try {
      val bodyRequest = JsonHelper.fromJson[SendCodeToPhoneBodyRequest](request.contentString)
      val registerByPhoneRequest = SendCodeToPhoneInternalReq(
        bodyRequest.phoneNumber,
        bodyRequest.email,
        bodyRequest.tokenCaptcha,
      )
      DataRequestContext.setDataRequest(request, registerByPhoneRequest)
      service(request)
    } catch {
      case e: Exception => Future.exception(e)
    }

  }
}