package profile.controller.http.filter.parser

import profile.controller.http.filter.user.UsernameFilterRequest

/**
 * <AUTHOR>
 */
case class LoginRequest(username: String, password: String, remember: <PERSON><PERSON><PERSON>) extends UsernameFilterRequest {
  override def getUsername: String = username

}

//class UserLoginParser @Inject()(userProfileService: UserProfileService,
//                                caasService: CaasService) extends SimpleFilter[Request, Response] {
//  override def apply(request: Request, service: Service[Request, Response]): Future[Response] = {
//    val loginBodyReq = JsonHelper.fromJson[LoginBodyRequest](request.contentString)
//    for {
//      username <- {
//        if (loginBodyReq.loginType.contains("email") || loginBodyReq.isEmail)
//          caasService.getActiveUsernameByVerifiedEmail(loginBodyReq.identify)
//            .map(_.getOrElse(throw UnAuthenException(message = "email invalid")))
//        else if (loginBodyReq.loginType.contains("phone") || loginBodyReq.isPhone)
//          caasService.getActiveUsernameByVerifiedPhone(loginBodyReq.identify)
//            .map(_.getOrElse(throw UnAuthenException(message = "phone invalid")))
//        else Future.value(loginBodyReq.identify)
//      }
//      _ = DataRequestContext.setDataRequest(request, LoginRequest(username, loginBodyReq.password, loginBodyReq.remember))
//      resp <- service(request)
//    } yield resp
//  }
//}