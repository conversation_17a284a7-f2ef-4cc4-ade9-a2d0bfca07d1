package profile.controller.http.filter.parser

import com.twitter.finagle.http.{Request, Response}
import com.twitter.finagle.{Service, SimpleFilter}
import com.twitter.inject.Logging
import com.twitter.util.Future
import profile.controller.http.filter.email.EmailFilterRequest
import profile.controller.http.filter.phone.PhoneFilterRequest
import profile.domain.LoginType
import profile.domain.request.RegisterOAuthUserBodyRequest
import profile.repository.{AppleOAuthRepository, FacebookOAuthRepository, GoogleOAuthRepository, OAuthRepository}
import profile.service.VerifyDataService
import profile.util.HttpRequestHelper

import javax.inject.Inject

/**
 * <AUTHOR>
 */

case class RegisterOAuthUserRequest(
                                     oauthType: String,
                                     id: String,
                                     token: String,
                                     oauthInfo: OAuthRepository,
                                     password: String) extends EmailFilterRequest with PhoneFilterRequest {

  override def getEmail(): Option[String] = {
    if (oauthInfo.verifiedEmail) oauthInfo.getEmail
    else None
  }

  override def getPhone(): String = oauthInfo.getPhoneNumber.get
}

class RegisterOAuthUserParser @Inject()(phoneAccountService: VerifyDataService) extends SimpleFilter[Request, Response] with Logging {

  override def apply(request: Request, service: Service[Request, Response]): Future[Response] = {


    val bodyRequest = HttpRequestHelper.parse[RegisterOAuthUserBodyRequest](request)

    bodyRequest.phoneToken match {
      case Some(phoneToken) => phoneAccountService.verifyTokenPhone(phoneToken, delete = false)
        .flatMap(phone => parseWithPhoneNumber(phone, bodyRequest, request, service))
        .flatMap(resp => phoneAccountService.deleteTokenPhone(phoneToken).onFailure(fn => error("", fn)).map(_ => resp))
      case _ => phoneAccountService.verifyCodeWithPhone(bodyRequest.normPhone.get, "", bodyRequest.verifyCode.get, deleteIfEqual = false)
        .flatMap(_ => parseWithPhoneNumber(bodyRequest.normPhone.get, bodyRequest, request, service))
        .flatMap(resp => phoneAccountService.deleteVerifyCodeWithPhone(bodyRequest.normPhone.get).onFailure(fn => error("", fn)).map(_ => resp))
    }

  }

  private def parseWithPhoneNumber(
                                    phoneNormalized: String, bodyRequest: RegisterOAuthUserBodyRequest,
                                    request: Request, service: Service[Request, Response]): Future[Response] = {

    // get oauth info
    val oauthUserInfo = LoginType.get(bodyRequest.oauthType) match {
      case LoginType.OAUTH_GOOGLE => Future.value(GoogleOAuthRepository(bodyRequest.id, bodyRequest.token))
      case LoginType.OAUTH_FACEBOOK => Future.value(FacebookOAuthRepository(bodyRequest.id, bodyRequest.token))
      case LoginType.OAUTH_APPLE => Future.value(AppleOAuthRepository(bodyRequest.id, bodyRequest.token, bodyRequest.fullName, None))
      case _ => Future.exception(new UnsupportedOperationException(s"Unsupported oauthType=${bodyRequest.oauthType}"))
    }
    // valid oauth data
    oauthUserInfo.map(oauthInfo => {

      oauthInfo.setPhoneNumber(phoneNormalized)

      val registerUserOAuthRequest = RegisterOAuthUserRequest(bodyRequest.oauthType, bodyRequest.id, bodyRequest.token, oauthInfo, bodyRequest.password)
      DataRequestContext.setDataRequest(request, registerUserOAuthRequest)

    }).flatMap(_ => service(request))
  }
}


