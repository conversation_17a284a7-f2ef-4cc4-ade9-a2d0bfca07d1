package profile.controller.http.filter.email

import com.twitter.finagle.http.{Request, Response}
import com.twitter.finagle.{Service, SimpleFilter}
import com.twitter.util.Future
import javax.inject.Inject
import profile.controller.http.filter.parser.DataRequestContext._
import profile.service.ProfileService
import profile.util.CustomUtils

/**
 * <AUTHOR>
 */

trait EmailFilterRequest {
  def getEmail(): Option[String]
}

class MustNotExistEmailFilter @Inject()(userProfileService: ProfileService) extends SimpleFilter[Request, Response] {
  override def apply(request: Request, service: Service[Request, Response]): Future[Response] = {
    val emailFilterRequest: EmailFilterRequest = request.requestData
    emailFilterRequest.getEmail() match {
      case Some(x) if !CustomUtils.isEmail(x) => Future.exception(new Exception("email is invalid"))
      case Some(x) => userProfileService.checkEmailExist(x).flatMap(_ => service(request))
      case _ => service(request)
    }
  }
}