package profile.controller.http

import com.twitter.finagle.http.Request
import com.twitter.finatra.http.Controller
import com.twitter.finatra.http.annotations.RouteParam
import profile.domain.response.UserResponse
import profile.service.AuthenJwtService
import profile.util.Constant._
import vn.vhm.common.domain.profiling.Profiler

import javax.inject.Inject

/**
 * <AUTHOR> 8/21/20 3:24 PM
 */
class InternalAuthController @Inject()(
                                        authenService: AuthenJwtService,
                                      ) extends Controller with BaseSecretKeyController {

  post("/internal/user/:id/login") {
    req: InternalLoginUserRequest => {
      Profiler("/internal/user/:id/login HTTP-POST") {

        val sk = verifySecretKey(req.request, "internal_auth")

        info(s"Internal login: ${req.id}, $sk")

        authenService.loginById(req.id, jwtRequestedBy = JWT_CLAIM_REQUIRED_BY_VALUE_INTERNAL, req.additionalClaims).map(result => {
          response.ok(UserResponse(
            code = 0,
            userInfo = result.userInfo,
            userProfile = result.userProfile,
            jwt = result.jwt.copy(refreshToken = None)
          ))
        })

      }
    }
  }
}

case class InternalLoginUserRequest(
                                     @RouteParam id: Long,
                                     additionalClaims: Map[String, Any],
                                     @Inject request: Request
                                   )