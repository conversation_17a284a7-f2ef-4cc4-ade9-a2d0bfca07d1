//package profile.controller.thrift
//
//import caas.service.CaasService
//import com.google.inject.Inject
//import com.twitter.finagle.thrift.ClientId
//import com.twitter.finatra.thrift.Controller
//import com.twitter.inject.Logging
//import com.twitter.scrooge.{Request, Response}
//import com.twitter.util.{Future, Return, Throw, Try}
//import profile.domain.entity.{SessionInfo, UserAuthInfo, UserProfile, User}
//import profile.service.AuthenJwtService
//import profile.service.ProfileCoreService
//import vn.vhm.common.domain.Implicits._
//import vn.vhm.common.domain.OptionImplicits.OptionImplicitAny
//import vn.vhm.common.domain.profiling.Profiler
//import vn.vhm.common.exception.RException
//import vn.vhm.common.util.{JsonHelper, Utils}
//import vn.vhm.domain.thrift.userprofile._
//import vn.vhm.service.TUserProfileService
//import vn.vhm.service.TUserProfileService._
//
///**
// * <AUTHOR>
// */
//class TUserProfileController @Inject()(
//                                        authenService: AuthenJwtService,
//                                        userProfileService: ProfileCoreService,
//                                        caasService: CaasService
//                                      ) extends Controller(TUserProfileService) with Logging {
//
//  protected val clazz = getClass.getCanonicalName
//
//  private def getClientId(): Option[String] = ClientId.current.map(_.name)
//
//  protected def getError(ex: Throwable): String = {
//    ex match {
//      case e: RException => e.error
//      case _: NullPointerException => ex.getClass.getCanonicalName
//      case _ => ex.getClass.getCanonicalName
//    }
//  }
//
//  def getErrorMsg(ex: Throwable): String = {
//    ex match {
//      case _: NullPointerException => ex.getClass.getCanonicalName
//      case e: RException => e.getMessage
//      case _ => Utils.getStackTraceAsString(ex)
//    }
//  }
//
//  protected def logError(msg: String, ex: Throwable = null): Unit = {
//    val clientInfo = getClientId().fold("")(name => s"[$name] ")
//    ex match {
//      case null => error(msg)
//      case _: org.apache.shiro.authc.AuthenticationException => error(s"$msg: ${ex.getMessage}")
//      case _: vn.vhm.common.exception.NotFoundException => warn(s"${ex.getClass.getCanonicalName}\t$clientInfo$msg\t${ex.getMessage}")
//      case e: RException => error(s"$msg: ${e.error} - ${e.getMessage}")
//      case _ => error(s"$clientInfo$msg", ex)
//    }
//  }
//
//  protected def thriftExec[A, B](fn: => Future[A])(ifSuccess: A => Future[B])(ifFailed: Throwable => Future[B]): Future[B] = {
//    try {
//      fn transform {
//        case Return(r) => ifSuccess(r)
//        case Throw(e) => ifFailed(e)
//      }
//    } catch {
//      case e: Exception => ifFailed(e)
//    }
//  }
//
//  protected def thriftExec2[T](fn: => Future[T])(ifFailed: Throwable => Future[T]): Future[T] = {
//    try {
//      fn rescue {
//        case e: Exception => ifFailed(e)
//      }
//    } catch {
//      case e: Exception => ifFailed(e)
//    }
//  }
//
//  handle(Ping).withFn {
//    _: Request[Ping.Args] => Future.value("pong").map(v => Response(v))
//  }
//
//  handle(GetUserProfile).withFn {
//    _req: Request[GetUserProfile.Args] =>
//      val req = _req.args
//      Profiler(s"$clazz.getUserProfile") {
//        thriftExec2 {
//          userProfileService.getUserProfile(req.username).map(r => TUserProfileResp(true, Some(asT(r))))
//        }(e => async {
//          logError(s"getUserProfile(${JsonHelper.toJson(req, false)})", e)
//          TUserProfileResp(false, None)
//        })
//      }.map(v => Response(v))
//  }
//
//  handle(MgetUserProfile).withFn {
//    _req: Request[MgetUserProfile.Args] =>
//      val req = _req.args
//      Profiler(s"$clazz.MgetUserProfile") {
//        thriftExec2 {
//          userProfileService.getUserProfiles(req.listUsername).map(_.map(t => t._1 -> asT(t._2)))
//            .map(r => TMapUserProfileResp(true, mapUserProfile = Some(r)))
//        }(e => async {
//          logError(s"mgetUserProfile(${JsonHelper.toJson(req)})", e)
//          TMapUserProfileResp(false, message = Some(getError(e)), messageDetail = Some(getErrorMsg(e)))
//        })
//      }.map(v => Response(v))
//  }
//
//  handle(GetUserProfileBySessionID).withFn {
//    req: Request[GetUserProfileBySessionID.Args] =>
//      val args = req.args
//      Profiler(s"$clazz.getUserProfileBySessionID") {
//        thriftExec2 {
//          for {
//            userInfo <- caasService.getUserBySession(args.sessionId)
//            userProfile <- if (userInfo.isDefined) userProfileService.getUserProfile(userInfo.get.username).liftToTry.map(_.toOption) else Future.None
//            result = userInfo match {
//              case Some(v) => TFullUserInfoResp(true, Some(asT(v)), userProfile.map(asT))
//              case _ => TFullUserInfoResp(false)
//            }
//          } yield result
//        }(e => async {
//          logError(s"getUserProfileBySessionID(${JsonHelper.toJson(args)})", e)
//          TFullUserInfoResp(false)
//        })
//      }.map(v => Response(v))
//  }
//
//  handle(GetUserProfileByAccessToken).withFn {
//    req: Request[GetUserProfileByAccessToken.Args] =>
//      val args = req.args
//      Profiler(s"$clazz.GetUserProfileByAccessToken") {
//        thriftExec2 {
//          for {
//            (username, _) <- async {
//              authenService.parseAccessToken(args.accessToken, false)
//            }
//            userInfo <- caasService.getUserByUsername(username)
//            userProfile <- if (userInfo.isDefined) userProfileService.getUserProfile(userInfo.get.username).liftToTry.map(_.toOption) else Future.None
//            result = userInfo match {
//              case Some(v) => TFullUserInfoResp(true, Some(asT(v)), userProfile.map(asT))
//              case _ => TFullUserInfoResp(false)
//            }
//          } yield result
//        }(e => async {
//          logError(s"GetUserProfileByAccessToken(${JsonHelper.toJson(args)})", e)
//          TFullUserInfoResp(false)
//        })
//      }.map(v => Response(v))
//  }
//
//  handle(GetUserProfileByUsername).withFn {
//    req: Request[GetUserProfileByUsername.Args] =>
//      val args = req.args
//      Profiler(s"$clazz.getUserProfileByUsername") {
//        thriftExec2 {
//          for {
//            userInfo <- caasService.getUserByUsername(args.username)
//            userProfile <- if (userInfo.isDefined) userProfileService.getUserProfile(userInfo.get.username).liftToTry.map(_.toOption) else Future.None
//            result = userInfo match {
//              case Some(v) => TFullUserInfoResp(true, Some(asT(v)), userProfile.map(asT))
//              case _ => TFullUserInfoResp(false)
//            }
//          } yield result
//        }(e => async {
//          logError(s"getUserProfileByUsername(${JsonHelper.toJson(args)})", e)
//          TFullUserInfoResp(false)
//        })
//      }.map(v => Response(v))
//  }
//
//  handle(MgetUserProfileByUsername).withFn {
//    req: Request[MgetUserProfileByUsername.Args] =>
//      val args = req.args
//      Profiler(s"$clazz.MgetUserProfileByUsername") {
//        thriftExec2 {
//          for {
//            mapUser <- caasService.mgetUserByUsername(args.listUsername).map(_.map(t => t._1 -> asT(t._2)))
//            listUsernameHasProfile = mapUser.keys.toSeq
//            mapProfile <- {
//              if (listUsernameHasProfile.nonEmpty) userProfileService.getUserProfiles(listUsernameHasProfile).map(_.map(t => t._1 -> asT(t._2)))
//              else Future.value(Map.empty[String, TUserProfile])
//            }
//          } yield TMapFullUserInfoResp(true, mapUserInfo = Some(mapUser), mapUserProfile = Some(mapProfile))
//        }(e => async {
//          logError(s"mgetUserProfileByUsername(${JsonHelper.toJson(args)})", e)
//          TMapFullUserInfoResp(false, message = Some(getError(e)), messageDetail = Some(getErrorMsg(e)))
//        })
//      }.map(v => Response(v))
//  }
//
//  handle(CheckSession).withFn {
//    req: Request[CheckSession.Args] =>
//      val args = req.args
//      Profiler(s"$clazz.checkSession") {
//        thriftExec2 {
//          authenService.checkSessionByRefreshToken(args.sessionId)
//            .map(r => TFullUserAuthInfoResp(exist = true, session = None, userInfo = Some(asT(r.userInfo)), userProfile = r.userProfile.map(asT)))
//        }(e => async {
//          logError(s"checkSession(${JsonHelper.toJson(args)})", e)
//          TFullUserAuthInfoResp(false)
//        })
//      }.map(v => Response(v))
//  }
//
//  handle(CheckAccessToken).withFn {
//    req: Request[CheckAccessToken.Args] =>
//      val args = req.args
//      Profiler(s"$clazz.CheckAccessToken") {
//        thriftExec2 {
//          async(authenService.parseAccessToken(args.accessToken, args.uncheckExpireTime.getOrElse(false))).map(tuple => {
//            TAccessTokenResp(
//              username = tuple._1.toSome,
//              payload = TAccessTokenPayloadValue(
//                phone = tuple._2.get("phone").map(_.toString),
//                phoneVerified = Try(tuple._2.get("phone_verified").map(_.toString.toInt)).toOption.flatten,
//                email = tuple._2.get("email").map(_.toString),
//                emailVerified = Try(tuple._2.get("email_verified").map(_.toString.toInt)).toOption.flatten,
//                displayName = tuple._2.get("display_name").map(_.toString),
//                avatar = tuple._2.get("avatar").map(_.toString),
//                additionalInfo = tuple._2.map(pair => pair._1 -> pair._2.toString).toSome.filter(_.nonEmpty),
//              ).toSome
//            )
//          })
//
//        }(e => async {
//          logError(s"CheckAccessToken(${JsonHelper.toJson(args)})", e)
//          TAccessTokenResp(error = getError(e).toSome, errorMessage = getErrorMsg(e).toSome)
//        })
//      }.map(v => Response(v))
//  }
//
//  handle(GetUserProfiles).withFn {
//    req: Request[GetUserProfiles.Args] =>
//      val args = req.args
//      Profiler(s"$clazz.getUserProfiles") {
//        thriftExec2 {
//          ???
//          //          userProfileService.getUserProfiles(args.from, args.size)
//          //            .map(f => TListUserProfileResp(f.total, Option(f.data).map(_.map(asT))))
//        }(e => async {
//          logError(s"getUserProfiles(${JsonHelper.toJson(args)})", e)
//          TListUserProfileResp(0, None, error = Some(getError(e)), errorMessage = Some(getErrorMsg(e)))
//        })
//      }.map(v => Response(v))
//  }
//
//  handle(SearchUserProfile).withFn {
//    req: Request[SearchUserProfile.Args] =>
//      val args = req.args
//      Profiler(s"$clazz.SearchUserProfile") {
//        thriftExec2 {
//          ???
//          //          userProfileService.searchUser(
//          //            args.from, args.size,
//          //            args.searchResultType.map(_.value),
//          //            args.gtCreatedTime, args.ltCreatedTime,
//          //            args.gtUpdatedTime, args.ltUpdatedTime,
//          //            args.orderByFields.getOrElse(Nil), args.orderByTypes.getOrElse(Nil)
//          //          ).map(v => TListUserProfileResp(v.total, Some(v.data.map(asTUserProfile))))
//        }(e => async {
//          logError(s"SearchUserProfile(${JsonHelper.toJson(args)})", e)
//          TListUserProfileResp(0, None, error = Some(getError(e)), errorMessage = Some(getErrorMsg(e)))
//        })
//      }.map(v => Response(v))
//  }
//
//  private def asT(userProfile: UserProfile): TUserProfile = {
//    TUserProfile(
//      username = userProfile.username,
//      displayName = userProfile.displayName,
//      email = userProfile.email,
//      avatar = userProfile.avatar,
//      phoneNumber = userProfile.phoneNumber,
//      phoneVerified = userProfile.phoneVerified.map(_ == 1),
//      familyName = userProfile.familyName,
//      givenName = userProfile.givenName,
//      additionalInfo = userProfile.extraInfo,
//      emailVerified = userProfile.emailVerified.map(_ == 1),
//      registerChannel = userProfile.registerChannel,
//      gender = userProfile.gender,
//      dayOfBirth = userProfile.dayOfBirth,
//      maritalStatus = userProfile.maritalStatus,
//      marriedDay = userProfile.marriedDay,
//      address = userProfile.address,
//      job = userProfile.job,
//      facebook = userProfile.facebook, linkedin = userProfile.linkedin, youtube = userProfile.youtube,
//      personalWebsite = userProfile.personalWebsite,
//      contactType = userProfile.contactType.map(_.flatMap(v => Option(v))),
//      locations = userProfile.locations.map(_.flatMap(v => Option(v))),
//      updatedTime = userProfile.updatedTime,
//      createdTime = None
//    )
//  }
//
//  private def asTUserProfile(user: User): TUserProfile = {
//    user.profile match {
//      case Some(profile) =>
//        asT(profile).copy(
//          updatedTime = user.updatedTime,
//          createdTime = user.createTime
//        )
//      case _ =>
//        TUserProfile(
//          username = user.username.getOrElse(""),
//          updatedTime = user.updatedTime,
//          createdTime = user.createTime
//        )
//    }
//
//  }
//
//  private def asT(sessionInfo: SessionInfo): TSessionInfo = {
//    TSessionInfo(sessionInfo.key, sessionInfo.value, sessionInfo.timeoutInMS, sessionInfo.domain, Some(sessionInfo.path))
//  }
//
//  private def asT(op: Option[SessionInfo]): Option[TSessionInfo] = {
//    op match {
//      case Some(x) => Some(asT(x))
//      case _ => None
//    }
//  }
//
//  private def asT(userInfo: UserAuthInfo): TUserInfo = {
//    TUserInfo(userInfo.username, userInfo.isActive, userInfo.createTime, userInfo.roles)
//  }
//
//  private def asT(userInfo: User): TUserInfo = {
//    TUserInfo(
//      userInfo.username.get,
//      userInfo.isActive.contains(1),
//      userInfo.createTime.getOrElse(0L)
//    )
//  }
//}
