package profile.domain

import vn.vhm.common.exception.NotFoundException

/**
 * <AUTHOR> 4/8/20 12:06 PM
 */
object LoginType extends Enumeration {
  type LoginType = Value

  val OAUTH_GOOGLE = Value("google")
  val OAUTH_FACEBOOK = Value("facebook")
  val OAUTH_APPLE = Value("apple")
  val U_P = Value("u_p")
  val PHONE = Value("phone")

  def get(v: String): LoginType = values.find(_.toString == v).getOrElse(throw NotFoundException(s"Not found oauthType $v"))

  def isOAuthType(v: String): Boolean = {
    get(v) match {
      case OAUTH_GOOGLE | OAUTH_FACEBOOK | OAUTH_APPLE => true
      case _ => false
    }
  }
}