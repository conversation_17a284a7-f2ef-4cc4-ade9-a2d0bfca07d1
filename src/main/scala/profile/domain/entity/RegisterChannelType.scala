package profile.domain.entity

import vn.vhm.common.exception.NotFoundException

/**
 * <AUTHOR> 1/27/24 16:07
 */
object RegisterChannelType extends Enumeration {

  type RegisterChannelType = Value

  val EMAIL = Value("em")
  val FACEBOOK = Value("fb")
  val GOOGLE = Value("gg")
  val APPLE = Value("ap")
  val AZURE = Value("az")
  val PHONE = Value("pn")

}

object OtpChannel extends Enumeration {

  type OtpChannel = Value

  val EMAIL = Value("email")
  val PHONE = Value("phone")

  def optByValue(value: String): Option[OtpChannel] = values.find(_.toString == value)

  def get(value: String): OtpChannel = optByValue(value).getOrElse(throw NotFoundException(s"Invalid verify status = $value"))
}
