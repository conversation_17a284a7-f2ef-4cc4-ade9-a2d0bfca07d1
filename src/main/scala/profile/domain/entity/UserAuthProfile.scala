package profile.domain.entity

import com.fasterxml.jackson.annotation.JsonIgnore
import profile.domain.response.CustomerDTO

/**
 * <AUTHOR>
 */
case class UserAuthProfile(
                            jwt: JwtToken,
                            userInfo: UserAuthInfo,
                            userProfile: Option[CustomerDTO] = None
                          ) {
  @JsonIgnore
  def getUsername: String = userInfo.userId

  @JsonIgnore
  def getRefreshToken: Option[String] = jwt.refreshToken
}

case class UserAuthResult(ssid: String, userInfo: UserAuthInfo)

case class UserAuthInfo(userId: String)

case class VerifyRegisterResponse(
                                   userAuth: Option[UserAuthProfile] = None,
                                   token: Option[String] = None,
                                   userProfile: Option[CustomerDTO] = None
                                 ) {
  val verifyContext = {
    if (token.exists(_.nonEmpty) && userProfile.isEmpty) 1
    else if (userAuth.nonEmpty) 2
    else 3
  }

  @JsonIgnore
  def getRefreshToken: Option[String] = userAuth.flatMap(_.jwt.refreshToken)
}