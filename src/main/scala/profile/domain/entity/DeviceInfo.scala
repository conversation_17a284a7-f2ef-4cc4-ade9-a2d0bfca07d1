package profile.domain.entity

import com.fasterxml.jackson.databind.annotation.JsonDeserialize

case class DeviceInfo(
                       deviceId: String,
                       @JsonDeserialize(contentAs = classOf[java.lang.Long])
                       addedTime: Option[Long]
                     )

object PlatForm extends Enumeration {
  type PlatForm = Value

  val IOS = Value(1, "ios")
  val ANDROID = Value(2, "android")
}

