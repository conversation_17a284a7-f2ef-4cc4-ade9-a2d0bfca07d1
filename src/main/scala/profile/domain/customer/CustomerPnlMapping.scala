package profile.domain.customer

import com.fasterxml.jackson.annotation.JsonIgnore
import com.fasterxml.jackson.databind.annotation.JsonDeserialize

/**
 * <AUTHOR> 7/29/24 05:48
 */
case class CustomerPnlMapping(
                               mappings: Map[String, CustomerPnlMappingData],
                               lastCheckAt: Long,
                               var consentStatus: Int = CustomerPnlConsentStatus.WAITING,
                               var consentAt: Long = 0L
                             ) {

  if (consentStatus != CustomerPnlConsentStatus.ACCEPTED && mappings.exists(_._2.consentStatus == CustomerPnlConsentStatus.ACCEPTED)) {
    consentStatus = CustomerPnlConsentStatus.ACCEPTED
  }

  if (consentAt <= 0L) {
    consentAt = mappings.map(_._2.consentAt) match {
      case Nil => consentAt
      case x => x.min
    }
  }

  @JsonIgnore
  def vCopy(): CustomerPnlMapping = {
    this.copy(
      mappings = this.mappings.map { case (k, v) => k -> v.copy() }
    )
  }

}

object CustomerPnlMappingData {

  def apply(pnlCustomer: PnlCustomer, consentedAt: Long, mappedAt: Option[Long]): CustomerPnlMappingData = {
    new CustomerPnlMappingData(
      pnl = pnlCustomer.pnl.get,
      pnlProfileId = pnlCustomer.pnlProfileId,
      pnlUserId = pnlCustomer.pnlUserId,
      phone = pnlCustomer.phone,
      email = pnlCustomer.email.map(_.toLowerCase),
      identityNumberDefault = pnlCustomer.identityNumberDefault
        .orElse(pnlCustomer.listIdentityDocument.getOrElse(Nil).find(_.`type`.contains(CustomerIdentityDocType.CCCD.toString)).flatMap(_.no))
        .orElse(pnlCustomer.listIdentityDocument.getOrElse(Nil).find(_.`type`.contains(CustomerIdentityDocType.CMND.toString)).flatMap(_.no))
        .orElse(pnlCustomer.listIdentityDocument.getOrElse(Nil).find(_.`type`.contains(CustomerIdentityDocType.PASSPORT.toString)).flatMap(_.no))
        .orElse(pnlCustomer.listIdentityDocument.getOrElse(Nil).headOption.flatMap(_.no)),
      cccdNo = pnlCustomer.listIdentityDocument.getOrElse(Nil).find(_.`type`.contains(CustomerIdentityDocType.CCCD.toString)).flatMap(_.no),
      passportNo = pnlCustomer.listIdentityDocument.getOrElse(Nil).find(_.`type`.contains(CustomerIdentityDocType.PASSPORT.toString)).flatMap(_.no),
      mappingAt = mappedAt.filter(_ > 0L).getOrElse(System.currentTimeMillis()),
      consentAt = consentedAt,
      consentStatus = if (consentedAt > 0L) CustomerPnlConsentStatus.ACCEPTED else CustomerPnlConsentStatus.WAITING,
      id = pnlCustomer.id,
      initTierCode = pnlCustomer.initMigrateData.flatMap(_.tierCode),

      numberOfTransaction = pnlCustomer.initMigrateData.flatMap(_.numberOfTransaction),
      totalSpendAmount = pnlCustomer.initMigrateData.flatMap(_.totalSpendAmount),
      totalSpendAmountRent = pnlCustomer.initMigrateData.flatMap(_.totalSpendAmountRent),
      totalSpendAmountPurchase = pnlCustomer.initMigrateData.flatMap(_.totalSpendAmountPurchase),

      migrateCalcAtTime = pnlCustomer.initMigrateData.flatMap(_.migrateCalcAtTime),

      numOfStudyingChild = pnlCustomer.initMigrateData.flatMap(_.numOfStudyingChild),
      numOfStudiedChild = pnlCustomer.initMigrateData.flatMap(_.numOfStudiedChild),


    )
  }
}

object CustomerPnlConsentStatus extends Enumeration {
  val WAITING = 0
  val ACCEPTED = 1
}

case class CustomerPnlMappingData(
                                   pnl: String,
                                   pnlProfileId: Option[String],
                                   pnlUserId: Option[String],
                                   phone: Option[String],
                                   email: Option[String],
                                   identityNumberDefault: Option[String],
                                   cccdNo: Option[String],
                                   passportNo: Option[String],
                                   consentStatus: Int = CustomerPnlConsentStatus.WAITING,
                                   consentAt: Long = 0L,
                                   mappingAt: Long = 0L,
                                   metadata: Option[Map[String, Any]] = None,

                                   @JsonDeserialize(contentAs = classOf[java.lang.Long])
                                   id: Option[Long] = None,
                                   var initTierCode: Option[String] = None,

                                   @JsonDeserialize(contentAs = classOf[java.lang.Integer])
                                   numberOfTransaction: Option[Int],

                                   @JsonDeserialize(contentAs = classOf[java.math.BigDecimal])
                                   totalSpendAmount: Option[java.math.BigDecimal],
                                   @JsonDeserialize(contentAs = classOf[java.math.BigDecimal])
                                   totalSpendAmountRent: Option[java.math.BigDecimal],
                                   @JsonDeserialize(contentAs = classOf[java.math.BigDecimal])
                                   totalSpendAmountPurchase: Option[java.math.BigDecimal],

                                   @JsonDeserialize(contentAs = classOf[java.lang.Long])
                                   migrateCalcAtTime: Option[Long],

                                   @JsonDeserialize(contentAs = classOf[java.lang.Integer])
                                   numOfStudyingChild: Option[Int] = None,

                                   @JsonDeserialize(contentAs = classOf[java.lang.Integer])
                                   numOfStudiedChild: Option[Int] = None,

                                 ) {

  initTierCode = initTierCode.filter(_.nonEmpty).map(_.toUpperCase)

  def makeAccepted(currTime: Long): CustomerPnlMappingData = {
    if (this.consentStatus == CustomerPnlConsentStatus.ACCEPTED) this
    else this.copy(consentStatus = CustomerPnlConsentStatus.ACCEPTED, consentAt = currTime)
  }

  def diff(other: CustomerPnlMappingData): Boolean = {
    this.id != other.id ||
      this.initTierCode != other.initTierCode ||
      this.numberOfTransaction != other.numberOfTransaction ||
      this.totalSpendAmount != other.totalSpendAmount ||
      this.totalSpendAmountRent != other.totalSpendAmountRent ||
      this.totalSpendAmountPurchase != other.totalSpendAmountPurchase ||
      //      this.migrateCalcAtTime != other.migrateCalcAtTime ||
      this.numOfStudyingChild != other.numOfStudyingChild ||
      this.numOfStudiedChild != other.numOfStudiedChild ||
      this.pnlProfileId != other.pnlProfileId ||
      this.pnlUserId != other.pnlUserId ||
      this.identityNumberDefault != other.identityNumberDefault ||
      this.phone != other.phone ||
      this.email != other.email
  }

  def isSameIdentify(otherPhone: Option[String], otherEmail: Option[String], otherCccd: Option[String], otherPassport: Option[String]): Boolean = {
    val cccdNo = this.cccdNo.orElse(this.identityNumberDefault)
    val passportNo = this.passportNo.orElse(this.identityNumberDefault)

    val equalPhone = this.phone.exists(_.nonEmpty) && this.phone.getOrElse("") == otherPhone.getOrElse("")
    val equalEmail = this.email.exists(_.nonEmpty) && this.email.getOrElse("") == otherEmail.getOrElse("")
    val equalCccd = cccdNo.exists(_.nonEmpty) && cccdNo.getOrElse("") == otherCccd.getOrElse("")
    val equalPassport = passportNo.exists(_.nonEmpty) && passportNo.getOrElse("") == otherPassport.getOrElse("")

    (!otherCccd.exists(_.nonEmpty) || !cccdNo.exists(_.nonEmpty) || equalCccd) &&
      (!otherPassport.exists(_.nonEmpty) || !passportNo.exists(_.nonEmpty) || equalPassport) &&
      (!otherPhone.exists(_.nonEmpty) || !this.phone.exists(_.nonEmpty) || equalPhone) &&
      (!otherEmail.exists(_.nonEmpty) || !this.email.exists(_.nonEmpty) || equalEmail)
  }

}