package profile.domain.customer

import com.fasterxml.jackson.annotation.JsonIgnore
import com.fasterxml.jackson.databind.annotation.JsonDeserialize

case class CustomerCdpProfile(
                               createdTime: Long,
                               updatedTime: Long,

                               @JsonDeserialize(contentAs = classOf[java.lang.Long])
                               firstAppInteractionTime: Option[Long],

                               @JsonDeserialize(contentAs = classOf[java.lang.Long])
                               lastAppInteractionTime: Option[Long],

                               @JsonDeserialize(contentAs = classOf[java.lang.Long])
                               pnlSpendingAmount: Map[String, Long],

                               livingAddress: CustomerLivingAddress,

                               scores: Map[String, CustomerScore]
                             ) {

  @JsonIgnore
  def livingAddressDisplayNameMetadata: Map[String, Any] = {
    if (livingAddress == null) {
      Map.empty
    } else {
      var displayNames = Map.empty[String, Any]
      if (livingAddress.region.exists(_.nonEmpty)) displayNames += "living_address_region_name" -> livingAddress.region
      if (livingAddress.province.exists(_.nonEmpty)) displayNames += "living_address_province_name" -> livingAddress.province
      displayNames
    }
  }

}

case class CustomerLivingAddress(
                                  continent: Option[String],
                                  continentCode: Option[String],

                                  country: Option[String],
                                  countryCode: Option[String],

                                  @JsonDeserialize(contentAs = classOf[java.lang.Integer])
                                  regionId: Option[Int],
                                  region: Option[String],


                                  @JsonDeserialize(contentAs = classOf[java.lang.Integer])
                                  provinceId: Option[Int],
                                  province: Option[String],

                                  @JsonDeserialize(contentAs = classOf[java.lang.Integer])
                                  cityId: Option[Int],
                                  city: Option[String]
                                ) {

}

case class CustomerScore(
                          score: Option[String] = None,
                          rank: Option[String] = None
                        ) {

}
