package profile.domain.customer

import profile.domain.BytesFile
import profile.domain.customer.CustomerIdentityDocType.CustomerIdentityDocType
import profile.domain.customer.EKYCDocumentType.EKYCDocumentType
import profile.domain.customer.EKYCVerifyStep.EKYCVerifyStep
import profile.service.CustomerIdentityDocumentInfo
import vn.vhm.common.exception.NotFoundException

object EKYCVerifyStep extends Enumeration {
  type EKYCVerifyStep = Value

  val SCAN_FRONT_DOCUMENT = Value("scan_front_document")
  val SCAN_BACK_DOCUMENT = Value("scan_back_document")
  val SCAN_NFC = Value("scan_nfc")
  val SCAN_FACE = Value("scan_face")

  def opt(value: String): Option[EKYCVerifyStep] = values.find(_.toString == value)

  def get(value: String): EKYCVerifyStep = opt(value).getOrElse(throw NotFoundException(s"Invalid verify step = $value"))
}

object EKYCDocumentType extends Enumeration {
  type EKYCDocumentType = Value

  val ID_CARD = Value("id_card") // CCCD 2024
  val CHIP_BASED_ID_CARD = Value("chip_based_id_card") // CCCD gan chip
  val WHITE_ID_CARD = Value("white_id_card") // CCCD khong gan chip
  val PASSPORT = Value("passport") // Passport
  val LEGACY_ID = Value("legacy_id") // CMT

  def opt(value: String): Option[EKYCDocumentType] = values.find(_.toString == value)

  def get(value: String): EKYCDocumentType = opt(value).getOrElse(throw NotFoundException(s"Invalid document type = $value"))
}

case class EKYCDocumentVerification(
                                     // document info
                                     provider: String,
                                     documentType: EKYCDocumentType,
                                     identityType: CustomerIdentityDocType,
                                     identityDoc: CustomerIdentityDocument,
                                     identityInfo: CustomerIdentityDocumentInfo,
                                     faceMatchingScore: CustomerIdentityVerificationScores,
                                     chipData: Option[CustomerIdentityChipData] = None,

                                     // scan images
                                     identityFiles: Option[Seq[BytesFile]],
                                     identityRawFiles: Option[Seq[BytesFile]] = None,
                                     faceImageFiles: Option[Seq[BytesFile]],
                                     chipImageFiles: Option[Seq[BytesFile]] = None,

                                     // metadata
                                     var nextStep: Option[EKYCVerifyStep] = None,
                                     var metadata: Option[Map[String, Any]] = None
                                   ) {

}
