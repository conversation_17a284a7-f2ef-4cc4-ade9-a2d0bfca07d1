package profile.domain.customer

import com.fasterxml.jackson.annotation.JsonIgnore
import com.fasterxml.jackson.databind.annotation.JsonDeserialize

/**
 * <AUTHOR> 7/29/24 05:50
 */

case class CustomerPnlRankingProgressDataMetric(
                                                 metric: String,
                                                 valueType: Option[String],
                                                 @JsonDeserialize(contentAs = classOf[java.lang.Long])
                                                 valueL: Option[Long] = None,
                                                 @JsonDeserialize(contentAs = classOf[java.lang.Double])
                                                 valueD: Option[Double] = None,
                                                 @JsonDeserialize(contentAs = classOf[java.lang.Integer])
                                                 valueI: Option[Int] = None,
                                                 valueS: Option[String] = None,
                                               ) {
  def hasChange(metric: CustomerPnlRankingProgressDataMetric): Boolean = {
    this.metric != metric.metric ||
      this.valueType != metric.valueType ||
      this.valueL != metric.valueL ||
      this.valueD != metric.valueD ||
      (this.valueD.isDefined && metric.valueD.isDefined && java.lang.Double.compare(this.valueD.get, metric.valueD.get) != 0) ||
      this.valueI != metric.valueI ||
      this.valueS != metric.valueS
  }
}

case class CustomerPnlRankingProgressData(
                                           updatedTime: Long,
                                           metrics: Map[String, CustomerPnlRankingProgressDataMetric]
                                         ) {

  @JsonIgnore
  var tierCode: Option[String] = None

  def hasChange(progressData: CustomerPnlRankingProgressData): Boolean = {
    progressData.metrics.size != this.metrics.size ||
      progressData.metrics.exists { case (k, v) =>
        !this.metrics.contains(k) || this.metrics(k).hasChange(v)
      } ||
      this.metrics.exists { case (k, v) =>
        !progressData.metrics.contains(k) || progressData.metrics(k).hasChange(v)
      }
  }
}

case class CustomerPnlRankingProgress(
                                       tierCode: Option[String],
                                       pnlProgress: Map[String, CustomerPnlRankingProgressData],
                                       updatedTime: Long,
                                       @JsonDeserialize(contentAs = classOf[java.lang.Long])
                                       dataTime: Option[Long] = None,
                                     ) {

  def hasChange(progress: CustomerPnlRankingProgress): Boolean = {
    progress.tierCode != this.tierCode ||
      progress.pnlProgress.size != this.pnlProgress.size ||
      progress.pnlProgress.exists { case (k, v) =>
        !this.pnlProgress.contains(k) || this.pnlProgress(k).hasChange(v)
      } ||
      this.pnlProgress.exists { case (k, v) =>
        !progress.pnlProgress.contains(k) || progress.pnlProgress(k).hasChange(v)
      }
  }

}
