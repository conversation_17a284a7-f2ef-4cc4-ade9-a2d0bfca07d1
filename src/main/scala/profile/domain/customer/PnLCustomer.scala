package profile.domain.customer

import com.fasterxml.jackson.annotation.JsonIgnore
import com.fasterxml.jackson.databind.annotation.JsonDeserialize
import profile.domain.customer.CustomerIdentityDocType.CustomerIdentityDocType
import profile.util.{Constant, CustomUtils, VHMConfig}
import vn.vhm.common.domain.Implicits.ImplicitAny
import vn.vhm.common.domain.profiling.Profiler
import vn.vhm.common.util.{<PERSON><PERSON><PERSON><PERSON><PERSON>, Utils}
import vn.vhm.jdbc.JdbcRecord
import vn.vhm.jdbc.postgres.PostgreSqlDAO

import javax.sql.DataSource

/**
 * <AUTHOR> 7/12/24 12:25
 */
object PnlCustomer {

  val TBL_NAME = "pnl_customers"

  def field(f: String): String = f

  val ID = field("id")
  val PNL = field("pnl")
  val PNL_PROFILE_ID = field("pnl_profile_id")
  val PNL_USER_ID = field("pnl_user_id")

  val PHONE = field("phone")
  val PHONE_VERIFIED = field("phone_verified_status")
  val EMAIL = field("email")
  val EMAIL_VERIFIED = field("email_verified_status")

  val IDENTITY_NUMBER_DEFAULT = field("identity_number_default")

  val LIST_IDENTITY_DOCUMENT = field("list_identity_document")

  val INIT_MIGRATE_DATA = field("init_migrate_data")

  val STATUS = field("status")
  val ACTIVE = field("active")
  val VCLUB_EVENT_ID = field("vclub_event_id")
  val VCLUB_USER_ID = field("vclub_user_id")

  val UPDATED_ON = field("updated_on")

  val primaryKeys = Seq(ID)

  val fields = Seq(
    ID, PNL,
    PNL_PROFILE_ID, PNL_USER_ID,
    PHONE, PHONE_VERIFIED,
    EMAIL, EMAIL_VERIFIED,
    LIST_IDENTITY_DOCUMENT,
    IDENTITY_NUMBER_DEFAULT,
    STATUS,
    VCLUB_EVENT_ID,
    VCLUB_USER_ID,
    INIT_MIGRATE_DATA,
    UPDATED_ON
  )

}

import profile.domain.customer.PnlCustomer._

object PnlCustomerStatus extends Enumeration {

  type PnlCustomerStatus = Value

  val ACTIVE = Value("ACTIVE")
  val BANNED = Value("BANNED")
  val DISABLED = Value("DISABLED")
  val DELETED = Value("DELETED")

}

case class PnlCustomerIdentityDocs(
                                    `type`: Option[String],
                                    no: Option[String],
                                    verifiedStatus: Option[String],
                                  )

case class PnlCustomerInitMigrateData(
                                       tierCode: Option[String],

                                       @JsonDeserialize(contentAs = classOf[java.lang.Integer])
                                       numberOfTransaction: Option[Int],

                                       @JsonDeserialize(contentAs = classOf[java.math.BigDecimal])
                                       totalSpendAmount: Option[java.math.BigDecimal],
                                       @JsonDeserialize(contentAs = classOf[java.math.BigDecimal])
                                       totalSpendAmountRent: Option[java.math.BigDecimal] = None,
                                       @JsonDeserialize(contentAs = classOf[java.math.BigDecimal])
                                       totalSpendAmountPurchase: Option[java.math.BigDecimal] = None,

                                       @JsonDeserialize(contentAs = classOf[java.lang.Long])
                                       migrateCalcAtTime: Option[Long],

                                       @JsonDeserialize(contentAs = classOf[java.lang.Integer])
                                       numOfStudyingChild: Option[Int] = None,
                                       @JsonDeserialize(contentAs = classOf[java.lang.Integer])
                                       numOfStudiedChild: Option[Int] = None,

                                     )

case class PnlCustomer(
                        var id: Option[Long] = None,
                        var pnl: Option[String] = None,
                        var pnlProfileId: Option[String] = None,
                        var pnlUserId: Option[String] = None,
                        var phone: Option[String] = None,
                        var phoneVerified: Option[Int] = None,
                        var email: Option[String] = None,
                        var emailVerified: Option[Int] = None,
                        var identityNumberDefault: Option[String] = None,

                        var status: Option[String] = None,
                        var active: Option[Boolean] = None,

                        var listIdentityDocument: Option[Seq[PnlCustomerIdentityDocs]] = None,

                        var initMigrateData: Option[PnlCustomerInitMigrateData] = None,

                        var updatedTime: Option[Long] = None,
                        var vclubUserId: Option[Long] = None,

                      ) extends JdbcRecord {

  override def getPrimaryKeys(): Seq[String] = primaryKeys

  override def getFields(): Seq[String] = fields

  override def isJsonField(field: String): Boolean = {
    field == LIST_IDENTITY_DOCUMENT || field == INIT_MIGRATE_DATA
  }

  override def setValues(field: String, value: Any): Unit = field match {
    case ID => id = value.asOpt
    case PNL => pnl = value.asOpt
    case PNL_PROFILE_ID => pnlProfileId = value.asOpt
    case PNL_USER_ID => pnlUserId = value.asOpt

    case PHONE => phone = value.asOpt
    case PHONE_VERIFIED => phoneVerified = value.asOptInt
    case EMAIL => email = value.asOpt
    case EMAIL_VERIFIED => emailVerified = value.asOptInt
    case STATUS => status = value.asOpt
    case ACTIVE => active = value.asOptBoolean
    case LIST_IDENTITY_DOCUMENT => listIdentityDocument = value.asOptString.map(JsonHelper.fromJson[Seq[PnlCustomerIdentityDocs]](_))
    case IDENTITY_NUMBER_DEFAULT => identityNumberDefault = value.asOpt

    case INIT_MIGRATE_DATA => initMigrateData = value.asOptString.map(JsonHelper.fromJson[PnlCustomerInitMigrateData](_))

    case UPDATED_ON => updatedTime = value.asOpt[java.sql.Timestamp].map(CustomUtils.timestamptzToLong)
    case VCLUB_USER_ID => vclubUserId = value.asOptLong

    case _ => //throw SqlFieldMissing(field, value)
  }

  override def getValue(field: String): Option[Any] = field match {
    case ID => id
    case PNL => pnl
    case PNL_PROFILE_ID => pnlProfileId
    case PNL_USER_ID => pnlUserId
    case PHONE => phone
    case PHONE_VERIFIED => phoneVerified
    case EMAIL => email
    case EMAIL_VERIFIED => emailVerified
    case STATUS => status
    case ACTIVE => active
    case LIST_IDENTITY_DOCUMENT => listIdentityDocument.map(JsonHelper.toJson(_))
    case IDENTITY_NUMBER_DEFAULT => identityNumberDefault
    case INIT_MIGRATE_DATA => initMigrateData.map(JsonHelper.toJson(_))
    case UPDATED_ON => updatedTime.map(t => CustomUtils.longToTimestamptz(t))
    case VCLUB_USER_ID => vclubUserId
    case _ => None //throw SqlFieldMissing(field)
  }

  @JsonIgnore
  def listIdentityNo: Seq[Option[String]] = {
    listIdentityDocument.map(_.map(_.no)).getOrElse(Nil)
  }
}

case class PnlCustomerDAO(ds: DataSource) extends PostgreSqlDAO[PnlCustomer] {

  override def createRecord(): PnlCustomer = PnlCustomer()

  override val table: String = PnlCustomer.TBL_NAME

  override val sqlLogger = VHMConfig.sqlLogger.getOrElse(super.sqlLogger)

  private val fieldsString = fields.mkString(",")

  def searchLatestActive(userId: String, listPnl: Seq[String],
                         reqPhone: Option[String], reqEmail: Option[String],
                         //                         reqIdentityNumber: Option[String]
                         reqCCCD: Option[String], reqPassport: Option[String]
                        ): Seq[PnlCustomer] = Profiler(s"${getClass.getCanonicalName}.searchLatestActive") {

    val conditionsValue = scala.collection.mutable.ListBuffer.empty[Any]

    val listSQL = listPnl
      //      .filter(_ => reqPhone.nonEmpty || reqEmail.nonEmpty || reqIdentityNumber.nonEmpty)
//      .filter(_ => reqPhone.nonEmpty || reqEmail.nonEmpty)
      .filter(pnl =>
        Constant.listPnlMappingByIdentityDoc.contains(pnl) && (reqCCCD.nonEmpty || reqPassport.nonEmpty) ||
        !Constant.listPnlMappingByIdentityDoc.contains(pnl) && (reqPhone.nonEmpty || reqEmail.nonEmpty)
      )
      .map(pnl => {

        val subConditions = scala.collection.mutable.ListBuffer.empty[String]
        val subConditionsValue = scala.collection.mutable.ListBuffer.empty[Any]

        val subSortConditions = scala.collection.mutable.ListBuffer.empty[String]
        val subSortConditionsValue = scala.collection.mutable.ListBuffer.empty[Any]

        //        subConditions += s"$PNL = '$pnl'"
        subConditions += s"$PNL = ?"
        subConditionsValue += pnl

        subConditions += s"$ACTIVE = true"

        //        subConditions += s"$STATUS = '${PnlCustomerStatus.ACTIVE.toString}'"
        subConditions += s"$STATUS = ?"
        subConditionsValue += PnlCustomerStatus.ACTIVE.toString


        //        subConditions += s"($VCLUB_USER_ID IS NULL OR $VCLUB_USER_ID = 0 OR $VCLUB_USER_ID = $userId)"
        subConditions += s"($VCLUB_USER_ID IS NULL OR $VCLUB_USER_ID = 0 OR $VCLUB_USER_ID = ?)"
        subConditionsValue += userId.toLong

        val orConditions = scala.collection.mutable.ListBuffer.empty[String]
        val orConditionsValue = scala.collection.mutable.ListBuffer.empty[String]

        //        reqIdentityNumber.foreach(v => {
        //          //          orConditions += s"$IDENTITY_NUMBER_DEFAULT = '$v'"
        //          orConditions += s"$IDENTITY_NUMBER_DEFAULT = ?"
        //          orConditionsValue += v
        //
        //          //          subSortConditions += s"CASE $IDENTITY_NUMBER_DEFAULT WHEN '$v' THEN 1 ELSE 0 END DESC"
        //          subSortConditions += s"CASE $IDENTITY_NUMBER_DEFAULT WHEN ? THEN 1 ELSE 0 END DESC"
        //          subSortConditionsValue += v
        //        })

        if (Constant.listPnlMappingByIdentityDoc.contains(pnl)) { // mapping by identity doc
          (reqCCCD, reqPassport) match {
            case (Some(cccd), Some(passport)) =>
              orConditions += s"( $LIST_IDENTITY_DOCUMENT @> ?::jsonb OR $LIST_IDENTITY_DOCUMENT @> ?::jsonb )"
              orConditionsValue += toIdentityJson(cccd) += toIdentityJson(passport)

              subSortConditions += s"CASE $LIST_IDENTITY_DOCUMENT @> ?::jsonb WHEN true THEN 1 ELSE 0 END DESC"
              subSortConditionsValue += toIdentityJson(cccd)

              subSortConditions += s"CASE $LIST_IDENTITY_DOCUMENT @> ?::jsonb WHEN true THEN 1 ELSE 0 END DESC"
              subSortConditionsValue += toIdentityJson(passport)

            case (Some(cccd), _) =>
              orConditions += s"$LIST_IDENTITY_DOCUMENT @> ?::jsonb"
              orConditionsValue += toIdentityJson(cccd)

              subSortConditions += s"CASE $LIST_IDENTITY_DOCUMENT @> ?::jsonb WHEN true THEN 1 ELSE 0 END DESC"
              subSortConditionsValue += toIdentityJson(cccd)

            case (_, Some(passport)) =>
              orConditions += s"$LIST_IDENTITY_DOCUMENT @> ?::jsonb"
              orConditionsValue += toIdentityJson(passport)

              subSortConditions += s"CASE $LIST_IDENTITY_DOCUMENT @> ?::jsonb WHEN true THEN 1 ELSE 0 END DESC"
              subSortConditionsValue += toIdentityJson(passport)

            case _ =>
          }
        } else { // other PNL mapping by phone and email
          (reqPhone, reqEmail.map(_.toLowerCase)) match {
            case (Some(p), Some(e)) =>
              //            orConditions += s"( ($PHONE = ? AND LOWER($EMAIL) = ?) OR ($PHONE = ? AND ($EMAIL IS NULL OR $EMAIL = '') ) OR ( ($PHONE IS NULL OR $PHONE = '') AND LOWER($EMAIL) = ?) )"
              //            orConditionsValue += p += e += p += e

              orConditions += s"( $PHONE = ? OR LOWER($EMAIL) = ? ) "
              orConditionsValue += p += e

              subSortConditions += s"CASE $PHONE WHEN ? THEN 1 ELSE 0 END DESC"
              subSortConditionsValue += p

              subSortConditions += s"CASE LOWER($EMAIL) WHEN ? THEN 1 ELSE 0 END DESC"
              subSortConditionsValue += e

            case (Some(p), _) =>
              orConditions += s"$PHONE = ?"
              orConditionsValue += p

              subSortConditions += s"CASE $PHONE WHEN ? THEN 1 ELSE 0 END DESC"
              subSortConditionsValue += p

            case (_, Some(e)) =>
              orConditions += s"LOWER($EMAIL) = ?"
              orConditionsValue += e

              subSortConditions += s"CASE LOWER($EMAIL) WHEN ? THEN 1 ELSE 0 END DESC"
              subSortConditionsValue += e

            case _ =>
          }
        }


        subConditions += orConditions.mkString("(", " OR ", ")")
        subConditionsValue ++= orConditionsValue

        subSortConditions += s"$VCLUB_EVENT_ID DESC" += s"$ID DESC"

        conditionsValue ++= subConditionsValue ++= subSortConditionsValue

        s"""
           |( SELECT $fieldsString
           |FROM $table
           |WHERE ${subConditions.mkString(" AND ")}
           |ORDER BY ${subSortConditions.mkString(", ")}
           |LIMIT 5 )""".stripMargin

      })

    if (listSQL.isEmpty) Nil
    else {
      execute(executeQuery(listSQL.mkString(" UNION ALL "), conditionsValue)(rs => {
        val holder = scala.collection.mutable.ListBuffer.empty[PnlCustomer]
        while (rs.next()) {
          val result = parseResult(rs)
          holder += result
        }
        val mapPnl = holder.groupBy(_.pnl.get)
        listPnl.flatMap(pnl => {
          if (Constant.listPnlMappingByIdentityDoc.contains(pnl)) {
            mapPnl.getOrElse(pnl, Nil) match {
              case x if x.isEmpty || x.size == 1 => x.headOption
              case x =>
                x.find(item => item.listIdentityNo.contains(reqCCCD) && item.listIdentityDocument.contains(reqPassport))
                  .orElse(x.find(item => item.listIdentityDocument.contains(reqCCCD) && !item.listIdentityDocument.contains(reqPassport)))
                  .orElse(x.find(item => !item.listIdentityDocument.contains(reqCCCD) && item.listIdentityDocument.contains(reqPassport)))
                  .orElse(x.find(item => item.listIdentityDocument.contains(reqCCCD)))
                  .orElse(x.headOption)
            }
          } else {
            mapPnl.getOrElse(pnl, Nil) match {
              case x if x.isEmpty || x.size == 1 => x.headOption
              case x =>
                x.find(item => item.phone.exists(_.nonEmpty) && item.phone == reqPhone && item.email.exists(_.nonEmpty) && item.email == reqEmail)
                  .orElse(x.find(item => item.phone.exists(_.nonEmpty) && item.phone == reqPhone && !item.email.exists(_.nonEmpty)))
                  .orElse(x.find(item => !item.phone.exists(_.nonEmpty) && item.email.exists(_.nonEmpty) && item.email == reqEmail))
                  .orElse(x.find(item => item.phone.exists(_.nonEmpty) && item.phone == reqPhone))
                  .orElse(x.headOption)
            }
          }
        })
      })(_))
    }

  }

  def multiSelect(ids: Seq[Long]): Seq[PnlCustomer] = {
    val query =
      s"""
         |SELECT $fieldsString
         |FROM $table
         |WHERE $ID IN (${ids.mkString(",")})
         |""".stripMargin
    execute(executeQuery(query)(rs => {
      val holder = scala.collection.mutable.ListBuffer.empty[PnlCustomer]
      while (rs.next()) {
        holder += parseResult(rs)
      }
      holder
    })(_))
  }
  
  private def toIdentityJson(identityNo: String): String = {
    s"""[{"no":"${Utils.asciifolding(identityNo)}"}]"""
  }

}
