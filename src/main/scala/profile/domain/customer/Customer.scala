package profile.domain.customer

import com.fasterxml.jackson.annotation.{<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, JsonAnySetter, JsonIgnore}
import com.fasterxml.jackson.databind.annotation.JsonDeserialize
import com.twitter.finatra.validation.{MethodValidation, ValidationResult}
import org.postgresql.util.PGobject
import profile.domain.UserStatus
import profile.domain.customer.CustomerIdentityDocType.CustomerIdentityDocType
import profile.domain.entity.{DeviceInfo, UserAuthInfo}
import profile.domain.response.{CustomerDTO, InternalCustomerDTO, RegisterByAdminCustomerResponse}
import profile.service.CustomerIdentityDocumentInfo
import profile.util.{CTimeUtils, Constant, CustomUtils, EmailHelper, VHMConfig}
import vn.vhm.common.domain.Implicits._
import vn.vhm.common.domain.OptionImplicits.OptionImplicitAny
import vn.vhm.common.domain.profiling.Profiler
import vn.vhm.common.exception.NotFoundException
import vn.vhm.common.util.{<PERSON><PERSON><PERSON><PERSON><PERSON>, Utils}
import vn.vhm.jdbc.postgres.PostgreSqlDAO
import vn.vhm.jdbc.{JdbcRecord, SqlFieldMissing}

import java.sql.Date
import java.util.{Map => JMap}
import javax.sql.DataSource

object Customer {

  val TBL_NAME = "customers"

  def field(f: String): String = f

  val USER_ID = field("id")
  val PASSWORD = field("password")
  val ACTIVE = field("active")
  val STATUS = field("status")
  val CREATED_ON = field("created_on")
  val UPDATED_ON = field("updated_on")
  val DELETED_AT = field("deleted_at")
  val CREATED_BY = field("created_by")
  val UPDATED_BY = field("updated_by")

  val PHONE = field("phone")
  val PHONE_VERIFIED = field("phone_verified")
  val EMAIL = field("email")
  val EMAIL_VERIFIED = field("email_verified")
  val NORMALIZED_EMAIL = field("normalized_email")

  val OAUTH_GOOGLE_ID = field("oauth_google_id")
  val OAUTH_FB_ID = field("oauth_facebook_id")
  val OAUTH_APPLE_ID = field("oauth_apple_id")
  val OAUTH_AZURE_ID = field("oauth_azure_id")
  val AUTH_FIREBASE_ID = field("auth_firebase_id")
  val DEVICES = field("devices")

  val FULL_NAME = field("full_name")
  val FIRST_NAME = field("first_name")
  val LAST_NAME = field("last_name")
  val AVATAR = field("avatar")
  val GENDER = field("gender")
  val BIRTHDAY = field("birthday")

  val LIST_IDENTITY_DOCUMENT = field("list_identity_document")
  val PNL_MAPPING = field("pnl_mapping")
  val PNL_RANKING_PROGRESS = field("pnl_ranking_progress")
  val TIER_IDS = field("tier_ids")
  val SEGMENTS = field("segments")
  val DISPLAY_SETTING = field("display_setting")
  val REFERRAL_CODE = field("referral_code")

  val METADATA = field("metadata")

  val DEMOGRAPHIC_METADATA = field("demographic_metadata")

  val DEMOGRAPHIC_METADATA_EXTRA = field("demographic_metadata_extra")

  val primaryKeys = Seq(USER_ID)

  val fields = Seq(
    USER_ID, PASSWORD, ACTIVE, STATUS,
    CREATED_ON, UPDATED_ON, DELETED_AT, CREATED_BY, UPDATED_BY,
    PHONE, PHONE_VERIFIED,
    EMAIL, EMAIL_VERIFIED, NORMALIZED_EMAIL,
    OAUTH_GOOGLE_ID, OAUTH_FB_ID, OAUTH_APPLE_ID, OAUTH_AZURE_ID, AUTH_FIREBASE_ID, DEVICES,
    FULL_NAME, FIRST_NAME, LAST_NAME, AVATAR,
    GENDER, BIRTHDAY,
    LIST_IDENTITY_DOCUMENT,
    PNL_MAPPING,
    PNL_RANKING_PROGRESS,
    TIER_IDS,
    SEGMENTS,
    DISPLAY_SETTING,
    REFERRAL_CODE,
    METADATA,
    DEMOGRAPHIC_METADATA,
    DEMOGRAPHIC_METADATA_EXTRA
  )

  def apply(email: String, emailVerified: Int): Customer = {
    Customer(email = email.toSome, emailVerified = emailVerified.toSome)
  }

  val USER_STATUS_ACTIVE_VAL = "ACTIVE"

}

object CustomerIdentityDocType extends Enumeration {
  type CustomerIdentityDocType = Value

  val CCCD = Value("CCCD")
  val PASSPORT = Value("PASSPORT")

  // legacy
  val CMND = Value("CMND")

  def findPriority(documents: Seq[CustomerIdentityDocument]): Option[CustomerIdentityDocument] = {
    documents.find(_.`type`.contains(CCCD.toString))
      .orElse(documents.find(_.`type`.contains(PASSPORT.toString)))
  }

  def opt(doc: String): Option[CustomerIdentityDocType] = values.find(_.toString == doc)

  def get(doc: String): CustomerIdentityDocType = opt(doc).getOrElse(throw NotFoundException(s"Invalid doc_type = $doc"))

}

object CustomerIdentityVerifyStatus extends Enumeration {
  type CustomerIdentityVerifyStatus = Value

  val SUBMITTED = Value("SUBMITTED")
  val REJECTED = Value("REJECTED")
  val VERIFIED = Value("VERIFIED")

  // enum for flow process
  val CANCELLED = Value("CANCELLED")

  def isRejected(value: CustomerIdentityVerifyStatus): Boolean = {
    value == REJECTED || value == CANCELLED
  }

  def opt(value: String): Option[CustomerIdentityVerifyStatus] = values.find(_.toString == value)

  def get(value: String): CustomerIdentityVerifyStatus = opt(value).getOrElse(throw NotFoundException(s"Invalid verify status = $value"))

}

case class CustomerIdentityDocument(
                                     `type`: Option[String], // CustomerIdentityDocType
                                     no: Option[String],
                                     verifiedStatus: Option[String], // CustomerIdentityVerifyStatus
                                     verifiedStatusMessage: Option[String],
                                     @JsonDeserialize(contentAs = classOf[java.lang.Long])
                                     identityDocumentVerifyId: Option[Long],

                                     // enhance data
                                     var idInfo: Option[CustomerIdentityDocumentInfo] = None,
                                     var idScores: Option[CustomerIdentityVerificationScores] = None,
                                     var nextStage: Option[String] = None,
                                     var stageMetadata: Option[String] = None,
                                     var verifiedStatusMessageDisplay: Option[String] = None,
                                     var verifiedStatusMessageNote: Option[String] = None,
                                   ) {

  @JsonIgnore
  def asSimple: CustomerIdentityDocument = {
    CustomerIdentityDocument(
      `type` = this.`type`,
      verifiedStatus = this.verifiedStatus,
      verifiedStatusMessage = this.verifiedStatusMessage,
      identityDocumentVerifyId = this.identityDocumentVerifyId,
      no = None,
      verifiedStatusMessageDisplay = verifiedStatusMessageDisplay,
      verifiedStatusMessageNote = verifiedStatusMessageNote
    )
  }
}

case class DemographicMetadata(
                                var birthday: Option[String] = None,

                                @JsonDeserialize(contentAs = classOf[java.lang.Integer])
                                var monthOfBirth: Option[Int] = None,

                                @JsonDeserialize(contentAs = classOf[java.lang.Integer])
                                var yearOfBirth: Option[Int] = None,

                                var fullName: Option[String] = None,

                                var gender: Option[String] = None,
                                var permanentAddress: Option[String] = None,
                                var permanentAddressProvinceCode: Option[String] = None,
                                var permanentAddressRegionCode: Option[String] = None,
                                var temporaryAddress: Option[String] = None,
                                var temporaryAddressProvinceCode: Option[String] = None,
                                var temporaryAddressRegionCode: Option[String] = None,
                                var livingAddress: Option[String] = None,
                                var livingAddressProvinceCode: Option[String] = None,
                                var livingAddressRegionCode: Option[String] = None,
                                var hometown: Option[String] = None,
                                var hometownProvinceCode: Option[String] = None,
                                var hometownRegionCode: Option[String] = None,
                                var nationality: Option[String] = None,

                                @JsonDeserialize(contentAs = classOf[java.lang.Long])
                                var firstAppInteractionTime: Option[Long] = None,

                                @JsonDeserialize(contentAs = classOf[java.lang.Long])
                                var lastAppInteractionTime: Option[Long] = None,

                                var firstAppInteractionDate: Option[String] = None,
                                var lastAppInteractionDate: Option[String] = None,

                                @JsonDeserialize(contentAs = classOf[java.lang.Long])
                                var firstLoginTime: Option[Long] = None,
                                var firstLoginDate: Option[String] = None,
                                @JsonDeserialize(contentAs = classOf[java.lang.Long])
                                var lastLoginTime: Option[Long] = None,
                                var lastLoginDate: Option[String] = None,

                                // disabled setter fields
                                private var _pnlTiers: Map[String, String] = Map.empty,
                                private var _pnlSpendingAmount: Map[String, Long] = Map.empty,
                                private var _customerScores: Map[String, String] = Map.empty,
                                private var _customerScoreRanks: Map[String, String] = Map.empty,
                                private var _undefinedFields: Map[String, Any] = Map.empty
                              ) {
  @JsonIgnore
  def isAllIdentityDataEmpty: Boolean = {
    birthday.isEmpty && gender.isEmpty && permanentAddress.isEmpty && hometown.isEmpty && nationality.isEmpty
  }

  def pnlTiers: Map[String, String] = _pnlTiers

  private def pnlTiers_ = (newPnlTiers: Map[String, String]) => {
    _pnlTiers = newPnlTiers // Setter with restricted access
  }

  def pnlSpendingAmount: Map[String, Long] = _pnlSpendingAmount

  private def pnlSpendingAmount_ = (newPnlSpendingAmount: Map[String, Long]) => {
    _pnlSpendingAmount = newPnlSpendingAmount // Setter with restricted access
  }

  def customerScores: Map[String, String] = _customerScores

  private def customerScores_ = (newCustomerScores: Map[String, String]) => {
    _customerScores = newCustomerScores // Setter with restricted access
  }

  def customerScoreRanks: Map[String, String] = _customerScoreRanks

  private def customerScoreRanks_ = (newCustomerScoreRanks: Map[String, String]) => {
    _customerScoreRanks = newCustomerScoreRanks // Setter with restricted access
  }

  def undefinedFields: Map[String, Any] = _undefinedFields

  private def undefinedFields_ = (newUndefinedFields: Map[String, Any]) => {
    _undefinedFields = newUndefinedFields // Setter with restricted access
  }


  import scala.jdk.CollectionConverters._

  @JsonAnySetter
  def setUndefinedFields(name: String, value: Any): Unit = {
    if (name.endsWith(Constant.DEMOGRAPHIC_METADATA_PNL_TIER_POSTFIX)) {
      _pnlTiers += (name -> value.toString)
    } else if (name.endsWith(Constant.DEMOGRAPHIC_METADATA_PNL_SPENDING_AMOUNT_POSTFIX)) {
      _pnlSpendingAmount += (name -> value.toString.toLong)
    } else if (name.endsWith(Constant.DEMOGRAPHIC_METADATA_CUSTOMER_SCORE_POSTFIX)) {
      _customerScores += (name -> value.toString)
    } else if (name.endsWith(Constant.DEMOGRAPHIC_METADATA_CUSTOMER_SCORE_RANK_POSTFIX)) {
      _customerScoreRanks += (name -> value.toString)
    } else {
      _undefinedFields += (name -> value)
    }
  }

  @JsonAnyGetter
  def getUndefinedFields: JMap[String, Any] = {
    (_pnlSpendingAmount ++ _pnlTiers ++ _customerScores ++ _customerScoreRanks ++ _undefinedFields).asJava
  }

  def setAllPnlTiers(newPnlTiers: Map[String, String]): DemographicMetadata = {
    _pnlTiers = newPnlTiers.map { case (key, value) => s"${key.toLowerCase}${Constant.DEMOGRAPHIC_METADATA_PNL_TIER_POSTFIX}" -> value }
    this
  }

  def setAllPnlSpendingAmount(newPnlSpendingAmount: Map[String, Long]): DemographicMetadata = {
    _pnlSpendingAmount = newPnlSpendingAmount.map { case (key, value) => s"${key.toLowerCase}${Constant.DEMOGRAPHIC_METADATA_PNL_SPENDING_AMOUNT_POSTFIX}" -> value }
    this
  }

  def setAllCustomerScores(newCustomerScores: Map[String, String]): DemographicMetadata = {
    _customerScores = newCustomerScores.map { case (key, value) => s"${key.toLowerCase}${Constant.DEMOGRAPHIC_METADATA_CUSTOMER_SCORE_POSTFIX}" -> value }
    this
  }

  def setAllCustomerScoreRanks(newCustomerScoreRanks: Map[String, String]): DemographicMetadata = {
    _customerScoreRanks = newCustomerScoreRanks.map { case (key, value) => s"${key.toLowerCase}${Constant.DEMOGRAPHIC_METADATA_CUSTOMER_SCORE_RANK_POSTFIX}" -> value }
    this
  }

  @JsonIgnore
  def getAllPnlTiers: Map[String, String] = {
    this.pnlTiers.map { case (key, value) => key.replace(Constant.DEMOGRAPHIC_METADATA_PNL_TIER_POSTFIX, "").toUpperCase -> value }
  }

  @JsonIgnore
  def getAllPnlSpendingAmount: Map[String, Long] = {
    this.pnlSpendingAmount.map { case (key, value) => key.replace(Constant.DEMOGRAPHIC_METADATA_PNL_SPENDING_AMOUNT_POSTFIX, "").toUpperCase -> value }
  }

  @JsonIgnore
  def getAllCustomerScores: Map[String, String] = {
    this.customerScores.map { case (key, value) => key.replace(Constant.DEMOGRAPHIC_METADATA_CUSTOMER_SCORE_POSTFIX, "").toUpperCase -> value }
  }

  @JsonIgnore
  def getAllCustomerScoreRanks: Map[String, String] = {
    this.customerScoreRanks.map { case (key, value) => key.replace(Constant.DEMOGRAPHIC_METADATA_CUSTOMER_SCORE_RANK_POSTFIX, "").toUpperCase -> value }
  }
}

import profile.domain.customer.Customer._

case class CustomerDisplaySetting(
                                   language: Option[String] = None,
                                 ) {

  def fillWith(displaySetting: Option[CustomerDisplaySetting]): CustomerDisplaySetting = {
    displaySetting match {
      case Some(v) =>
        v.copy(
          language = language.orElse(v.language)
        )
      case _ => this
    }
  }

}

case class Customer(
                     var userId: Option[String] = None,
                     var password: Option[String] = None,
                     var active: Option[Boolean] = None,
                     var status: Option[String] = None,

                     @JsonDeserialize(contentAs = classOf[java.lang.Long])
                     var createdTime: Option[Long] = None,
                     @JsonDeserialize(contentAs = classOf[java.lang.Long])
                     var updatedTime: Option[Long] = None,
                     @JsonDeserialize(contentAs = classOf[java.lang.Long])
                     var deletedAt: Option[Long] = None,
                     var createdBy: Option[String] = None,
                     var updatedBy: Option[String] = None,

                     var phone: Option[String] = None,
                     @JsonDeserialize(contentAs = classOf[java.lang.Integer])
                     var phoneVerified: Option[Int] = None,
                     var email: Option[String] = None,
                     @JsonDeserialize(contentAs = classOf[java.lang.Integer])
                     var emailVerified: Option[Int] = None,
                     var normalizedEmail: Option[String] = None,

                     var oauthGoogleId: Option[String] = None,
                     var oauthFacebookId: Option[String] = None,
                     var oauthAppleId: Option[String] = None,
                     var oauthAzureId: Option[String] = None,
                     var authFirebaseId: Option[String] = None,
                     var devices: Option[Seq[DeviceInfo]] = None,

                     var fullName: Option[String] = None,
                     var firstName: Option[String] = None,
                     var lastName: Option[String] = None,
                     var avatar: Option[String] = None,
                     var gender: Option[String] = None,
                     var birthday: Option[String] = None,

                     var listIdentityDocument: Option[Seq[CustomerIdentityDocument]] = None,
                     var pnlMapping: Option[CustomerPnlMapping] = None,
                     var pnlRankingProgress: Option[CustomerPnlRankingProgress] = None,

                     var tierIds: Option[Seq[Int]] = None,
                     var displaySetting: Option[CustomerDisplaySetting] = None,

                     var referralCode: Option[String] = None,
                     //                     var tierIds: Option[Seq[Int]] = None

                     //                 var maritalStatus: Option[String] = None,
                     //                 var address: Option[String] = None,
                     //                 var extraInfo: Option[Map[String, String]] = None,
                     var metadata: Option[Map[String, Any]] = None,
                     var demographicMetadata: Option[DemographicMetadata] = None,
                     var demographicMetadataExtra: Option[Map[String, Any]] = None,
                     var segments: Option[Seq[Long]] = None,

                     // enhanced data
                     var segmentCodes: Option[Seq[String]] = None,
                   ) extends JdbcRecord {

  //region functions

  def toDto: CustomerDTO = {
    CustomerDTO(
      userId = this.userId,
      createdBy = this.createdBy,
      createdTime = this.createdTime,
      updatedTime = this.updatedTime,

      phone = this.phone,
      phoneVerified = this.phoneVerified,
      email = this.email,
      emailVerified = this.emailVerified,

      fullName = this.fullName,
      firstName = this.firstName,
      lastName = this.lastName,
      avatar = this.avatar,
      gender = this.gender,
      birthday = this.birthday,
      listIdentityDocument = this.listIdentityDocument.map(_.map(_.asSimple)),
      pnlMapping = this.pnlMapping,
      displaySetting = this.displaySetting,
      referralCode = this.referralCode,
      segments = this.segments,
      segmentCodes = this.segmentCodes,
    )
  }

  def toInternalDto: InternalCustomerDTO = {
    InternalCustomerDTO(
      userId = this.userId,
      createdTime = this.createdTime,
      updatedTime = this.updatedTime,

      phone = this.phone,
      phoneVerified = this.phoneVerified,
      email = this.email,
      emailVerified = this.emailVerified,

      fullName = this.fullName,
      firstName = this.firstName,
      lastName = this.lastName,
      avatar = this.avatar,
      gender = this.gender,
      birthday = this.birthday,
      listIdentityDocument = this.listIdentityDocument.map(_.map(_.asSimple)),
      pnlMapping = this.pnlMapping,
      displaySetting = this.displaySetting,
      referralCode = this.referralCode,
      devices = this.devices
    )
  }

  def toCustomerAdminRegisterResponseDto: RegisterByAdminCustomerResponse = {
    RegisterByAdminCustomerResponse(
      vclub_user_id = this.userId.map(_.toLong),
      createdTime = this.createdTime,
      updatedTime = this.updatedTime,

      fullName = this.fullName,
      firstName = this.firstName,
      lastName = this.lastName
    )
  }

  def setName(fullName: Option[String], lastName: Option[String], firstName: Option[String]): Customer = {
    fullName match {
      case Some(v) if v.nonEmpty => setName(v)
      case _ => setName(lastName, firstName)
    }
    this
  }

  def setName(fullName: String): Customer = {
    fullName.trim.lastIndexOf(" ") match {
      case x if x > 0 =>
        this.firstName = fullName.substring(x).toSome
        this.lastName = fullName.substring(0, x).toSome
      case _ =>
        this.firstName = fullName.toSome
        this.lastName = "".toSome
    }
    this.fullName = fullName.toSome

    this
  }

  def setName(familyName: Option[String], givenName: Option[String]): Customer = {
    this.fullName = Seq(familyName, givenName).flatten.filter(_.nonEmpty).mkString(" ").toSome
    this.firstName = givenName
    this.lastName = familyName
    this
  }

  def setAvatar(avatar: Option[String]): Customer = {
    this.avatar = avatar
    this
  }

  @JsonIgnore
  def getVerifiedIdentityNumberByType(docType: CustomerIdentityDocType): Option[String] = {
    this.listIdentityDocument
      .flatMap(_.find(doc => doc.`type`.contains(docType.toString) && doc.verifiedStatus.contains(CustomerIdentityVerifyStatus.VERIFIED.toString))
        .flatMap(_.no))
  }

  @JsonIgnore
  def statusIsActive: Boolean = status.contains(USER_STATUS_ACTIVE_VAL)

  @JsonIgnore
  def getDeviceIds: Seq[String] = {
    this.devices.map(_.map(_.deviceId)).getOrElse(Nil)
  }

  @JsonIgnore
  def getAuth: UserAuthInfo = {
    UserAuthInfo(userId = userId.get)
  }

  @JsonIgnore
  def nameIsEmpty: Boolean = !fullName.exists(_.nonEmpty) && !lastName.exists(_.nonEmpty) && !firstName.exists(_.nonEmpty)

  private def optValue[T](newVal: Option[T], oldValue: Option[T]): Option[T] = newVal.orElse(oldValue)

  def mergeWith(newCustomer: Customer): Customer = {
    this.active = optValue(newCustomer.active, this.active)
    this.status = optValue(newCustomer.status, this.status)

    this.updatedTime = optValue(newCustomer.updatedTime, this.updatedTime)
    this.deletedAt = optValue(newCustomer.deletedAt, this.deletedAt)

    this.phone = optValue(newCustomer.phone, this.phone)
    this.phoneVerified = optValue(newCustomer.phoneVerified, this.phoneVerified)
    this.email = optValue(newCustomer.email, this.email)
    this.emailVerified = optValue(newCustomer.emailVerified, this.emailVerified)

    this.oauthGoogleId = optValue(newCustomer.oauthGoogleId, this.oauthGoogleId)
    this.oauthFacebookId = optValue(newCustomer.oauthFacebookId, this.oauthFacebookId)
    this.oauthAppleId = optValue(newCustomer.oauthAppleId, this.oauthAppleId)
    this.oauthAzureId = optValue(newCustomer.oauthAzureId, this.oauthAzureId)
    this.authFirebaseId = optValue(newCustomer.authFirebaseId, this.authFirebaseId)
    this.devices = optValue(newCustomer.devices, this.devices)

    this.fullName = optValue(newCustomer.fullName, this.fullName)
    this.firstName = optValue(newCustomer.firstName, this.firstName)
    this.lastName = optValue(newCustomer.lastName, this.lastName)
    this.avatar = optValue(newCustomer.avatar, this.avatar)
    this.gender = optValue(newCustomer.gender, this.gender)
    this.birthday = optValue(newCustomer.birthday, this.birthday)

    this.listIdentityDocument = optValue(newCustomer.listIdentityDocument, this.listIdentityDocument)
    this.pnlMapping = optValue(newCustomer.pnlMapping, this.pnlMapping)
    this.pnlRankingProgress = optValue(newCustomer.pnlRankingProgress, this.pnlRankingProgress)
    this.tierIds = optValue(newCustomer.tierIds, this.tierIds)
    this.displaySetting = optValue(newCustomer.displaySetting, this.displaySetting)
    this.referralCode = optValue(newCustomer.referralCode, this.referralCode)

    this.metadata = optValue(newCustomer.metadata, this.metadata)
    this.demographicMetadata = optValue(newCustomer.demographicMetadata, this.demographicMetadata)

    this
  }

  @JsonIgnore
  def omitSensitive(isHistorical: Boolean = false): Customer = {
    this.copy(
      password = None,
      oauthGoogleId = {
        if (isHistorical) this.oauthGoogleId
        else None
      },
      oauthFacebookId = {
        if (isHistorical) this.oauthFacebookId
        else None
      },
      oauthAppleId = {
        if (isHistorical) this.oauthAppleId
        else None
      },
      oauthAzureId = {
        if (isHistorical) this.oauthAzureId
        else None
      },
      authFirebaseId = {
        if (isHistorical) this.authFirebaseId
        else None
      },
      devices = {
        if (isHistorical) this.devices
        else None
      },
      listIdentityDocument = {
        if (isHistorical) this.listIdentityDocument
        else None
      },
      pnlRankingProgress = {
        if (isHistorical) this.pnlRankingProgress
        else None
      }
    )
  }

  //endregion

  override def getPrimaryKeys(): Seq[String] = primaryKeys

  override def getFields(): Seq[String] = fields

  override def isJsonField(field: String): Boolean = {
    field == DEVICES || field == PNL_MAPPING || field == LIST_IDENTITY_DOCUMENT ||
      field == PNL_RANKING_PROGRESS ||
      field == DISPLAY_SETTING ||
      field == METADATA || field == DEMOGRAPHIC_METADATA || field == DEMOGRAPHIC_METADATA_EXTRA
  }

  override def setValues(field: String, value: Any): Unit = field match {
    case USER_ID => userId = value.asOptLong.map(_.toString)
    case PASSWORD => //password = value.asOpt
    case ACTIVE => active = value.asOpt
    case STATUS => status = value.asOptString

    case CREATED_ON => createdTime = value.asOpt[java.sql.Timestamp].map(CustomUtils.timestamptzToLong)
    case UPDATED_ON => updatedTime = value.asOpt[java.sql.Timestamp].map(CustomUtils.timestamptzToLong)
    case DELETED_AT => deletedAt = value.asOpt
    case CREATED_BY => createdBy = value.asOpt
    case UPDATED_BY => updatedBy = value.asOpt

    case PHONE => phone = value.asOpt
    case PHONE_VERIFIED => phoneVerified = value.asOptInt
    case EMAIL => email = value.asOpt
    case EMAIL_VERIFIED => emailVerified = value.asOptInt
    case NORMALIZED_EMAIL => normalizedEmail = value.asOpt

    case OAUTH_GOOGLE_ID => oauthGoogleId = value.asOpt
    case OAUTH_FB_ID => oauthFacebookId = value.asOpt
    case OAUTH_APPLE_ID => oauthAppleId = value.asOpt
    case OAUTH_AZURE_ID => oauthAzureId = value.asOpt
    case AUTH_FIREBASE_ID => authFirebaseId = value.asOpt
    case DEVICES => devices = value.asOptString.map(JsonHelper.fromJson[Seq[DeviceInfo]](_))

    case FULL_NAME => fullName = value.asOpt
    case FIRST_NAME => firstName = value.asOpt
    case LAST_NAME => lastName = value.asOpt
    case AVATAR => avatar = value.asOpt
    case GENDER => gender = value.asOpt
    case BIRTHDAY => birthday = value.asOptString

    case LIST_IDENTITY_DOCUMENT => listIdentityDocument = value.asOptString.map(JsonHelper.fromJson[Seq[CustomerIdentityDocument]](_))
    case PNL_MAPPING => pnlMapping = value.asOptString.map(JsonHelper.fromJson[CustomerPnlMapping](_))
    case PNL_RANKING_PROGRESS => pnlRankingProgress = value.asOptString.map(JsonHelper.fromJson[CustomerPnlRankingProgress](_))
    case TIER_IDS =>
      tierIds = value.asOptString.map(_v =>
        JsonHelper.fromJson[Seq[Int]](if (_v.startsWith("[")) _v else _v.replace('{', '[').replace('}', ']'))
      )

    case SEGMENTS =>
      segments = value.asOptString.map(_v =>
        JsonHelper.fromJson[Seq[Long]](if (_v.startsWith("[")) _v else _v.replace('{', '[').replace('}', ']'))
      )

    case DISPLAY_SETTING => displaySetting = value.asOptString.map(JsonHelper.fromJson[CustomerDisplaySetting](_))

    case REFERRAL_CODE => referralCode = value.asOpt

    case METADATA => metadata = value.asOptString.map(JsonHelper.fromJson[Map[String, Any]](_))

    case DEMOGRAPHIC_METADATA => demographicMetadata = value.asOptString.map(JsonHelper.fromJson[DemographicMetadata](_))
    case DEMOGRAPHIC_METADATA_EXTRA => demographicMetadataExtra = value.asOptString.map(JsonHelper.fromJson[Map[String, Any]](_))

    case _ => throw SqlFieldMissing(field, value)
  }

  override def getValue(field: String): Option[Any] = field match {
    case USER_ID => userId.map(_.toLong)
    case PASSWORD => password
    case ACTIVE => active
    case STATUS => status
    case CREATED_ON => createdTime.map(t => CustomUtils.longToTimestamptz(t))
    case UPDATED_ON => updatedTime.map(t => CustomUtils.longToTimestamptz(t))
    case DELETED_AT => deletedAt
    case CREATED_BY => createdBy
    case UPDATED_BY => updatedBy

    case PHONE => phone
    case PHONE_VERIFIED => phoneVerified
    case EMAIL => email
    case EMAIL_VERIFIED => emailVerified
    case NORMALIZED_EMAIL => normalizedEmail

    case OAUTH_GOOGLE_ID => oauthGoogleId
    case OAUTH_FB_ID => oauthFacebookId
    case OAUTH_APPLE_ID => oauthAppleId
    case OAUTH_AZURE_ID => oauthAzureId
    case AUTH_FIREBASE_ID => authFirebaseId
    case DEVICES => devices.map(JsonHelper.toJson(_))
    case FULL_NAME => fullName
    case FIRST_NAME => firstName
    case LAST_NAME => lastName
    case AVATAR => avatar
    case GENDER => gender
    case BIRTHDAY => birthday.map(Date.valueOf)

    case LIST_IDENTITY_DOCUMENT => listIdentityDocument.map(JsonHelper.toJson(_))
    case PNL_MAPPING => pnlMapping.map(JsonHelper.toJson(_))
    case PNL_RANKING_PROGRESS => pnlRankingProgress.map(JsonHelper.toJson(_))
    case TIER_IDS => None
    case SEGMENTS => None

    case DISPLAY_SETTING => displaySetting.map(JsonHelper.toJson(_))

    case REFERRAL_CODE => referralCode

    case METADATA => metadata.map(JsonHelper.toJson(_))

    case DEMOGRAPHIC_METADATA => demographicMetadata.map(JsonHelper.toJson(_))
    case DEMOGRAPHIC_METADATA_EXTRA => demographicMetadataExtra.map(JsonHelper.toJson(_))

    case _ => throw SqlFieldMissing(field)
  }
}

case class CustomerDAO(ds: DataSource) extends PostgreSqlDAO[Customer] {

  override def createRecord(): Customer = Customer()

  override val table: String = Customer.TBL_NAME

  override val sqlLogger = VHMConfig.sqlLogger.getOrElse(super.sqlLogger)

  private val fieldsString = fields.filterNot(_ == PASSWORD).mkString(",")

  def multiSelect(listUsername: Seq[String]): Map[String, Customer] = {
    val query =
      s"""
         | SELECT $fieldsString
         | FROM $table
         | WHERE $USER_ID IN (${listUsername.map(_ => "?").mkString(",")})
         |""".stripMargin
    execute(executeQuery(query, listUsername.map(_.toLong))(rs => {
      val holder = scala.collection.mutable.Map.empty[String, Customer]
      while (rs.next()) {
        val data = parseResult(rs)
        holder += (data.userId.get -> data)
      }
      holder.toMap
    })(_))
  }

  def select(userId: String): Option[Customer] = Profiler(s"$clazz.select") {
    val query =
      s"""
         | SELECT $fieldsString
         | FROM $table
         | WHERE $USER_ID = ?
         |""".stripMargin
    execute(executeQuery(query, Seq(userId.toLong))(rs => {
      if (rs.next()) Some(parseResult(rs)) else None
    })(_))
  }

  def selectUsernameByEmail(email: String, status: Boolean): Option[String] = {
    val query =
      s"""
         | SELECT $USER_ID
         | FROM $table
         | WHERE LOWER($EMAIL) = ? AND $ACTIVE = ?
         |""".stripMargin
    execute(executeQuery(query, Seq(email.toLowerCase, status))(rs => {
      if (rs.next()) Some(rs.getString(1)) else None
    })(_))
  }

  def selectUsernameByPhone(phone: String, status: Boolean): Option[String] = {
    val query =
      s"""
         | SELECT $USER_ID
         | FROM $table
         | WHERE $PHONE = ? AND $ACTIVE = ?
         |""".stripMargin
    execute(executeQuery(query, Seq(phone, status))(rs => {
      if (rs.next()) Some(rs.getString(1)) else None
    })(_))
  }

  def selectUserByPhoneOrEmail(normPhone: Option[String], email: Option[String], status: Boolean): Option[Customer] = {

    if (normPhone.isEmpty && email.isEmpty) return None

    val conditions = scala.collection.mutable.ListBuffer[String]()
    val conditionsValue = scala.collection.mutable.ListBuffer[Any]()

    if (normPhone.nonEmpty) {
      conditions.append(s"$PHONE = ?")
      conditionsValue.append(normPhone)
    }

    email.foreach(value => {
      conditions.append(s"LOWER($EMAIL) = ?")
      conditionsValue.append(value.toLowerCase)
    })

    val query =
      s"""
         | SELECT $fieldsString
         | FROM $table
         | WHERE (${conditions.mkString(" OR ")}) AND $ACTIVE = ?
         |""".stripMargin
    execute(executeQuery(query, conditionsValue :+ status)(rs => {
      if (rs.next()) Some(parseResult(rs)) else None
    })(_))

  }

  def selectUserByPhone(normPhone: String, status: Boolean): Option[Customer] = {
    val conditions = scala.collection.mutable.ListBuffer[String](s"$PHONE = ?")
    val conditionsValue = scala.collection.mutable.ListBuffer[Any](normPhone)

    val query =
      s"""
         | SELECT $fieldsString
         | FROM $table
         | WHERE (${conditions.mkString(" AND ")}) AND $ACTIVE = ?
         |""".stripMargin
    execute(executeQuery(query, conditionsValue :+ status)(rs => {
      if (rs.next()) Some(parseResult(rs)) else None
    })(_))

  }

  def selectUserByPhoneWithPassState(normPhone: String, status: Boolean): (Option[Customer], Int) = {
    val conditions = scala.collection.mutable.ListBuffer[String](s"$PHONE = ?")
    val conditionsValue = scala.collection.mutable.ListBuffer[Any](normPhone)

    val query =
      s"""
         | SELECT $fieldsString, CASE WHEN $PASSWORD = '-' THEN 0 ELSE 1 END AS state
         | FROM $table
         | WHERE (${conditions.mkString(" AND ")}) AND $ACTIVE = ?
         |""".stripMargin
    execute(executeQuery(query, conditionsValue :+ status)(rs => {
      if (rs.next()) (Some(parseResult(rs)), rs.getInt("state")) else (None, 0)
    })(_))
  }

  def existUserByEmail(email: String, status: Boolean, excludeUser: Option[String] = None): Boolean = {
    val conditions = scala.collection.mutable.ListBuffer[String](s"LOWER($EMAIL) = ?")
    val conditionsValue = scala.collection.mutable.ListBuffer[Any](email.toLowerCase)

    excludeUser.foreach(value => {
      conditions.append(s"$USER_ID != ?")
      conditionsValue.append(value.toLong)
    })

    conditions.append(s"$ACTIVE = ?")
    conditionsValue.append(status)

    val query =
      s"""
         | SELECT 1
         | FROM $table
         | WHERE (${conditions.mkString(" AND ")})
         |""".stripMargin
    execute(executeQuery(query, conditionsValue)(rs => {
      if (rs.next()) rs.getInt(1) == 1 else false
    })(_))

  }

  def existUserByNormalizedEmail(normalizedEmail: String, status: Boolean, excludeUser: Option[String] = None): Boolean = {
    val conditions = scala.collection.mutable.ListBuffer[String](s"$NORMALIZED_EMAIL = ?")
    val conditionsValue = scala.collection.mutable.ListBuffer[Any](normalizedEmail)

    excludeUser.foreach(value => {
      conditions.append(s"$USER_ID != ?")
      conditionsValue.append(value.toLong)
    })

    conditions.append(s"$ACTIVE = ?")
    conditionsValue.append(status)

    val query =
      s"""
         | SELECT 1
         | FROM $table
         | WHERE (${conditions.mkString(" AND ")})
         |""".stripMargin
    execute(executeQuery(query, conditionsValue)(rs => {
      if (rs.next()) rs.getInt(1) == 1 else false
    })(_))

  }

  def existUserByPhone(phone: String, status: Boolean, excludeUser: Option[String] = None): Boolean = {
    val conditions = scala.collection.mutable.ListBuffer[String](s"$PHONE = ?")
    val conditionsValue = scala.collection.mutable.ListBuffer[Any](phone)

    excludeUser.foreach(value => {
      conditions.append(s"$USER_ID != ?")
      conditionsValue.append(value.toLong)
    })

    conditions.append(s"$ACTIVE = ?")
    conditionsValue.append(status)

    val query =
      s"""
         | SELECT 1
         | FROM $table
         | WHERE (${conditions.mkString(" AND ")})
         |""".stripMargin
    execute(executeQuery(query, conditionsValue)(rs => {
      if (rs.next()) rs.getInt(1) == 1 else false
    })(_))

  }

  def selectUserByEmail(email: String, status: Boolean, withNormalize: Boolean = false): Option[Customer] = {
    val conditions = scala.collection.mutable.ListBuffer[String](s"LOWER($EMAIL) = ?")
    val conditionsValue = scala.collection.mutable.ListBuffer[Any](email.toLowerCase)

    if (withNormalize) {
      conditions.append(s"$NORMALIZED_EMAIL = ?")
      conditionsValue.append(EmailHelper.normalize(email))
    }

    val query =
      s"""
         | SELECT $fieldsString
         | FROM $table
         | WHERE (${conditions.mkString(" OR ")}) AND $ACTIVE = ?
         |""".stripMargin
    execute(executeQuery(query, conditionsValue :+ status)(rs => {
      if (rs.next()) Some(parseResult(rs)) else None
    })(_))

  }

  def selectUserByEmailWithPassState(email: String, status: Boolean, withNormalize: Boolean = false): (Option[Customer], Int) = {
    val conditions = scala.collection.mutable.ListBuffer[String](s"LOWER($EMAIL) = ?")
    val conditionsValue = scala.collection.mutable.ListBuffer[Any](email.toLowerCase)

    if (withNormalize) {
      conditions.append(s"$NORMALIZED_EMAIL = ?")
      conditionsValue.append(EmailHelper.normalize(email))
    }

    val query =
      s"""
         | SELECT $fieldsString, CASE WHEN $PASSWORD = '-' THEN 0 ELSE 1 END AS state
         | FROM $table
         | WHERE (${conditions.mkString(" OR ")}) AND $ACTIVE = ?
         |""".stripMargin
    execute(executeQuery(query, conditionsValue :+ status)(rs => {
      if (rs.next()) (Some(parseResult(rs)), rs.getInt("state")) else (None, 0)
    })(_))
  }

  def selectUserByNormalizedEmail(normalizedEmail: String, status: Boolean): Option[Customer] = {
    val conditions = scala.collection.mutable.ListBuffer[String](s"$NORMALIZED_EMAIL = ?")
    val conditionsValue = scala.collection.mutable.ListBuffer[Any](normalizedEmail)

    val query =
      s"""
         | SELECT $fieldsString
         | FROM $table
         | WHERE (${conditions.mkString(" AND ")}) AND $ACTIVE = ?
         |""".stripMargin
    execute(executeQuery(query, conditionsValue :+ status)(rs => {
      if (rs.next()) Some(parseResult(rs)) else None
    })(_))

  }

  def selectUserByEmails(emails: Seq[String], status: Boolean): Map[String, Customer] = {
    val sql =
      s"""
         |SELECT $fieldsString
         |FROM $table
         |WHERE $EMAIL IN (${emails.map(_ => "?").mkString(",")}) AND $ACTIVE = ?
         |""".stripMargin

    execute(executeQuery(sql, emails ++ Seq(status))(rs => {
      val holder = scala.collection.mutable.Map.empty[String, Customer]
      while (rs.next()) {
        val record = parseResult(rs)
        holder(record.email.get) = record
      }
      holder.toMap
    })(_))
  }

  def searchActiveUserId(phones: Seq[String], emails: Seq[String], identityNumbers: Seq[String]): Seq[String] = {
    if (phones.isEmpty && emails.isEmpty && identityNumbers.isEmpty) Nil
    else {
      val orConditions = scala.collection.mutable.ListBuffer[String]()
      val orConditionsValue = scala.collection.mutable.ListBuffer[Any]()

      if (phones.nonEmpty) {
        orConditions += s" $PHONE IN (${phones.map(_ => "?").mkString(",")}) "
        orConditionsValue ++= phones
      }

      if (emails.nonEmpty) {
        orConditions += s" $EMAIL IN (${emails.map(_ => "?").mkString(",")}) "
        orConditionsValue ++= emails
      }

      if (identityNumbers.nonEmpty) {
        orConditions += s" (${identityNumbers.map(_ => s"$LIST_IDENTITY_DOCUMENT @> ?::jsonb").mkString(" OR ")}) "
        orConditionsValue ++= identityNumbers.map(toIdentityJson)
      }

      val query =
        s"""
           | SELECT $USER_ID
           | FROM $table
           | WHERE (${orConditions.mkString(" OR ")}) AND $ACTIVE = ?
           | LIMIT 100
           |""".stripMargin

      execute(executeQuery(query, orConditionsValue :+ UserStatus.Active)(rs => {
        val holder = scala.collection.mutable.ListBuffer.empty[String]
        while (rs.next()) {
          holder += rs.getLong(1).toString
        }
        holder
      })(_))
    }
  }

  /**
   * Fetch customers that have an email but no normalized_email within an ID range
   * @param fromId Start of customer ID range (inclusive, -1 means no lower limit)
   * @param toId End of customer ID range (inclusive, -1 means no upper limit)
   * @param limit Maximum number of customers to fetch
   * @return List of customers
   */
  def fetchCustomersWithoutNormalizedEmailInRange(fromId: Long, toId: Long, limit: Int): Seq[Customer] = {
    val query =
      s"""
         | SELECT $fieldsString
         | FROM $table
         | WHERE $EMAIL IS NOT NULL 
         | AND ($NORMALIZED_EMAIL IS NULL OR $NORMALIZED_EMAIL = '')
         | AND ($fromId < 0 OR $USER_ID >= $fromId)
         | AND ($toId < 0 OR $USER_ID <= $toId)
         | ORDER BY $USER_ID ASC
         | LIMIT $limit
         |""".stripMargin

    execute(executeQuery(query)(rs => {
      val customers = scala.collection.mutable.ListBuffer.empty[Customer]
      while (rs.next()) {
        customers += parseResult(rs)
      }
      customers.toSeq
    })(_))
  }

  def fetchCustomersByFilter(filter: CustomerFilter): Seq[Customer] = Profiler(s"$clazz.fetchCustomersByFilter") {
    var subWhere = "";
    if (filter.requiredDevice.nonEmpty) {
      if (filter.requiredDevice.get) {
        subWhere = s" AND $DEVICES IS NOT NULL"
      } else {
        subWhere = s" AND ($DEVICES IS NULL) "
      }
    }

    val query =
      s"""
         | SELECT $fieldsString
         | FROM $table
         | WHERE $ACTIVE is true
         | AND (${filter.fromId} < 0 OR $USER_ID >= ${filter.fromId})
         | AND (${filter.toId} < 0 OR $USER_ID <= ${filter.toId})
         | ${subWhere}
         | ORDER BY $USER_ID DESC
         | LIMIT ${filter.limit}
         |""".stripMargin

    execute(executeQuery(query)(rs => {
      val customers = scala.collection.mutable.ListBuffer.empty[Customer]
      while (rs.next()) {
        customers += parseResult(rs)
      }
      customers
    })(_))
  }

  /**
   * Update a customer's normalized_email field
   * @param userId Customer ID
   * @param normalizedEmail Normalized email value
   * @return Number of rows updated (should be 1 if successful)
   */
  def updateNormalizedEmail(userId: String, normalizedEmail: String): Int = {
    val query =
      s"""
         | UPDATE $table
         | SET $NORMALIZED_EMAIL = ?
         | WHERE $USER_ID = ?
         |""".stripMargin

    execute(executeUpdate(query, Seq(normalizedEmail, userId.toLong))(_))
  }

  def updateLoginTimeWithoutLocking(userId: String, firstLoginTime: Option[Long], lastLoginTime: Option[Long]): Int = {
    val updatedFields = Map(
      "first_login_time" -> firstLoginTime,
      "last_login_time" -> lastLoginTime,
      "first_login_date" -> CTimeUtils.epochToTimestampTz(firstLoginTime, throwExceptionWhenFail = false).orNull,
      "last_login_date" -> CTimeUtils.epochToTimestampTz(lastLoginTime, throwExceptionWhenFail = false).orNull
    )

    val loginTimeUpdate = new PGobject()
    loginTimeUpdate.setType("jsonb")
    loginTimeUpdate.setValue(JsonHelper.toJson(updatedFields))

    val query =
      s"""
         | UPDATE $table
         | SET $DEMOGRAPHIC_METADATA = COALESCE($DEMOGRAPHIC_METADATA, '{}'::jsonb) || ?::jsonb
         | WHERE $USER_ID = ?
         |""".stripMargin

    execute(executeUpdate(query, Seq(loginTimeUpdate, userId.toLong))(_))
  }

  def selectActiveWithGtId(userIdStart: Long, userIdTo: Long, reqSize: Int): Seq[Customer] = {
    val query =
      s"""
         |SELECT $fieldsString
         |FROM $table
         |WHERE $USER_ID > $userIdStart AND $ACTIVE = ${UserStatus.Active} AND ($userIdTo < 0 OR $USER_ID <= $userIdTo)
         |ORDER BY $USER_ID ASC
         |LIMIT $reqSize
         |""".stripMargin

    execute(executeQuery(query)(rs => {
      val holder = scala.collection.mutable.ListBuffer.empty[Customer]
      while (rs.next()) {
        holder += parseResult(rs)
      }
      holder
    })(_))
  }

  def selectUserByActive(from: Int, size: Int,
                         active: Boolean,
                         fromDeleteAt: Option[Long], toDeleteAt: Option[Long]): Seq[String] = {

    val conditions = scala.collection.mutable.ListBuffer[String](s"$ACTIVE = ?")
    val conditionValues = scala.collection.mutable.ListBuffer[Any](active)

    (fromDeleteAt, toDeleteAt) match {
      case (Some(_formTime), Some(_toTime)) => conditions += s" $DELETED_AT >= ${_formTime} AND $DELETED_AT < ${_toTime}"

      case (Some(_formTime), _) => conditions += s" $DELETED_AT >= ${_formTime} "

      case (_, Some(_toTime)) => conditions += s" $DELETED_AT < ${_toTime}"

      case _ =>
    }

    val query =
      s"""
         | SELECT $USER_ID
         | FROM $table
         | WHERE (${conditions.mkString(" AND ")})
         | ${buildLimit(from, size)}
         |""".stripMargin

    execute(executeQuery(query, conditionValues)(rs => {
      val holder = scala.collection.mutable.ListBuffer.empty[String]
      while (rs.next()) {
        holder += rs.getLong(1).toString
      }
      holder
    })(_))
  }

  private def toIdentityJson(identityNo: String): String = {
    s"""[{"no":"${Utils.asciifolding(identityNo)}", "verified_status": "${CustomerIdentityVerifyStatus.VERIFIED.toString}"}]"""
  }

}


case class CustomerFilter(
                           requiredDevice: Option[Boolean],
                           limit: Int,
                           fromId: Long,
                           toId: Long
                         ) {
}