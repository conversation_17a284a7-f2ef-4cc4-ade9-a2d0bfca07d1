package profile.domain.response



case class RegisterByAdminResponseDTO(
                                       otp: Option[RegisterByAdminOtpResponse] = None,
                                       customer: Option[RegisterByAdminCustomerResponse] = None
                                   ) {
  val state = {
    if (otp.nonEmpty) 0
    else 1
  }

}

case class RegisterByAdminOtpResponse(
                                     isSentOtp: Boolean = true,
                                     otpChannel: String, // OtpChannel
                                     expiredInSeconds: Int
                                   )

case class RegisterByAdminCustomerResponse(
                                        vclub_user_id: Option[Long] = None,
                                        createdTime: Option[Long] = None,
                                        updatedTime: Option[Long] = None,
                                        registerChannelType: Option[String] = None,

                                        fullName: Option[String] = None,
                                        firstName: Option[String] = None,
                                        lastName: Option[String] = None,
                                      )