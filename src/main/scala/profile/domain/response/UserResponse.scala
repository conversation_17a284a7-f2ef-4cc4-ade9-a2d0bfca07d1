package profile.domain.response

import profile.domain.entity.{JwtToken, UserAuthInfo}

/**
 * <AUTHOR>
 */
case class UserResponse(
                         code: Int,
                         userInfo: UserAuthInfo = null,
                         userProfile: Option[CustomerDTO] = None,
                         jwt: JwtToken = null
                       )

//case class UserResponseForAdmin(
//                                 code: Int,
//                                 userInfo: UserInfo = null,
//                                 userProfile: Option[UserProfile] = None,
//                                 msg: String = null,
//                                 session: Option[SessionInfo] = None,
//                                 defaultOAuthCredential: Option[Boolean] = None,
//                                 password: Option[String] = None
//                               )
