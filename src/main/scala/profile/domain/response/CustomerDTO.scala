package profile.domain.response

import profile.domain.customer.{CustomerDisplaySetting, CustomerIdentityDocument, CustomerPnlMapping}

case class CustomerDTO(
                        userId: Option[String] = None,
                        createdBy: Option[String] = None,
                        createdTime: Option[Long] = None,
                        updatedTime: Option[Long] = None,

                        phone: Option[String] = None,
                        phoneVerified: Option[Int] = None,
                        email: Option[String] = None,
                        emailVerified: Option[Int] = None,

                        fullName: Option[String] = None,
                        firstName: Option[String] = None,
                        lastName: Option[String] = None,
                        avatar: Option[String] = None,
                        gender: Option[String] = None,
                        birthday: Option[String] = None,
                        listIdentityDocument: Option[Seq[CustomerIdentityDocument]] = None,
                        pnlMapping: Option[CustomerPnlMapping] = None,
                        displaySetting: Option[CustomerDisplaySetting] = None,
                        referralCode: Option[String] = None,
                        segments: Option[Seq[Long]] = None,
                        segmentCodes: Option[Seq[String]] = None,
                      ) {
  val id = userId.filter(_.nonEmpty).map(_.toLong)
}