package profile.domain.event

case class CustomerIdentityRejectedEvent(
                                          customerId: Long,
                                          fullName: String,
                                          identityType: String,
                                          identityNo: String,
                                          identityDocumentVerifyId: Long,
                                          rejectedBy: String,
                                          rejectedTime: Long,
                                          rejectedReasonCode: String,
                                          rejectedReasonMessage: Option[String] = None,
                                        )
