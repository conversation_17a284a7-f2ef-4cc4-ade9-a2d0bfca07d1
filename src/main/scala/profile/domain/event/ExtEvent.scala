package profile.domain.event

import com.fasterxml.jackson.databind.PropertyNamingStrategy
import com.fasterxml.jackson.databind.annotation.JsonNaming

import java.util.UUID

/**
 * <AUTHOR> 10/28/24 10:33
 */
@JsonNaming(classOf[PropertyNamingStrategy.SnakeCaseStrategy])
case class ExtEvent[T](
                        id: String = UUID.randomUUID().toString,
                        asType: Option[String] = None,
                        code: String,
                        tenantId: Option[Long] = None,
                        source: String,
                        orgCode: Option[String]=None,
                        originalId: Option[String],
                        sourceSystemId: Option[String] = None,
                        email: Option[String] = None,
                        eventTime: Long = System.currentTimeMillis(),
                        data: T,
                        customerId: Option[Long],
                        pnl: Option[String] = None,
                        orderId: Option[String]=None,
                        customerIdentity: CustomerIdentifyRequest,
                        vclubEventId: Option[Long] = None,
                        vclubReceivedTime: Option[Long] = None,
                      ) {

  val `type`: String = "EXT_EVENT"

}

@JsonNaming(classOf[PropertyNamingStrategy.SnakeCaseStrategy])
case class CustomerIdentifyRequest(
                                    vclubUserId: Option[Long],
                                    email: Option[String],
                                    phone: Option[String]
                                  )

object ExtEventCode extends Enumeration {

  val CUSTOMER_CREATED = Value("CUSTOMER_CREATED")
  val CUSTOMER_PNL_MAPPED = Value("CUSTOMER_PNL_MAPPED")
  val CUSTOMER_DEMOGRAPHIC_METADATA_CHANGED = Value("CUSTOMER_DEMOGRAPHIC_METADATA_CHANGED")


}