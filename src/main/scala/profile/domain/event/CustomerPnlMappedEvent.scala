package profile.domain.event

import com.fasterxml.jackson.annotation.JsonProperty
import com.fasterxml.jackson.databind.annotation.JsonDeserialize
import profile.domain.customer.CustomerPnlMappingData
import vn.vhm.common.domain.OptionImplicits.OptionImplicitAny

/**
 * <AUTHOR>
 */
case class CustomerPnlMappedEvent(
                                   @JsonProperty("PNL")
                                   pnl: String,

                                   @JsonProperty("PNL_CUSTOMERS_TBL_ID")
                                   @JsonDeserialize(contentAs = classOf[java.lang.Long])
                                   pnlCustomersTblId: Option[Long] = None,

                                   @JsonProperty("PNL_PROFILE_ID")
                                   pnlProfileId: Option[String] = None,

                                   @JsonProperty("PNL_USER_ID")
                                   pnlUserId: Option[String] = None,

                                   @JsonProperty("IDENTITY_NUMBER_DEFAULT")
                                   identityNumberDefault: Option[String] = None,

                                   @JsonProperty("PHONE")
                                   phone: Option[String] = None,

                                   @JsonProperty("EMAIL")
                                   email: Option[String] = None,

                                   @JsonProperty("MAPPING_AT")
                                   @JsonDeserialize(contentAs = classOf[java.lang.Long])
                                   mappingAt: Option[Long] = None,

                                   @JsonProperty("CONSENT_STATUS")
                                   @JsonDeserialize(contentAs = classOf[java.lang.Integer])
                                   consentStatus: Option[Int] = None,

                                   @JsonProperty("INITIAL_TIER_CODE")
                                   initialTierCode: Option[String] = None,

                                   @JsonProperty("NUMBER_OF_TRANSACTION")
                                   @JsonDeserialize(contentAs = classOf[java.lang.Integer])
                                   numberOfTransaction: Option[Int] = None,

                                   @JsonProperty("TOTAL_SPEND_AMOUNT")
                                   @JsonDeserialize(contentAs = classOf[java.math.BigDecimal])
                                   totalSpendAmount: Option[java.math.BigDecimal] = None,

                                   @JsonProperty("TOTAL_SPEND_AMOUNT_RENT")
                                   @JsonDeserialize(contentAs = classOf[java.math.BigDecimal])
                                   totalSpendAmountRent: Option[java.math.BigDecimal] = None,

                                   @JsonProperty("TOTAL_SPEND_AMOUNT_PURCHASE")
                                   @JsonDeserialize(contentAs = classOf[java.math.BigDecimal])
                                   totalSpendAmountPurchase: Option[java.math.BigDecimal] = None,

                                   @JsonProperty("NUM_OF_STUDYING_CHILD")
                                   @JsonDeserialize(contentAs = classOf[java.lang.Integer])
                                   numOfStudyingChild: Option[Int] = None,

                                   @JsonProperty("NUM_OF_STUDIED_CHILD")
                                   @JsonDeserialize(contentAs = classOf[java.lang.Integer])
                                   numOfStudiedChild: Option[Int] = None,

                                   @JsonProperty("MIGRATE_CALCULATE_AT")
                                   @JsonDeserialize(contentAs = classOf[java.lang.Long])
                                   migrateCalculateAt: Option[Long] = None,

                                   @JsonProperty("ORIGINAL_ID")
                                   originalId: Option[String] = None,

                                   @JsonProperty("CUSTOMER_ID")
                                   @JsonDeserialize(contentAs = classOf[java.lang.Long])
                                   customerId: Option[Long] = None,

                                   @JsonProperty("FIRST_NAME")
                                   firstName: Option[String] = None,

                                   @JsonProperty("LAST_NAME")
                                   lastName: Option[String] = None,

                                   @JsonProperty("FULL_NAME")
                                   fullName: Option[String] = None,
                                 ) {
  def fromCustomerPnlMappingData(customerPnlMapping: CustomerPnlMappingData): CustomerPnlMappedEvent = {
    this.copy(
      pnl = customerPnlMapping.pnl,
      pnlCustomersTblId = customerPnlMapping.id,
      pnlProfileId = customerPnlMapping.pnlProfileId,
      pnlUserId = customerPnlMapping.pnlUserId,
      identityNumberDefault = customerPnlMapping.identityNumberDefault,
      phone = customerPnlMapping.phone,
      email = customerPnlMapping.email,
      mappingAt = customerPnlMapping.mappingAt.toSome,
      consentStatus = Some(customerPnlMapping.consentStatus),
      initialTierCode = customerPnlMapping.initTierCode,
      numberOfTransaction = customerPnlMapping.numberOfTransaction,
      totalSpendAmount = customerPnlMapping.totalSpendAmount,
      totalSpendAmountRent = customerPnlMapping.totalSpendAmountRent,
      totalSpendAmountPurchase = customerPnlMapping.totalSpendAmountPurchase,
      numOfStudyingChild = customerPnlMapping.numOfStudyingChild,
      numOfStudiedChild = customerPnlMapping.numOfStudiedChild,
      migrateCalculateAt = customerPnlMapping.migrateCalcAtTime,
    )
  }
}
