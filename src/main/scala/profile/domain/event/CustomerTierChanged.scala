package profile.domain.event

import com.fasterxml.jackson.databind.annotation.JsonDeserialize
import profile.domain.customer.CustomerPnlMapping

/**
 * <AUTHOR> 7/20/24 06:53
 */
case class CustomerTierChanged(
                                source: String,
                                timestamp: Long,
                                customerId: String,
                                oldTierId: Option[Int],
                                oldTierCode: Option[String],
                                newTierId: Option[Int],
                                newTierCode: Option[String],
                                pnlMapping: Option[CustomerPnlMapping],

                                @JsonDeserialize(contentAs = classOf[java.lang.Long])
                                dataTime: Option[Long] = None
                              )
