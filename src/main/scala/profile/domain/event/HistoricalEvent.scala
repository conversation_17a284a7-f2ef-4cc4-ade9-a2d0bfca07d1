package profile.domain.event

import com.fasterxml.jackson.annotation.JsonIgnore
import com.fasterxml.jackson.databind.JsonNode

/**
 * <AUTHOR> 5/15/20 11:08 AM
 */
object HistoricalActionEvent extends Enumeration {
  val CREATED = Value("CREATED")
  val UPDATED = Value("UPDATED")
  val DELETED = Value("DELETED")
}

case class HistoricalEvent[T](
                               action: String,
                               timestamp: Long,
                               objectId: String,
                               data: Option[T],
                               oldData: Option[T],
                               updateData: Option[JsonNode] = None,
                               source: Option[String] = None,
                               performer: Option[String] = None,
                             ) {

  @JsonIgnore
  def isCreated: Boolean = action.equalsIgnoreCase(HistoricalActionEvent.CREATED.toString)

  @JsonIgnore
  def isUpdated: Boolean = action.equalsIgnoreCase(HistoricalActionEvent.UPDATED.toString)

  @JsonIgnore
  def isCreatedOrUpdated: Boolean = isCreated || isUpdated

}
