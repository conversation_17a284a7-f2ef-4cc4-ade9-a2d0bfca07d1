package profile.domain.request

import com.twitter.finagle.http.Request
import core.quota.domain.QuotaFilterContext.QuotaFilterContextSyntax
import profile.service.ProtectTokenData
import profile.util.{Constant, CustomUtils}
import vn.vhm.common.domain.OptionImplicits.OptionImplicitAny

/**
 * <AUTHOR>
 */
trait BaseShieldRequest {

  val request: Request

  private lazy val appBuildNumber: Option[String] = request.headerMap.get(Constant.APP_BUILD_NUMBER_HEADER)

  private lazy val protectTokens: Option[Seq[ProtectTokenData]] = request.headerMap
    .filter(h => Constant.PROTECT_TOKEN_HEADERS.map(_.toLowerCase).contains(h._1.toLowerCase))
    .map(h => ProtectTokenData(h._1, h._2))
    .toSeq
    .toSome
    .filter(_.nonEmpty)

  private lazy val shieldToken: Option[String] = request.headerMap.get(Constant.SHIELD_TOKEN_HEADER)

  private lazy val deviceId: Option[String] = request.headerMap.get(Constant.DEVICE_ID_HEADER)

  private lazy val deviceOs: Option[String] = request.headerMap.get(Constant.DEVICE_PLATFORM_HEADER)

  private lazy val ipAddress: Option[String] = request.optIP

  private lazy val userAgent: Option[String] = request.userAgent

  private lazy val requestId: Option[String] = request.headerMap.get(Constant.REQUEST_ID_HEADER)

  def getShieldDataRequest: ShieldDataRequest = {
    ShieldDataRequest(
      appVersion = appBuildNumber,
      protectTokens = protectTokens,
      shieldToken = shieldToken,
      deviceId = deviceId,
      deviceOs = deviceOs,
      ipAddress = ipAddress,
      userAgent = userAgent,
      requestId = requestId,
      lang = CustomUtils.getLang(request)
    )
  }

}

case class ShieldDataRequest(
                              appVersion: Option[String],
                              protectTokens: Option[Seq[ProtectTokenData]],
                              shieldToken: Option[String],
                              deviceId: Option[String],
                              deviceOs: Option[String],
                              ipAddress: Option[String],
                              userAgent: Option[String],
                              requestId: Option[String],
                              lang: String
                            )