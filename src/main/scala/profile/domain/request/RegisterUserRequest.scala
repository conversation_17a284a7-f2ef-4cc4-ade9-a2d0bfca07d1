package profile.domain.request

import com.fasterxml.jackson.annotation.JsonProperty
import com.twitter.finagle.http.Request
import com.twitter.finatra.validation.{MethodValidation, ValidationResult}
import org.joda.time.format.DateTimeFormat
import profile.domain.customer.{Customer, CustomerDisplaySetting}
import profile.domain.entity.DeviceInfo
import profile.domain.request.UpdateUserRequest.{checkTimeFormat, dayOfBirthFormat, enumGenders}
import profile.exception.InvalidPhoneException
import profile.util.PhoneUtils
import vn.vhm.common.domain.OptionImplicits.OptionImplicitAny

import javax.inject.Inject

/**
 * <AUTHOR>
 */
case class ForgetPasswordBodyRequest(
                                      password: String,
                                      @JsonProperty("phone") phone: String,
                                      @JsonProperty("verify_code") verifyCode: String
                                    ) {
  if (!vn.vhm.common.util.PhoneUtils.isPhoneNumber(phone)) throw InvalidPhoneException()

  val normPhone = PhoneUtils.normalizePhone(phone)
}

case class RegisterOAuthUserBodyRequest(
                                         @JsonProperty("phone_token") phoneToken: Option[String],
                                         @JsonProperty("oauth_type") oauthType: String,
                                         id: String,
                                         token: String,
                                         @JsonProperty("phone") phone: Option[String],
                                         @JsonProperty("verify_code") verifyCode: Option[String],
                                         @JsonProperty("full_name") fullName: Option[String] = None,
                                         password: String
                                       ) {

  phone.foreach(p => if (!vn.vhm.common.util.PhoneUtils.isPhoneNumber(p)) throw InvalidPhoneException())

  val normPhone: Option[String] = phone.map(v => PhoneUtils.normalizePhone(v))
}

case class UserOAuthBodyRequest(
                                 @JsonProperty("oauth_type") oauthType: String,
                                 id: String,
                                 token: String,
                                 @JsonProperty("full_name") fullName: Option[String] = None
                               )

case class UpdatePhoneBodyRequest(
                                   @JsonProperty("phone") phone: String,
                                   @JsonProperty("verify_code") verifyCode: String
                                 ) {
  val normalizedPhoneNumber = PhoneUtils.normalizePhone(phone)
}

case class UpdateEmailBodyRequest(email: String)

object UpdateUserRequest {
  val enumGenders = Seq("male", "female", "other")
  val dayOfBirthFormat = "yyyy-MM-dd"
  val enumMaritalStatuses = Seq("single", "married", "divorced", "other")
  val marriedDayFormat = "dd/MM/yyyy"
  val enumContactTypes = Seq("buyer", "renter", "seller", "lessor", "agency", "invester")

  def checkTimeFormat(value: String, format: String): Boolean = {
    try {
      DateTimeFormat.forPattern(format).parseMillis(value)
      true
    } catch {
      case _: IllegalArgumentException => false
    }
  }
}

case class UpdateUserRequest(
                              fullName: Option[String] = None,
                              firstName: Option[String] = None,
                              lastName: Option[String] = None,
                              gender: Option[String] = None,
                              birthday: Option[String] = None,
                              deviceId: Option[String] = None,
                              displaySetting: Option[CustomerDisplaySetting] = None,

                              @Inject request: Request = null
                            ) {

  @MethodValidation
  def checkValid: ValidationResult = {
    if (firstName.exists(_.length > 100)) return ValidationResult.Invalid("first_name too long")
    if (lastName.exists(_.length > 100)) return ValidationResult.Invalid("last_name too long")

    if (gender.isDefined && !enumGenders.contains(gender.get)) return ValidationResult.Invalid("gender is invalid")
    if (birthday.isDefined && !checkTimeFormat(birthday.get, dayOfBirthFormat)) return ValidationResult.Invalid(s"`birthday` is invalid for format `$dayOfBirthFormat`")

    ValidationResult.Valid()
  }

  def fillWith(base: Customer): Customer = {

    val newVal = base.copy()

    if (fullName.isDefined || firstName.isDefined || lastName.isDefined) {
      newVal.setName(fullName, lastName, firstName)
    }

    deviceId.filterNot(_.isEmpty).foreach(deviceId => {
      val newDevices = newVal.devices match {
        case Some(v) if !v.map(_.deviceId).contains(deviceId) =>
          v ++ Seq(DeviceInfo(deviceId = deviceId, addedTime = System.currentTimeMillis().toSome))
        case Some(v) => v
        case _ =>
          Seq(DeviceInfo(deviceId = deviceId, addedTime = System.currentTimeMillis().toSome))
      }
      newVal.devices = newDevices.toSome
    })

    gender.foreach(_ => newVal.gender = gender)
    birthday.foreach(_ => newVal.birthday = birthday)

    displaySetting.foreach(value => newVal.displaySetting = value.fillWith(newVal.displaySetting).toSome)

    newVal
  }
}

