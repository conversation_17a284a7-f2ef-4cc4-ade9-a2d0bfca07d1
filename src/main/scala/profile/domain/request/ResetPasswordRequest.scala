package profile.domain.request

import com.twitter.finagle.http.Request
import com.twitter.finatra.validation.{MethodValidation, ValidationResult}
import profile.controller.http.filter.EmailQuotaJwtReq
import profile.controller.http.filter.phone.PhoneQuotaJwtReq
import profile.exception.InvalidParamException
import profile.util.{Constant, PasswordHelper}

import javax.inject.Inject
import scala.concurrent.duration.Duration

case class ResetPasswordRequest(
                                 identify: String,
                                 tokenCaptcha: Option[String],
                                 @Inject request: Request
                               ) extends BaseIdentifyRequest with PhoneQuotaJwtReq with EmailQuotaJwtReq {

  override def optNormPhone(): Option[String] = if (isPhone) Some(getNormPhone) else None

  override def optQuotaEmail(): Option[String] = if (isEmail) Some(getEmail) else None

  override def optTokenCaptcha(): Option[String] = tokenCaptcha

  override def optLockInDuration(): Option[Duration] = None

  def getPlatform: Option[String] = request.headerMap.get(Constant.DEVICE_PLATFORM_HEADER)
}

case class VerifyResetPasswordRequest(
                                       identify: String,
                                       code: String,
                                       tokenCaptcha: Option[String],
                                       @Inject request: Request) extends BaseIdentifyRequest with PhoneQuotaJwtReq with EmailQuotaJwtReq {


  override def optNormPhone(): Option[String] = if (isPhone) Some(getNormPhone) else None

  override def optQuotaEmail(): Option[String] = if (isEmail) Some(getEmail) else None

  override def optTokenCaptcha(): Option[String] = tokenCaptcha

  override def optLockInDuration(): Option[Duration] = None
}

case class ConfirmResetPasswordRequest(
                                        identify: String,
                                        token: Option[String],
                                        newPassword: String,

                                        deviceId: Option[String],
                                        @Inject request: Request) extends BaseIdentifyRequest {

  def getToken: String = token.getOrElse(throw InvalidParamException("token is required"))

  @MethodValidation
  def checkValid: ValidationResult = try {

    PasswordHelper.validatePassword(newPassword)

    ValidationResult.Valid()

  } catch {
    case e: Exception => ValidationResult.Invalid(e.getMessage)
  }

}
