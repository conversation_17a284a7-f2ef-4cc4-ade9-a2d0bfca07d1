package profile.domain.request

import com.twitter.finagle.http.Request
import com.twitter.finatra.http.request.RequestUtils
import com.twitter.finatra.validation.{MethodValidation, ValidationResult}
import org.apache.commons.io.FileUtils
import profile.controller.http.filter.UserQuotaJwtReq
import profile.domain.FileLocalData
import vn.vhm.common.exception.{NotFoundException, RCustomException, VhmCustomException}

import java.io.File
import java.util.UUID
import javax.inject.Inject

/**
 * <AUTHOR>
 */
case class UpdateAvatarRequest(@Inject req: Request) extends UserQuotaJwtReq {

  lazy val multiParams = RequestUtils.multiParams(req)

  lazy val file = {
    val fileBody = multiParams.getOrElse("file", throw RCustomException("not_found", "file"))

    val fileExt = fileBody.contentType match {
      case Some("image/jpeg") => "jpg"
      case Some("image/jpg") => "jpg"
      case Some("image/png") => "png"
      case Some(x) => throw VhmCustomException("invalid_param", s"Unsupported content-type [$x]")
      case _ => throw NotFoundException("content_type not found")
    }

    val id = UUID.randomUUID().toString
    val localFilePath = s"/tmp/$id/avatar.$fileExt"
    FileUtils.writeByteArrayToFile(new java.io.File(localFilePath), fileBody.data)

    FileLocalData(
      localPath = localFilePath,
      numBytes = fileBody.data.length,
      mime = fileBody.contentType.getOrElse("image/jpg"),
      fileName = fileBody.filename.getOrElse("avatar.jpg")
    )

  }

  def close(): Unit = try {
    FileUtils.deleteQuietly(new File(file.localPath))
  } catch {
    case _: Exception =>
  }

  @MethodValidation
  def checkValid: ValidationResult = try {
    ValidationResult.Valid()
  } catch {
    case e: Exception => ValidationResult.Invalid(e.getMessage)
  }

  override def quotaUsername(): String = {
    import profile.controller.http.filter.user.UserContext.UserContextSyntax
    req.getUsername()
  }

  override def optTokenCaptcha(): Option[String] = {
    multiParams.get("token_captcha").map(v => new String(v.data))
  }

}
