package profile.domain.request

import com.fasterxml.jackson.annotation.JsonProperty
import profile.exception.InvalidPhoneException
import profile.util.PhoneUtils

/**
 * <AUTHOR>
 */
case class VerifyCodePhoneRequest(
                                   @JsonProperty("phone") phoneNumber: String,
                                   @JsonProperty("verify_code") verifyCode: String
                                 ) {

  if (!vn.vhm.common.util.PhoneUtils.isPhoneNumber(phoneNumber)) throw InvalidPhoneException()

  val normalizedPhone = PhoneUtils.normalizePhone(phoneNumber)
}
