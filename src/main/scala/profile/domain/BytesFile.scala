package profile.domain

import com.fasterxml.jackson.annotation.{JsonIgnore, JsonProperty}

import java.util.UUID

case class BytesFile(
                      id: String = UUID.randomUUID().toString,
                      localPath: String,
                      filePath: String,
                      contentType: String,
                      originFileName: Option[String] = None,
                      idx: Int
                    ) {
  @JsonIgnore
  def isImage: Boolean = contentType.startsWith("image")

}

case class VClubFileInfo(
                          @JsonProperty("fileName") fileName: String,
                          format: String,
                          `type`: String
                        )
