package profile.service

import caas.service.CaasService
import com.fasterxml.jackson.databind.node.ObjectNode
import com.google.common.cache.CacheBuilder
import com.twitter.finagle.http.Request
import com.twitter.util.{Future, Return, Throw, Try}
import io.jsonwebtoken.impl.DefaultClaims
import io.jsonwebtoken.impl.crypto.DefaultJwtSignatureValidator
import io.jsonwebtoken.io.Decoders
import io.jsonwebtoken.{Claims, SignatureAlgorithm}
import org.apache.commons.io.FileUtils
import profile.controller.http.{InternalValidateAuthRequest, ValidateOwnerRequest}
import profile.controller.http.filter.parser._
import profile.controller.http.request.{CalcQuotaEmailRequest, CalcQuotaPhoneRequest, DeactivateByAdminRequest, RegisterByAdminData, RegisterUserJwtData, RegisterUserOnboardJwtRequest}
import profile.domain.customer.Customer
import profile.domain.entity.RegisterChannelType.RegisterChannelType
import profile.domain.entity._
import profile.domain.event.CustomerNewSignedInEvent
import profile.domain.request._
import profile.domain.response.{CustomerDTO, RegisterByAdminCustomerResponse, RegisterByAdminOtpResponse, RegisterByAdminResponseDTO}
import profile.exception._
import profile.util.Constant._
import profile.util.{Constant, EmailHelper, PasswordHelper, PhoneUtils, SnowflakeDefault}
import vn.vhm.common.client.kafka_010.StringKafkaProducer
import vn.vhm.common.domain.Implicits.async
import vn.vhm.common.domain.OptionImplicits.OptionImplicitAny
import vn.vhm.common.domain.profiling.Profiler
import vn.vhm.common.exception.UnsupportedException
import vn.vhm.common.repository.lock.AsyncLockManager
import vn.vhm.common.util.{JsonHelper, Utils, ZConfig}

import java.io.File
import java.security.interfaces.{RSAPrivateKey, RSAPublicKey}
import java.security.spec.{PKCS8EncodedKeySpec, X509EncodedKeySpec}
import java.security.{Key, KeyFactory}
import java.util.concurrent.TimeUnit
import java.util.{Base64, Date}
import javax.crypto.spec.SecretKeySpec
import javax.inject.Inject
import scala.concurrent.duration.{DurationInt, DurationLong}

/**
 * <AUTHOR>
 */
trait AuthenJwtService {

  def registerByPhone(normPhone: String): Future[Unit]

  def registerByPhone(normPhone: String, deviceId: String, platform: Option[String]): Future[Unit]

  def registerByEmail(email: String): Future[Unit]

  def registerByEmail(email: String, deviceId: String): Future[Unit]

  def verifyRegisterByPhone(normPhone: String, code: String): Future[String]

  def verifyRegisterByPhone(normPhone: String, code: String, deviceId: String): Future[VerifyRegisterResponse]

  def verifyRegisterByEmail(email: String, code: String): Future[String]

  def verifyRegisterByEmail(email: String, code: String, deviceId: String): Future[VerifyRegisterResponse]

  def confirmRegisterByPhone(normPhone: String, tokenPhone: String, registerData: RegisterUserJwtData): Future[Customer]

  def confirmRegisterByEmail(email: String, token: String, registerData: RegisterUserJwtData): Future[Customer]

  def loginAfterRegisterSuccess(user: Customer, registerData: RegisterUserJwtData): Future[UserAuthProfile]

  def loginPhone(request: LoginByPhoneRequest): Future[UserAuthProfile]

  def loginEmail(request: LoginByEmailRequest): Future[UserAuthProfile]

  def loginById(customerId: Long, jwtRequestedBy: String, additionalClaims: Map[String, Any]): Future[UserAuthProfile]

  def loginWithOAuth(request: LoginOAuthReq): Future[UserAuthProfile]

  def refreshAccessToken(refreshToken: String): Future[UserAuthProfile]

  def logout(refreshToken: String): Future[Unit]

  def getUsernameFromAccessToken(accessToken: String): String

  def parseAccessToken(accessToken: String, uncheckExpireTime: Boolean): (String, Map[String, Any])

  def forgotPasswordByPhone(phone: String, platform: Option[String]): Future[Unit]

  def forgotPasswordByEmail(email: String): Future[Unit]

  def verifyForgotPasswordByPhone(normPhone: String, code: String): Future[String]

  def verifyForgotPasswordByEmail(email: String, code: String): Future[String]

  def confirmForgotPasswordByPhone(normPhone: String, token: String, newPassword: String, deviceId: String): Future[Unit]

  def confirmForgotPasswordByEmail(email: String, token: String, newPassword: String, deviceId: String): Future[Unit]

  def deactivateUser(username: String, passHashed: String): Future[Unit]

  def validateOwner(request: ValidateOwnerRequest): Future[String]

  def validateAuth(request: InternalValidateAuthRequest): Future[Boolean]

  def clearOtherSessions(event: CustomerNewSignedInEvent): Future[Unit]

  def registerByAdmin(request: Request, data: RegisterByAdminData): Future[RegisterByAdminResponseDTO]

  def confirmRegisterByAdmin(data: RegisterByAdminData, registerChannelType: RegisterChannelType): Future[RegisterByAdminResponseDTO]

  def registerUserOnboard(request: RegisterUserOnboardJwtRequest): Future[Customer]

  def deactivateUserByAdmin(request: DeactivateByAdminRequest): Future[Unit]

}


case class AuthenJwtServiceImpl @Inject()(
                                           verifyDataService: VerifyDataService,
                                           caasService: CaasService,
                                           profileService: ProfileService,
                                           quotaService: HttpQuotaService,

                                           createOrUpdatePhoneLock: AsyncLockManager,
                                           createOrUpdateEmailLock: AsyncLockManager,
                                           updateCustomerLock: AsyncLockManager,

                                           kafkaProducer: StringKafkaProducer,
                                           customerNewSignedInTopic: String,

                                         ) extends AuthenJwtService {

  private val clazz = getClass.getCanonicalName

  private val ADMIN_CREATE_USER_EMPTY_PASSWORD_VALUE = "-"
  private val adminCreateUserReferralCodeMap = ZConfig.getMapString("admin_create_user.referral_code", default = Map.empty)

  private val jwtIssuer = Utils.asciifolding(ZConfig.appName)
  private val jwtKeyId = ZConfig.getString("session.jwt.key_id")

  private val jwtAccessTokenExpireInSecond = ZConfig.getLong("session.jwt.access_token_expire_in_second")
  private val jwtAccessTokenInternalExpireInSecond = ZConfig.getLong("session.jwt.internal_access_token_expire_in_second", jwtAccessTokenExpireInSecond)
  private val jwtOwnerVerificationTokenExpireInSecond = ZConfig.getLong("session.jwt.owner_verification_token_expire_in_second", 10.minute.toSeconds)

  private val jwtRefreshTokenExpireInSecond = ZConfig.getLong("session.jwt.refresh_token_expire_in_second").second.toMillis
  private val jwtAlgo = SignatureAlgorithm.valueOf(ZConfig.getString("session.jwt.algo"))
  private lazy val jwtPrivateKey = {
    jwtAlgo match {
      case SignatureAlgorithm.HS256 => _getPrivateKeyHS256(ZConfig.getString("session.jwt.secret_key_hs256"))
      case SignatureAlgorithm.RS256 => _getPrivateKeyRS256(FileUtils.readFileToString(new File(ZConfig.getString("session.jwt.secret_key_rs256_path"))))
      case _ => throw UnsupportedException(s"Unsupported jwt key algo ${jwtAlgo.getValue}")
    }
  }
  private lazy val jwtPublicKey = {
    jwtAlgo match {
      case SignatureAlgorithm.RS256 => _getPublicRS256(FileUtils.readFileToString(new File(ZConfig.getString("session.jwt.public_key_rs256_path"))))
      case _ => throw UnsupportedException(s"Unsupported jwt key algo ${jwtAlgo.getValue}")
    }
  }

  private val deactivateUserCache = CacheBuilder.newBuilder()
    .expireAfterWrite(1, TimeUnit.HOURS)
    .maximumSize(5000).build[String, java.lang.Boolean]()

  override def registerByPhone(normPhone: String): Future[Unit] = Profiler(s"$clazz.registerByPhone") {
    createOrUpdatePhoneLock.withLock(normPhone) {
      for {
        _ <- profileService.getActiveUserByPhone(normPhone).map {
          case Some(user) if user.phone.contains(normPhone) => throw AlreadyExistPhoneException(normPhone, data = Map("full_name" -> user.fullName))
          case _ =>
        }
        _ <- verifyDataService.genAndSendCodeToPhone(normPhone, s"${RegisterChannelType.PHONE}-register")
      } yield {}
    }
  }


  override def registerByPhone(normPhone: String, deviceId: String, platform: Option[String]): Future[Unit] = Profiler(s"$clazz.registerPhone") {
    createOrUpdatePhoneLock.withLock(normPhone) {
      for {
        result <- profileService.getActiveUserByPhone(normPhone).flatMap {
          case Some(user) if user.phone.contains(normPhone) && user.getDeviceIds.contains(deviceId) =>
            Future.exception(AlreadyExistPhoneException(normPhone, data = Map("full_name" -> user.fullName)))
          case Some(user) if user.phone.contains(normPhone) && !user.statusIsActive =>
            Future.exception(AlreadyExistPhoneException(normPhone, data = Map("full_name" -> user.fullName)))
          case Some(user) if user.phone.contains(normPhone) =>
            verifyDataService.genAndSendCodeToPhone(normPhone, s"${RegisterChannelType.PHONE}-register-other-device-$deviceId", platform)
          case _ =>
            verifyDataService.genAndSendCodeToPhone(normPhone, s"${RegisterChannelType.PHONE}-register", platform)
        }
      } yield result
    }
  }

  override def registerByEmail(email: String, deviceId: String): Future[Unit] = Profiler(s"$clazz.registerByEmail") {
    val normalizedEmail = EmailHelper.normalize(email)
    createOrUpdateEmailLock.withLock(normalizedEmail) {
      for {
        result <- profileService.getActiveUserByEmail(email, withNormalize = true).flatMap {
          case Some(user) if !email.equalsIgnoreCase(user.email.get) =>
            Future.exception(AlreadyExistByNormalizedEmailException(email, data = Map("full_name" -> user.fullName, "normalized_email" -> normalizedEmail, "existed_email" -> user.email.get)))
          case Some(user) if user.getDeviceIds.contains(deviceId) =>
            Future.exception(AlreadyExistEmailException(email, data = Map("full_name" -> user.fullName)))
          case Some(user) if !user.statusIsActive =>
            Future.exception(AlreadyExistEmailException(email, data = Map("full_name" -> user.fullName)))
          case Some(user) =>
            verifyDataService.genAndSendCodeToEmail(email, s"${RegisterChannelType.EMAIL}-register-other-device-$deviceId", user.fullName, isForgot = false)
          case _ =>
            verifyDataService.genAndSendCodeToEmail(email, s"${RegisterChannelType.EMAIL}-register", None, isForgot = false)
        }
      } yield result
    }
  }

  override def registerByEmail(email: String): Future[Unit] = Profiler(s"$clazz.registerByEmail") {
    val normalizedEmail = EmailHelper.normalize(email)
    createOrUpdateEmailLock.withLock(normalizedEmail) {
      for {
        _ <- profileService.getActiveUserByEmail(email, withNormalize = true).flatMap {
          case Some(user) if !email.equalsIgnoreCase(user.email.get) =>
            Future.exception(AlreadyExistByNormalizedEmailException(email, data = Map("full_name" -> user.fullName, "normalized_email" -> normalizedEmail, "existed_email" -> user.email.get)))
          case Some(user) => Future.exception(AlreadyExistEmailException(email, data = Map("full_name" -> user.fullName)))
          case _ => Future.None
        }
        _ <- verifyDataService.genAndSendCodeToEmail(email, s"${RegisterChannelType.EMAIL}-register", None, isForgot = false)
      } yield {}
    }
  }


  override def verifyRegisterByPhone(normPhone: String, code: String): Future[String] = Profiler(s"$clazz.verifyRegisterByPhone") {
    for {
      _ <- verifyDataService.verifyCodeWithPhone(normPhone, s"${RegisterChannelType.PHONE}-register", code, deleteIfEqual = true)
      tokenPhone <- verifyDataService.genToken(normPhone, s"${RegisterChannelType.PHONE}-register")
    } yield tokenPhone
  }

  override def clearOtherSessions(event: CustomerNewSignedInEvent): Future[Unit] = Profiler(s"$clazz.clearOtherSessions") {

    caasService.clearOtherSessions(event.userId, event.timestamp, event.currentSession)

  }

  private def sendCustomerNewSignedInEvent(userId: String, currentSession: String, deviceId: String): Future[Unit] = Profiler(s"$clazz.sendCustomerNewSignedInEvent") {
    val event = CustomerNewSignedInEvent(
      userId = userId,
      timestamp = System.currentTimeMillis() - 1.second.toMillis,
      currentSession = currentSession,
      deviceId= deviceId.toSome
    )
    kafkaProducer.sendOnlyValue(customerNewSignedInTopic, JsonHelper.toJson(event)).unit
  }

  override def verifyRegisterByPhone(normPhone: String, code: String, deviceId: String): Future[VerifyRegisterResponse] = Profiler(s"$clazz.verifyRegisterByPhone") {
    for {
      result <- caasService.getActiveUserByPhoneWithPassState(normPhone).flatMap {
        case (Some(user), _) if user.phone.contains(normPhone) && user.getDeviceIds.contains(deviceId) =>
          Future.exception(AlreadyExistPhoneException(normPhone, data = Map("full_name" -> user.fullName)))
        case (Some(user), state) if user.phone.contains(normPhone) && (state == 0) =>
          verifyDataService.verifyCodeWithPhone(normPhone, s"${RegisterChannelType.PHONE}-register-other-device-$deviceId", code, deleteIfEqual = true)
            .flatMap(_ => verifyDataService.genToken(user.userId.get, "update-register-by-pnl"))
            .map(token => VerifyRegisterResponse(token = token.toSome, userProfile = CustomerDTO(
              userId = user.userId,
              fullName = user.fullName,
              gender = user.gender,
              birthday = user.birthday,
              referralCode = user.referralCode
            ).toSome))
        case (Some(user), _) if user.phone.contains(normPhone) =>
          for {
            _ <- verifyDataService.verifyCodeWithPhone(normPhone, s"${RegisterChannelType.PHONE}-register-other-device-$deviceId", code, deleteIfEqual = true)
            _ <- profileService.updateUser(user.userId.get, UpdateUserRequest(deviceId = deviceId.toSome))

            loginResult <- caasService.loginOnlyUsername(
              user.userId.get, Some(jwtRefreshTokenExpireInSecond),
              Map(JWT_CLAIM_REQUESTED_BY_F -> Constant.JWT_CLAIM_REQUIRED_BY_VALUE_USER)
            )

            result <- _getProfileAndBuildJwt(
              loginResult, user.toSome, jwtRequestedBy = Constant.JWT_CLAIM_REQUIRED_BY_VALUE_USER,
              Map(JWT_CLAIM_DEVICE_ID -> deviceId)
            )

            _ = sendCustomerNewSignedInEvent(user.userId.get, currentSession = result.getRefreshToken.get, deviceId=deviceId)

          } yield VerifyRegisterResponse(userAuth = result.toSome)
        case _ =>
          verifyDataService.verifyCodeWithPhone(normPhone, s"${RegisterChannelType.PHONE}-register", code, deleteIfEqual = true)
            .flatMap(_ => verifyDataService.genToken(normPhone, s"${RegisterChannelType.PHONE}-register"))
            .map(token => VerifyRegisterResponse(token = token.toSome))
      }
    } yield result
  }

  override def verifyRegisterByEmail(email: String, code: String): Future[String] = Profiler(s"$clazz.verifyRegisterByEmail") {
    for {
      _ <- verifyDataService.verifyCodeWithEmail(email, code, s"${RegisterChannelType.EMAIL}-register", delete = true)
      token <- verifyDataService.genToken(email, s"${RegisterChannelType.EMAIL}-register")
    } yield token
  }


  override def verifyRegisterByEmail(email: String, code: String, deviceId: String): Future[VerifyRegisterResponse] = Profiler(s"$clazz.verifyRegisterByEmail") {
    for {
      result <- caasService.getActiveUserByEmailWithPassState(email).flatMap {
        case (Some(user), _) if user.getDeviceIds.contains(deviceId) => Future.exception(AlreadyExistEmailException(email, data = Map("full_name" -> user.fullName)))
        case (Some(user), state) if (state == 0) =>
          verifyDataService.verifyCodeWithEmail(email, code, s"${RegisterChannelType.EMAIL}-register-other-device-$deviceId", delete = true)
            .flatMap(_ => verifyDataService.genToken(user.userId.get, "update-register-by-pnl"))
            .map(token => VerifyRegisterResponse(token = token.toSome, userProfile = CustomerDTO(
              userId = user.userId,
              fullName = user.fullName,
              gender = user.gender,
              birthday = user.birthday,
              referralCode = user.referralCode
            ).toSome))
        case (Some(user), _) =>
          for {
            _ <- verifyDataService.verifyCodeWithEmail(email, code, s"${RegisterChannelType.EMAIL}-register-other-device-$deviceId", delete = true)
            _ <- profileService.updateUser(user.userId.get, UpdateUserRequest(deviceId = deviceId.toSome))
            loginResult <- caasService.loginOnlyUsername(
              user.userId.get, Some(jwtRefreshTokenExpireInSecond),
              Map(JWT_CLAIM_REQUESTED_BY_F -> Constant.JWT_CLAIM_REQUIRED_BY_VALUE_USER)
            )
            result <- _getProfileAndBuildJwt(
              loginResult, user.toSome, Constant.JWT_CLAIM_REQUIRED_BY_VALUE_USER,
              Map(JWT_CLAIM_DEVICE_ID -> deviceId)
            )

            _ = sendCustomerNewSignedInEvent(user.userId.get, currentSession = result.getRefreshToken.get, deviceId=deviceId)

          } yield VerifyRegisterResponse(userAuth = result.toSome)

        case _ =>
          verifyDataService.verifyCodeWithEmail(email, code, s"${RegisterChannelType.EMAIL}-register", delete = true)
            .flatMap(_ => verifyDataService.genToken(email, s"${RegisterChannelType.EMAIL}-register"))
            .map(token => VerifyRegisterResponse(token = token.toSome))
      }
    } yield result
  }

  override def confirmRegisterByPhone(normPhone: String, tokenPhone: String, registerData: RegisterUserJwtData): Future[Customer] = Profiler(s"$clazz.confirmRegisterByPhone") {
    //    createOrUpdatePhoneLock.withLock(normPhone) {
    for {
      _ <- profileService.existActiveUserByPhone(normPhone).map(existUser => {
        if (existUser) throw AlreadyExistPhoneException(normPhone)
      })
      _ <- verifyDataService.verifyToken(normPhone, tokenPhone, s"${RegisterChannelType.PHONE}-register", delete = false)
      user <- {
        val username = SnowflakeDefault.nextId().toString
        val timestamp = System.currentTimeMillis()
        val user = Customer(
          userId = username.toSome,
          password = PasswordHelper.hashPasswordBCrypt(username, registerData.password).toSome,
          active = true.toSome,
          createdTime = Some(timestamp), updatedTime = Some(timestamp),
          createdBy = Some(USER_SELF), updatedBy = Some(USER_SELF),
          phone = normPhone.toSome, phoneVerified = 1.toSome,
          email = None, emailVerified = None,
          oauthGoogleId = None, oauthFacebookId = None, oauthAppleId = None,
          gender = registerData.gender, birthday = registerData.birthday,
          devices = Seq(DeviceInfo(registerData.deviceId, addedTime = Some(timestamp))).toSome,
          referralCode = registerData.referralCode
        ).setName(registerData.fullName, registerData.lastName, registerData.firstName)
        profileService.addUser(user)
      }
    } yield {
      user.omitSensitive()
    }
    //    }
  }

  override def confirmRegisterByEmail(email: String, token: String, registerData: RegisterUserJwtData): Future[Customer] = Profiler(s"$clazz.confirmRegisterByEmail") {
    //    createOrUpdatePhoneLock.withLock(email) {
    for {
      _ <- profileService.existActiveUserByEmail(email).map(existUser => {
        if (existUser) throw AlreadyExistEmailException(email)
      })
      _ <- verifyDataService.verifyToken(email, token, s"${RegisterChannelType.EMAIL}-register", delete = false)
      user <- {
        val username = SnowflakeDefault.nextId().toString
        val timestamp = System.currentTimeMillis()
        val user = Customer(
          userId = username.toSome,
          password = PasswordHelper.hashPasswordBCrypt(username, registerData.password).toSome,
          active = true.toSome,
          createdTime = Some(timestamp), updatedTime = Some(timestamp),
          createdBy = Some(USER_SELF), updatedBy = Some(USER_SELF),
          phone = None, phoneVerified = None,
          email = email.toSome, emailVerified = 1.toSome, normalizedEmail = EmailHelper.normalize(email).toSome,
          oauthGoogleId = None, oauthFacebookId = None, oauthAppleId = None,
          gender = registerData.gender, birthday = registerData.birthday,
          devices = Seq(DeviceInfo(registerData.deviceId, addedTime = Some(timestamp))).toSome,
          referralCode = registerData.referralCode,
        ).setName(registerData.fullName, registerData.lastName, registerData.firstName)
        profileService.addUser(user)
      }
    } yield user.omitSensitive()
    //    }
  }
  //region Login & Logout

  override def loginAfterRegisterSuccess(user: Customer, registerData: RegisterUserJwtData): Future[UserAuthProfile] = Profiler(s"$clazz.loginAfterRegisterSuccess") {
    for {
      loginResult <- caasService.loginOnlyUsername(
        user.userId.get, Some(jwtRefreshTokenExpireInSecond),
        Map(JWT_CLAIM_REQUESTED_BY_F -> Constant.JWT_CLAIM_REQUIRED_BY_VALUE_USER)
      )
      result <- _getProfileAndBuildJwt(
        loginResult, None, jwtRequestedBy = Constant.JWT_CLAIM_REQUIRED_BY_VALUE_USER,
        Map(JWT_CLAIM_DEVICE_ID -> registerData.deviceId)
      )

      _ = sendCustomerNewSignedInEvent(user.userId.get, currentSession = result.getRefreshToken.get, deviceId=registerData.deviceId)

    } yield result
  }

  private def _getProfileAndBuildJwt(loginResult: UserAuthResult, optUser: Option[Customer],
                                     jwtRequestedBy: String,
                                     additionalClaims: Map[String, Any] = Map.empty): Future[UserAuthProfile] = Profiler(s"$clazz._getProfileAndBuildJwt") {
    for {
      user <- optUser match {
        case Some(u) => profileService.get(u, rescanPnl = true)
        case _ => profileService.get(loginResult.userInfo.userId, rescanPnl = true)
      }
      jwtToken = _buildJwtToken(user, loginResult.ssid.toSome, jwtRequestedBy = jwtRequestedBy, additionalClaims)
    } yield {
      UserAuthProfile(jwt = jwtToken, loginResult.userInfo, user.toDto.toSome)
    }
  }

  private val maxLoginFailed = ZConfig.getInt("authen_service.max_login_failed", 5)
  private val lockLoginInMillis = ZConfig.getInt("authen_service.lock_login_in_millis", 28800000)
  private val resetNumLoginFailedAfterInMillis = ZConfig.getInt("authen_service.reset_num_login_failed_after_in_millis", 28800000)
  private val loginWrongPassMac: String = s"login_wrong_pass"

  def loginAndLockOnFailedAttempts(username: String, rawPassword: String): Future[UserAuthResult] = Profiler(s"$clazz.loginAndLockOnFailedAttempts") {
    for {
      _ <- verifyDataService.getLockTimeByEntity("user", username, loginWrongPassMac).map(lockTime => {
        if (lockTime.nonEmpty) throw InvalidCredentialException("user locked",
          data = Map("exceed_login_wrong_pass_quota" -> true, "locked_until_time" -> lockTime, "max_login_failed" -> maxLoginFailed))
      })

      loginResult <- {
        caasService.login(
          username, PasswordHelper.hashPasswordBCrypt(username, rawPassword), Some(jwtRefreshTokenExpireInSecond),
          Map(JWT_CLAIM_REQUESTED_BY_F -> Constant.JWT_CLAIM_REQUIRED_BY_VALUE_USER)
        )
        //          .rescue {
        //            case _: InvalidCredentialException if optOldSalt.isDefined =>
        //              caasService.login(
        //                  username, PasswordHelper.hashPassword(username, rawPassword, optOldSalt.get), Some(jwtRefreshTokenExpireInSecond),
        //                  Map(JWT_CLAIM_REQUESTED_BY_F -> Constant.JWT_CLAIM_REQUIRED_BY_VALUE_USER)
        //                )
        //                .onSuccess(_ => caasService.resetPasswordUser(username, PasswordHelper.hashPasswordBCrypt(username, rawPassword)))
        //          }
      }
        .transform {
          case Return(r) => async {
            verifyDataService.deleteQuotaUserMac(username, loginWrongPassMac)
            r
          }
          case Throw(e: InvalidCredentialException) =>
            verifyDataService.incrQuotaUserMac(username, loginWrongPassMac, resetNumLoginFailedAfterInMillis.millis)
              .flatMap(numQuota => {
                if (numQuota < maxLoginFailed) Future.exception(e.copy(data = Map("num_login_failed" -> numQuota, "max_login_failed" -> maxLoginFailed, "lock_login_in_millis" -> lockLoginInMillis)))
                else {
                  verifyDataService.buildEntityWithLockTime("user", username, loginWrongPassMac, lockLoginInMillis.millis)
                    .onSuccess(_ => verifyDataService.deleteQuotaUserMac(username, loginWrongPassMac))
                    .flatMap(lockTime => {
                      Future.exception(e.copy(data = Map("exceed_login_wrong_pass_quota" -> true, "locked_until_time" -> lockTime, "max_login_failed" -> maxLoginFailed)))
                    })
                }
              })
          case Throw(e) => Future.exception(e)
        }

    } yield loginResult
  }

  override def loginPhone(request: LoginByPhoneRequest): Future[UserAuthProfile] = Profiler(s"$clazz.loginPhone") {
    val normPhone = PhoneUtils.normalizePhone(request.normPhone)
    for {
      user <- profileService.getActiveUserByPhone(normPhone).map(_.getOrElse(throw NotExistUserByPhone(normPhone)))
      username = user.userId.get

      loginResult <- loginAndLockOnFailedAttempts(username, request.password)
      _ <- {
        if (user.getDeviceIds.contains(request.deviceId)) Future.Unit
        else {
          request.code match {
            case Some(code) =>
              quotaService.calcQuotaPhoneVerifyCode(request, "user-login-other-device-verify-code", 5.minutes, 5) {
                verifyDataService.verifyCodeWithPhone(normPhone, s"login_other_device_${username}_${request.deviceId}", code, deleteIfEqual = true)
                  .flatMap(_ => profileService.updateUser(username, UpdateUserRequest(deviceId = request.deviceId.toSome)))
              }
            case _ =>
              quotaService.calcQuotaPhoneSendCode(CalcQuotaPhoneRequest(normPhone, lockInDuration = 30.minutes.toSome)) {
                verifyDataService.genAndSendCodeToPhone(normPhone, s"login_other_device_${username}_${request.deviceId}", request.platform)
              }.flatMap(_ => Future.exception(InvalidCredentialException(s"Need verify phone code when login with other device", data = Map("need_verify_other_device" -> true))))
          }
        }
      }
      result <- _getProfileAndBuildJwt(
        loginResult, user.toSome, jwtRequestedBy = Constant.JWT_CLAIM_REQUIRED_BY_VALUE_USER,
        additionalClaims = Map(JWT_CLAIM_DEVICE_ID -> request.deviceId)
      )

      _ = sendCustomerNewSignedInEvent(result.getUsername, currentSession = result.getRefreshToken.get, deviceId=request.deviceId)

    } yield result

  }

  override def loginById(customerId: Long, jwtRequestedBy: String, additionalClaims: Map[String, Any]): Future[UserAuthProfile] = Profiler(s"$clazz.loginById") {
    val username = String.valueOf(customerId)
    for {

      loginResult <- caasService.loginOnlyUsername(
        username, Some(jwtRefreshTokenExpireInSecond),
        Map(JWT_CLAIM_REQUESTED_BY_F -> jwtRequestedBy)
      )
      result <- _getProfileAndBuildJwt(loginResult, None, jwtRequestedBy = jwtRequestedBy, additionalClaims)

      //      _ = caasService.clearOtherSessions(result.getUsername, currentSession = result.getRefreshToken.get)

    } yield result

  }

  override def loginEmail(request: LoginByEmailRequest): Future[UserAuthProfile] = Profiler(s"$clazz.loginEmail") {
    val email = request.email
    for {
      user <- profileService.getActiveUserByEmail(email).map(_.getOrElse(throw NotExistUserByEmail(email)))
      username = user.userId.get

      loginResult <- loginAndLockOnFailedAttempts(username, request.password)
      _ <- {
        if (user.getDeviceIds.contains(request.deviceId)) Future.Unit
        else {
          request.code match {
            case Some(code) =>
              quotaService.calcQuotaEmailVerifyCode(request, "user-login-other-device-send-code", 5.minutes, 5) {
                verifyDataService.verifyCodeWithEmail(email, code, s"login_other_device_${username}_${request.deviceId}", delete = true)
                  .flatMap(_ => profileService.updateUser(username, UpdateUserRequest(deviceId = request.deviceId.toSome)))
              }
            case _ =>
              quotaService.calcQuotaEmailSendCode(CalcQuotaEmailRequest(email, lockInDuration = 30.minutes.toSome)) {
                verifyDataService.genAndSendCodeToEmail(email, s"login_other_device_${username}_${request.deviceId}", user.fullName, isForgot = false)
              }.flatMap(_ => Future.exception(InvalidCredentialException(s"Need verify phone code when login with other device", data = Map("need_verify_other_device" -> true))))
          }
        }
      }
      result <- _getProfileAndBuildJwt(
        loginResult, user.toSome, jwtRequestedBy = Constant.JWT_CLAIM_REQUIRED_BY_VALUE_USER,
        additionalClaims = Map(JWT_CLAIM_DEVICE_ID -> request.deviceId)
      )

      _ = sendCustomerNewSignedInEvent(result.getUsername, currentSession = result.getRefreshToken.get, deviceId=request.deviceId)

    } yield result
  }

  override def loginWithOAuth(request: LoginOAuthReq): Future[UserAuthProfile] = Profiler(s"$clazz.loginWithOAuth") {
    for {
      loginResult <- request match {
        case req: LoginOAuthRequest => caasService.loginWithOAuth(req.oauthInfo.asOAuthInfo, Some(jwtRefreshTokenExpireInSecond), None, Map.empty[String, String])
        case req: LoginOAuthByUsernameRequest => caasService.loginOnlyUsername(req.username, Some(jwtRefreshTokenExpireInSecond), Map.empty[String, String])
        case _ => Future.exception(InvalidCredentialException("Login info wrong"))
      }
      result <- _getProfileAndBuildJwt(loginResult, None, Constant.JWT_CLAIM_REQUIRED_BY_VALUE_EMPTY)
    } yield result
  }

  override def logout(refreshToken: String): Future[Unit] = Profiler(s"$clazz.logout") {
    caasService.logout(refreshToken)
  }

  //endregion

  override def getUsernameFromAccessToken(accessToken: String): String = Profiler(s"$clazz.getUsernameFromAccessToken") {
    _parseJwtToken(accessToken)._1
  }

  override def parseAccessToken(accessToken: String, uncheckExpireTime: Boolean): (String, Map[String, Any]) = Profiler(s"$clazz.parseAccessToken") {
    val result = _parseJwtToken(accessToken)

    (result._1, JsonHelper.fromNode[Map[String, Any]](result._2))
  }

  override def refreshAccessToken(refreshToken: String): Future[UserAuthProfile] = Profiler(s"$clazz.refreshAccessToken") {
    for {
      userInfo <- caasService.getUserBySession(refreshToken).map(_.getOrElse(throw RefreshTokenInvalid()))
      user <- profileService.get(userInfo.userId, rescanPnl = true)
      jwtToken = _buildJwtToken(user, None, jwtRequestedBy = Constant.JWT_CLAIM_REQUIRED_BY_VALUE_EMPTY)
    } yield {
      UserAuthProfile(
        jwt = jwtToken,
        userInfo = userInfo,
        userProfile = user.toDto.toSome
      )
    }
  }

  override def forgotPasswordByPhone(normPhone: String, platform: Option[String]): Future[Unit] = Profiler(s"$clazz.forgotPasswordByPhone") {
    for {
      username <- profileService.getActiveUsernameByPhone(normPhone).map(_.getOrElse(throw NotExistUserByPhone(normPhone)))
      _ <- verifyDataService.genAndSendCodeToPhone(normPhone, s"${username}_forgot_password", platform)
    } yield {}
  }

  override def forgotPasswordByEmail(email: String): Future[Unit] = Profiler(s"$clazz.forgotPasswordByEmail") {
    for {
      user <- profileService.getActiveUserByEmail(email).map(_.getOrElse(throw NotExistUserByEmail(email)))
      _ <- verifyDataService.genAndSendCodeToEmail(email, s"${user.userId.get}_forgot_password", user.fullName, isForgot = true)
    } yield {}
  }

  override def verifyForgotPasswordByPhone(normPhone: String, code: String): Future[String] = Profiler(s"$clazz.verifyForgotPasswordByPhone") {
    for {
      username <- profileService.getActiveUsernameByPhone(normPhone).map(_.getOrElse(throw NotExistUserByPhone(normPhone)))
      _ <- verifyDataService.verifyCodeWithPhone(normPhone, s"${username}_forgot_password", code, deleteIfEqual = true)
      tokenPhone <- verifyDataService.genToken(normPhone, s"${username}_forgot_password")
    } yield tokenPhone
  }

  override def verifyForgotPasswordByEmail(email: String, code: String): Future[String] = Profiler(s"$clazz.verifyForgotPasswordByEmail") {
    for {
      username <- profileService.getActiveUsernameByEmail(email).map(_.getOrElse(throw NotExistUserByEmail(email)))
      _ <- verifyDataService.verifyCodeWithEmail(email, code, s"${username}_forgot_password", delete = true)
      tokenPhone <- verifyDataService.genToken(email, s"${username}_forgot_password")
    } yield tokenPhone
  }

  override def confirmForgotPasswordByPhone(normPhone: String, token: String,
                                            newPassword: String, deviceId: String): Future[Unit] = Profiler(s"$clazz.confirmForgotPasswordByPhone") {
    for {
      username <- profileService.getActiveUsernameByPhone(normPhone).map(_.getOrElse(throw NotExistUserByPhone(normPhone)))
      _ <- verifyDataService.verifyToken(normPhone, token, s"${username}_forgot_password", delete = true)
      _ <- caasService.resetPasswordUser(username, PasswordHelper.hashPasswordBCrypt(username, newPassword)).flatMap(_ => {
          profileService.updateUser(username, UpdateUserRequest(deviceId = deviceId.toSome))
        })
        .onSuccess(_ => verifyDataService.deleteLockTimeByEntity("user", username, loginWrongPassMac))
    } yield {}
  }

  override def confirmForgotPasswordByEmail(email: String, token: String,
                                            newPassword: String, deviceId: String): Future[Unit] = Profiler(s"$clazz.confirmForgotPasswordByEmail") {
    for {
      username <- profileService.getActiveUsernameByEmail(email).map(_.getOrElse(throw NotExistUserByPhone(email)))
      _ <- verifyDataService.verifyToken(email, token, s"${username}_forgot_password", delete = true)
      _ <- caasService.resetPasswordUser(username, PasswordHelper.hashPasswordBCrypt(username, newPassword)).flatMap(_ => {
          profileService.updateUser(username, UpdateUserRequest(deviceId = deviceId.toSome))
        })
        .onSuccess(_ => verifyDataService.deleteLockTimeByEntity("user", username, loginWrongPassMac))
    } yield {}
  }

  //endregion

  //region JWT Token

  private def _buildJwtTokenHS256(user: Customer, optRefreshToken: Option[String], jwtRequestedBy: String): JwtToken = {

    import io.jsonwebtoken.Jwts
    import io.jsonwebtoken.impl.DefaultJwsHeader

    val expireIn = if (jwtRequestedBy == Constant.JWT_CLAIM_REQUIRED_BY_VALUE_INTERNAL)
      jwtAccessTokenInternalExpireInSecond
    else jwtAccessTokenExpireInSecond

    /**
     * issuer — Software organization who issues the token.
     * subject — Intended user of the token.
     * audience — Basically identity of the intended recipient of the token.
     * expiresIn — Expiration time after which the token will be invalid.
     * algorithm — Encryption algorithm to be used to protect the token.
     */
    val jwtBuilder = Jwts.builder()
      .setClaims(_buildJwtClaim(user, jwtRequestedBy = jwtRequestedBy, expireInSecond = expireIn))
      .setHeader(new DefaultJwsHeader().setAlgorithm(jwtAlgo.getValue).setKeyId(jwtKeyId))
    jwtBuilder.signWith(jwtPrivateKey, jwtAlgo)

    val accessToken = jwtBuilder.compact()

    JwtToken(
      token = accessToken,
      refreshToken = optRefreshToken,
      expireIn = expireIn
    )
  }

  def _buildJwtToken(user: Customer, optRefreshToken: Option[String], jwtRequestedBy: String, additionalClaims: Map[String, Any] = Map.empty): JwtToken = Profiler(s"$clazz._buildJwtToken") {

    import io.jsonwebtoken.Jwts
    import io.jsonwebtoken.impl.DefaultJwsHeader


    val expireIn = if (jwtRequestedBy == Constant.JWT_CLAIM_REQUIRED_BY_VALUE_INTERNAL)
      jwtAccessTokenInternalExpireInSecond
    else jwtAccessTokenExpireInSecond

    /**
     * issuer — Software organization who issues the token.
     * subject — Intended user of the token.
     * audience — Basically identity of the intended recipient of the token.
     * expiresIn — Expiration time after which the token will be invalid.
     * algorithm — Encryption algorithm to be used to protect the token.
     */
    val jwtBuilder = Jwts.builder()
      .setClaims(_buildJwtClaim(user, jwtRequestedBy = jwtRequestedBy, expireInSecond = expireIn, additionalClaims = additionalClaims))
      .setHeader(new DefaultJwsHeader().setAlgorithm(jwtAlgo.getValue).setKeyId(jwtKeyId))
    jwtBuilder.signWith(jwtPrivateKey, jwtAlgo)

    val accessToken = jwtBuilder.compact()

    JwtToken(
      token = accessToken,
      refreshToken = optRefreshToken,
      expireIn = expireIn
    )
  }

  def _buildOnlyJwtToken(userId: String, mapData: Map[String, AnyRef]): String = Profiler(s"$clazz._buildOnlyJwtToken") {

    import io.jsonwebtoken.Jwts
    import io.jsonwebtoken.impl.DefaultJwsHeader

    /**
     * issuer — Software organization who issues the token.
     * subject — Intended user of the token.
     * audience — Basically identity of the intended recipient of the token.
     * expiresIn — Expiration time after which the token will be invalid.
     * algorithm — Encryption algorithm to be used to protect the token.
     */
    val jwtBuilder = Jwts.builder()
      .setClaims(_buildJwtClaim(userId, jwtOwnerVerificationTokenExpireInSecond, mapData))
      .setHeader(new DefaultJwsHeader().setAlgorithm(jwtAlgo.getValue).setKeyId(jwtKeyId))
    jwtBuilder.signWith(jwtPrivateKey, jwtAlgo)

    jwtBuilder.compact()

  }

  private def _buildJwtClaim(user: Customer, jwtRequestedBy: String,
                             expireInSecond: Long, additionalClaims: Map[String, Any] = Map.empty): Claims = {
    val timeNow = System.currentTimeMillis()
    val claim = new DefaultClaims()
    additionalClaims.foreach(pair => claim.put(pair._1, pair._2))

    claim.setIssuer(jwtIssuer)
      .setId(user.userId.get)
      .setSubject(user.userId.get)
      .setIssuedAt(new Date(timeNow))
      .setExpiration(new Date(timeNow + expireInSecond * 1000L))

    claim.put(JWT_CLAIM_REQUESTED_BY_F, jwtRequestedBy)

    user.createdTime.foreach(createdTime => {
      claim.put(JWT_CLAIM_CREATED_AT_F, long2Long(createdTime))
    })

    user.email.foreach(email => {
      claim.put(JWT_CLAIM_EMAIL_F, email)
      claim.put(JWT_CLAIM_EMAIL_VERIFIED_F, int2Integer(user.emailVerified.getOrElse(0)))
    })
    claim.put(JWT_CLAIM_FULL_NAME_F, user.fullName.getOrElse(""))
    user.phone.foreach(phone => {
      claim.put(JWT_CLAIM_PHONE_F, phone)
      claim.put(JWT_CLAIM_PHONE_VERIFIED_F, int2Integer(user.phoneVerified.getOrElse(0)))
    })
    user.displaySetting.flatMap(_.language)
      .foreach(language => claim.put("lang", language))

    claim
  }

  private def _buildJwtClaim(userId: String, tokenExpireInSecond: Long, mapData: Map[String, AnyRef]): Claims = {
    val timeNow = System.currentTimeMillis()

    val claim = new DefaultClaims()
      .setIssuer(jwtIssuer)
      .setId(userId)
      .setSubject(userId)
      .setIssuedAt(new Date(timeNow))
      .setExpiration(new Date(timeNow + tokenExpireInSecond * 1000L))

    mapData.foreach(pair => claim.put(pair._1, pair._2))

    claim
  }

  private def _parseJwtToken(accessToken: String, uncheckExpireTime: Boolean = false): (String, ObjectNode) = Profiler(s"$clazz._parseJwtToken") {
    val result = jwtAlgo match {
      case SignatureAlgorithm.HS256 => _parseJwtTokenHS256(accessToken, uncheckExpireTime)
      case SignatureAlgorithm.RS256 => _parseJwtTokenRS256(accessToken, uncheckExpireTime)
      case _ => throw UnsupportedException(s"Unsupported jwt key algo ${jwtAlgo.getValue}")
    }

    if (deactivateUserCache.getIfPresent(result._1) != null) throw AccessTokenInvalid()

    result
  }

  private def _parseJwtTokenHS256(accessToken: String, uncheckExpireTime: Boolean = false): (String, ObjectNode) = {
    val splitValues = {
      val split = accessToken.split("\\.")
      if (split.size != 3) throw AccessTokenInvalid()
      split
    }
    val tokenWithoutSignature = s"${splitValues(0)}.${splitValues(1)}"
    val signature = splitValues(2)

    val headerNode = JsonHelper.fromJson[ObjectNode](Utils.base64Decode(splitValues(0)))
    val payloadNode = Try {
      JsonHelper.fromJson[ObjectNode](Utils.base64Decode(splitValues(1)))
    }.getOrElse(JsonHelper.fromJson[ObjectNode](new String(Base64.getUrlDecoder.decode(splitValues(1)), "utf-8")))

    val validator = new DefaultJwtSignatureValidator(SignatureAlgorithm.HS256, jwtPrivateKey, Decoders.BASE64URL)
    if (!validator.isValid(tokenWithoutSignature, signature)) throw AccessTokenInvalid()
    //    val appId = payloadNode.path("iss").asText()
    val privateKeyId = headerNode.path("kid").asText()
    if (privateKeyId != jwtKeyId) throw AccessTokenInvalid()

    val exp = payloadNode.path("exp").asLong() * 1000L
    if (uncheckExpireTime) {
      if (exp + 30.minute.toMillis < System.currentTimeMillis()) throw AccessTokenInvalid()
    } else if (exp < System.currentTimeMillis()) throw AccessTokenInvalid()

    val username = payloadNode.path("sub").asText()
    (username, payloadNode)
  }

  def _parseJwtTokenRS256(accessToken: String, uncheckExpireTime: Boolean = false): (String, ObjectNode) = {
    val splitValues = {
      val split = accessToken.split("\\.")
      if (split.size != 3) throw AccessTokenInvalid()
      split
    }
    val tokenWithoutSignature = s"${splitValues(0)}.${splitValues(1)}"
    val signature = splitValues(2)

    val headerNode = Try {
      JsonHelper.fromJson[ObjectNode](Utils.base64Decode(splitValues(0)))
    }.toOption.getOrElse(throw AccessTokenInvalid())

    val payloadNode = Try {
      JsonHelper.fromJson[ObjectNode](Utils.base64Decode(splitValues(1)))
    }.toOption.getOrElse(JsonHelper.fromJson[ObjectNode](new String(Base64.getUrlDecoder.decode(splitValues(1)), "utf-8")))

    val validator = new DefaultJwtSignatureValidator(SignatureAlgorithm.RS256, jwtPublicKey, Decoders.BASE64URL)
    if (!validator.isValid(tokenWithoutSignature, signature)) throw AccessTokenInvalid()
    //    val appId = payloadNode.path("iss").asText()
    val privateKeyId = headerNode.path("kid").asText()
    if (privateKeyId != jwtKeyId) throw AccessTokenInvalid()

    val exp = payloadNode.path("exp").asLong() * 1000L
    if (uncheckExpireTime) {
      if (exp + 30.minute.toMillis < System.currentTimeMillis()) throw AccessTokenInvalid()
    } else if (exp < System.currentTimeMillis()) throw AccessTokenInvalid()

    val username = payloadNode.path("sub").asText()
    (username, payloadNode)
  }

  private def _getPrivateKeyHS256(secretKeyAsString: String): Key = {
    new SecretKeySpec(Base64.getDecoder.decode(secretKeyAsString), "HmacSHA256")
  }

  def _getPrivateKeyRS256(privateKeyAsString: String): RSAPrivateKey = {
    val privateKeyOnlyValue = privateKeyAsString
      //      .replace("-----BEGIN PRIVATE KEY-----", "")
      .replace("-----BEGIN PRIVATE KEY-----", "")
      //      .replace("-----END PRIVATE KEY-----", "")
      .replace("-----END PRIVATE KEY-----", "")
      .replaceAll(System.lineSeparator, "")
      .trim
    val pkcs8EncodedKeySpec = new PKCS8EncodedKeySpec(Utils.base64DecodeToBytes(privateKeyOnlyValue))
    KeyFactory.getInstance("RSA")
      .generatePrivate(pkcs8EncodedKeySpec)
      .asInstanceOf[RSAPrivateKey]
  }

  def _getPublicRS256(publicKeyAsString: String): RSAPublicKey = {
    val publicKeyOnlyValue = publicKeyAsString
      //      .replace("-----BEGIN PRIVATE KEY-----", "")
      .replace("-----BEGIN PUBLIC KEY-----", "")
      //      .replace("-----END PRIVATE KEY-----", "")
      .replace("-----END PUBLIC KEY-----", "")
      .replaceAll(System.lineSeparator, "")
      .trim
    val x509EncodedKeySpec = new X509EncodedKeySpec(Utils.base64DecodeToBytes(publicKeyOnlyValue))
    KeyFactory.getInstance("RSA")
      .generatePublic(x509EncodedKeySpec)
      .asInstanceOf[RSAPublicKey]
  }

  //endregion

  override def deactivateUser(username: String, passHashed: String): Future[Unit] = Profiler(s"$clazz.deactivateUser") {
    for {
      _ <- caasService.isPasswordUser(username, passHashed)
      _ <- profileService.deleteUser(username)
      _ = caasService.logoutAll(username)
      _ = {
        deactivateUserCache.put(username, true)
      }
    } yield {}
  }

  override def validateOwner(request: ValidateOwnerRequest): Future[String] = Profiler(s"$clazz.validateOwner") {
    for {
      customer <- profileService.get(request.username)
      userId = customer.userId.get
      _ = {
        val isAuthed = request.jwtRequestedBy.contains(Constant.JWT_CLAIM_REQUIRED_BY_VALUE_INTERNAL)

        if (!isAuthed && !customer.getDeviceIds.contains(request.deviceId)) throw InvalidCredentialException("Invalid device id")
      }
      _ <- caasService.isPasswordUser(userId, request.passwordHashed).rescue {
        case _: InvalidCredentialException => Future.exception(InvalidCredentialException("Invalid credential"))
      }

      result = _buildOnlyJwtToken(userId, request.data)

    } yield result

  }

  override def validateAuth(request: InternalValidateAuthRequest): Future[Boolean] = Profiler(s"$clazz.validateAuth") {
    for {
      customer <- profileService.get(request.username)
      userId = customer.userId.get
      //      _ = {
      //        val isAuthed = request.jwtRequestedBy.contains(Constant.JWT_CLAIM_REQUIRED_BY_VALUE_INTERNAL)
      //
      //        if (!isAuthed && !customer.getDeviceIds.contains(request.deviceId)) throw InvalidCredentialException("Invalid device id")
      //      }
      _ <- caasService.isPasswordUser(userId, request.passwordHashed).rescue {
        case _: InvalidCredentialException => Future.exception(InvalidCredentialException("Invalid credential"))
      }

      //      result = _buildOnlyJwtToken(userId, request.data)

    } yield true

  }

  // region Register by Admin

  private def checkAvailableForRegisterByAdmin(data: RegisterByAdminData): Future[(Option[String], Option[String])] = {
    for {
      (availablePhone, recheckPhoneUser) <- if (data.phone.exists(_.nonEmpty)) {
        val normPhone = data.optNormPhone().get
        createOrUpdatePhoneLock.withLock(normPhone) {
          for {
            result <- profileService.getActiveUserByPhone(normPhone).flatMap {
              // Không throw ngay vì phone gửi qua chưa verify mà email đã verify nên ưu tiên đăng ký bằng email nếu email chưa tồn tại
              case Some(user) if data.emailVerified && !data.phoneVerified => Future.value((normPhone.toSome, user.toSome))
              case Some(user) => throw AlreadyExistUserForInternalException(user.userId.get, data = Map("full_name" -> user.fullName, "exist_by_phone" -> true))
              case _ => Future.value((data.optNormPhone(), None))
            }
          } yield result
        }
      } else Future.value((None, None))

      (availableEmail, recheckEmailUser) <- if (data.email.exists(_.nonEmpty)) {
        val email = data.optQuotaEmail().get
        createOrUpdateEmailLock.withLock(email) {
          for {
            result <- profileService.getActiveUserByEmail(email, withNormalize = true).flatMap {
              // Không throw ngay vì email gửi qua chưa verify mà phone đã verify nên ưu tiên đăng ký bằng phone nếu phone chưa tồn tại
              case Some(user) if data.phoneVerified && !data.emailVerified => Future.value((email.toSome, user.toSome))
              case Some(user) => throw AlreadyExistUserForInternalException(user.userId.get, data = Map("full_name" -> user.fullName, "exist_by_email" -> true))
              case _ => Future.value((data.optQuotaEmail(), None))
            }
          } yield result
        }
      } else Future.value((None, None))

      _ <- recheckPhoneUser match {
        case Some(user) if availableEmail.isEmpty =>
          Future.exception(AlreadyExistUserForInternalException(user.userId.get, data = Map("full_name" -> user.fullName, "exist_by_phone" -> true)))
        case _ => Future.Unit
      }

      _ <- recheckEmailUser match {
        case Some(user) if availablePhone.isEmpty =>
          Future.exception(AlreadyExistUserForInternalException(user.userId.get, data = Map("full_name" -> user.fullName, "exist_by_email" -> true)))
        case _ => Future.Unit
      }
    } yield (availablePhone, availableEmail)
  }

  override def registerByAdmin(request: Request, data: RegisterByAdminData): Future[RegisterByAdminResponseDTO] = Profiler(s"$clazz.registerByAdmin") {
    for {
      (availablePhone, availableEmail) <- checkAvailableForRegisterByAdmin(data)

      isSentPhone <- availablePhone match {
        case Some(normPhone) if !data.phoneVerified && !(availableEmail.isDefined && data.emailVerified) =>
          quotaService.calcQuotaPhoneSendCode(request, CalcQuotaPhoneRequest(normPhone, lockInDuration = 30.minutes.toSome), checkQuotaCountry = true, checkQuotaIP = true) {
            verifyDataService.genAndSendCodeToPhone(normPhone, s"${RegisterChannelType.PHONE}-${data.pnl}-register")
          }.map(_ => true)
        case _ => Future.False
      }

      isSentEmail <- if (!isSentPhone) {
        availableEmail match {
          case Some(email) if !data.emailVerified && !(availablePhone.isDefined && data.phoneVerified) =>
            quotaService.calcQuotaEmailSendCode(request, CalcQuotaEmailRequest(email, lockInDuration = 30.minutes.toSome), checkQuotaCountry = true, checkQuotaIP = true) {
              verifyDataService.genAndSendCodeToEmail(email, s"${RegisterChannelType.EMAIL}-${data.pnl}-register", None, isForgot = false)
            }.map(_ => true)
          case _ => Future.False
        }
      } else Future.False

      result <- if (isSentPhone || isSentEmail) {
        verifyDataService.genPnlRequestToken(data)
          .map(_ => RegisterByAdminOtpResponse(
            expiredInSeconds = verifyDataService.getVerifyCodeExpireTimeInSecond,
            otpChannel = if (isSentPhone) OtpChannel.PHONE.toString else OtpChannel.EMAIL.toString
          )).map(otp => RegisterByAdminResponseDTO(otp = otp.toSome))
      } else {
        createUserByAdmin(data, if (data.phoneVerified) RegisterChannelType.PHONE else RegisterChannelType.EMAIL)
          .map(user => RegisterByAdminResponseDTO(customer = user.toSome))
      }

    } yield result
  }

  override def confirmRegisterByAdmin(data: RegisterByAdminData, registerChannelType: RegisterChannelType): Future[RegisterByAdminResponseDTO] = Profiler(s"$clazz.confirmRegisterByAdmin") {
    verifyDataService.verifyAdminRequestToken(data) {
      val code = data.otp.getOrElse(throw InvalidParamException("invalid_param", "otp: field is required"))
      val otpChannel = data.otpChannel.getOrElse(throw InvalidParamException("invalid_param", "otp_channel: field is required"))
      for {
        _ <- OtpChannel.optByValue(otpChannel) match {
          case Some(value) if value == OtpChannel.PHONE && registerChannelType == RegisterChannelType.PHONE =>
            val normPhone = data.optNormPhone().get
            for {
              _ <- profileService.existActiveUserByPhone(normPhone).map {
                case true => throw AlreadyExistPhoneException(normPhone)
                case false => ()
              }
              _ <- verifyDataService.verifyCodeWithPhone(normPhone, s"${RegisterChannelType.PHONE}-${data.pnl}-register", code, deleteIfEqual = true)
            } yield {}
          case Some(value) if value == OtpChannel.EMAIL && registerChannelType == RegisterChannelType.EMAIL =>
            val email = data.optEmail().get
            for {
              _ <- profileService.existActiveUserByEmail(email).map {
                case true => throw AlreadyExistEmailException(email)
                case false => ()
              }
              _ <- verifyDataService.verifyCodeWithEmail(email, code, s"${RegisterChannelType.EMAIL}-${data.pnl}-register", delete = true)
            } yield {}
          case Some(value) => throw InvalidParamException("invalid_param", s"otp_channel: $value is not match with request before")
          case None => throw InvalidParamException("invalid_param", s"otp_channel: $otpChannel is invalid")
          case _ => throw InvalidParamException("invalid_param", s"otp_channel: $otpChannel is required")
        }
        user <- createUserByAdmin(data, registerChannelType)
          .map(user => RegisterByAdminResponseDTO(customer = user.toSome))
      } yield user
    }
  }

  private def createUserByAdmin(data: RegisterByAdminData, registerChannelType: RegisterChannelType): Future[RegisterByAdminCustomerResponse] = {
    val username = SnowflakeDefault.nextId().toString
    val timestamp = System.currentTimeMillis()
    val referralCode = data.referralCode.orElse(adminCreateUserReferralCodeMap.get(data.pnl.map(_.toLowerCase).get))
    val (phone, phoneVerified, email, emailVerified, chanel) = if (registerChannelType == RegisterChannelType.PHONE) {
      if (data.emailVerified) {
        (data.optNormPhone(), 1.toSome, data.optEmail(), 1.toSome, "phone, email".toSome)
      } else {
        (data.optNormPhone(), 1.toSome, None, None, "phone".toSome)
      }
    } else {
      (None, None, data.optEmail(), 1.toSome, "email".toSome)
    }
    val user = Customer(
      userId = Some(username),
      password = Some(ADMIN_CREATE_USER_EMPTY_PASSWORD_VALUE),
      active = Some(true),
      createdTime = Some(timestamp), updatedTime = Some(timestamp),
      createdBy = Some(data.cmsUsername), updatedBy = Some(data.cmsUsername),
      phone = phone, phoneVerified = phoneVerified,
      email = email, emailVerified = emailVerified, normalizedEmail = email.map(EmailHelper.normalize),
      gender = data.gender, birthday = data.birthday,
      referralCode = referralCode
    ).setName(data.fullName, data.lastName, data.firstName)

    profileService.addUser(user)
      .onSuccess(c => profileService.get(c.userId.get, rescanPnl = true))
      .map(_ => user.toCustomerAdminRegisterResponseDto.copy(registerChannelType = chanel))
  }

  override def registerUserOnboard(request: RegisterUserOnboardJwtRequest): Future[Customer] = Profiler(s"$clazz.confirmUserOnboarding") {
    updateCustomerLock.withLock(request.userId) {
      for {
        customer <- profileService.get(request.userId, rescanPnl = false)
        _ <- verifyDataService.verifyToken(customer.userId.get, request.token, "update-register-by-pnl", delete = false)
        updatedCustomer <- profileService.internalUpdateUser(
          request.userId,
          Customer(
            userId = request.userId.toSome,
            password = PasswordHelper.hashPasswordBCrypt(request.userId, request.password).toSome,
            updatedTime = Some(System.currentTimeMillis()),
            updatedBy = Some(request.userId),
            gender = request.gender, birthday = request.birthday,
            devices = Seq(DeviceInfo(request.deviceId, addedTime = Some(System.currentTimeMillis()))).toSome
          ).setName(request.fullName, request.lastName, request.firstName)
        )
      } yield updatedCustomer
    }
  }

  // endregion

  //  override def forgotPasswordByEmail(email: String): Future[Unit] = {
  //    for {
  //      optUser <- profileService.getActiveUserByEmail(email).flatMap {
  //        case Some(user) => Future.value(Some(user))
  //        case _ => Future.None
  //      }
  //      _ <- optUser match {
  //        case Some(user) => verifyDataService.genAndSendCodeToEmail(email, user.username.get.toString, user.fullName)
  //        case _ => Future.exception(NotExistUserByEmail(email))
  //      }
  //    } yield {}
  //  }
  //
  //  override def verifyForgotPasswordByEmail(email: String, code: String, newPassword: String): Future[Unit] = {
  //    for {
  //      optUsername <- caasService.getActiveUsernameByEmail(email)
  //      _ <- optUsername match {
  //        case Some(username) =>
  //          verifyDataService.verifyCodeWithEmail(email, code, username, true)
  //            .flatMap(_ => caasService.resetPasswordUser(username, PasswordHelper.hashPassword(username, newPassword)))
  //            .flatMap(_ => profileService.updateEmail(username, email, true))
  //        case _ => Future.exception(InvalidVerifyEmailCodeException())
  //      }
  //    } yield {}
  //
  //  }
  //

  override def deactivateUserByAdmin(request: DeactivateByAdminRequest): Future[Unit] = Profiler(s"$clazz.deactivateUserByAdmin") {
    for {
      _ <- {
        if (request.pnl != Constant.JWT_CLAIM_PNL_VINCLUB) throw InvalidParamException("invalid_param", "pnl: only support vinclub") else Future.Unit
      }
      _ <- profileService.deleteUser(request.id,
        auditor = request.cmsUsername.toSome,
        manualRequestBy = request.requestBy.toSome,
        manualRequestReason = request.requestReason.toSome
      )
      _ = caasService.logoutAll(request.id)
      _ = {
        deactivateUserCache.put(request.id, true)
      }
    } yield {}
  }
}