package profile.service

import com.twitter.util.Future
import vn.vhm.common.client.ssdb.SSDBClient
import vn.vhm.common.domain.Implicits.{ImplicitAny, async}
import vn.vhm.common.domain.profiling.Profiler
import vn.vhm.common.exception.VhmCustomException
import vn.vhm.common.util.JsonHelper

/**
 * <AUTHOR> 22/01/2024 15:57
 */
trait MetadataSyncService {

  def getMetadata(key: String): Future[Map[String, Any]]

  def setMetadata(key: String, metadata: Map[String, Any]): Future[Boolean]

}

case class MetadataSyncServiceImpl(ssdbClient: SSDBClient, metadataKey: String) extends MetadataSyncService {

  private val clazz: String = getClass.getCanonicalName

  //  private val metadataCache = CacheBuilder.newBuilder()
  //    .expireAfterWrite(24, TimeUnit.HOURS)
  //    .maximumSize(100).build[String, Map[String, Any]]()

  override def getMetadata(key: String): Future[Map[String, Any]] = Profiler(s"$clazz.getMetadata") {
    async {
      val res = ssdbClient.exec(_.hget(metadataKey, key))
      if (res.notFound()) Map.empty[String, Any]
      else if (res.ok()) {
        val result = JsonHelper.fromJson[Map[String, Any]](res.asString())
        //        metadataCache.put(key, result)
        result
      }
      else throw VhmCustomException(s"Error when get metadata with key: $key: ${ssdbClient.getError(res)}")
    }
  }

  override def setMetadata(key: String, metadata: Map[String, Any]): Future[Boolean] = Profiler(s"$clazz.setMetadata") {
    async {
      //      metadataCache.put(key, metadata)
      ssdbClient.exec(_.hset(metadataKey, key, metadata.toJson())).ok()
    }
  }

}
