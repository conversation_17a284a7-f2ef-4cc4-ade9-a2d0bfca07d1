package profile.service.scheduler

import akka.actor.{Actor, ActorR<PERSON>, <PERSON>ps}
import akka.vhm.{AkkaImplicits, RAkkaFuture}
import com.twitter.concurrent.AsyncMutex
import com.twitter.inject.Logging
import com.twitter.util.{Future, Try}
import profile.domain.customer.{CustomerDAO, CustomerIdentityVerificationDAO}
import profile.service.scheduler.GeneralScheduler._
import profile.service.{DataSyncService, MetadataSyncService}
import vn.vhm.common.domain.Implicits.async
import vn.vhm.common.domain.OptionImplicits.OptionImplicitAny
import vn.vhm.common.domain.profiling.Profiler
import vn.vhm.common.exception.VhmException
import vn.vhm.common.repository.lock.AsyncLockManager
import vn.vhm.common.service.DebugServiceNotification
import vn.vhm.common.util.JsonHelper

import java.util.Calendar
import scala.concurrent.duration.DurationInt
import scala.util.Random
import scala.util.control.NonFatal

/**
 * <AUTHOR> 22/01/2024 16:49
 */

trait BaseScheduler extends Logging {

  def notifyIfReceiveError[T](_sender: ActorRef, req: GeneralScheduleReq, result: Try[T]): Unit = {
    result match {
      case com.twitter.util.Throw(e) =>
        val (errorVal, errorMessageVal) = e match {
          case e: VhmException => (e.error, e.getMessage)
          case _ => (e.getClass.getCanonicalName, e.getMessage)
        }
        error(s"Receive error of flow ${req.getClass.getCanonicalName}: $errorVal - $errorMessageVal")
        DebugServiceNotification.notify(
          s"Receive error of flow ${getClass.getCanonicalName}.${req.getClass.getCanonicalName}: $errorVal - $errorMessageVal"
        )
      case _ =>
    }
  }

  def notifyIfReceiveError[T](flowName: String, result: Try[T]): Unit = {
    result match {
      case com.twitter.util.Throw(e) =>
        val (errorVal, errorMessageVal) = e match {
          case e: VhmException => (e.error, e.getMessage)
          case _ => (e.getClass.getCanonicalName, e.getMessage)
        }
        error(s"Receive error of flow $flowName: $errorVal - $errorMessageVal")
        DebugServiceNotification.notifyTelegram(
          s"Receive error of flow ${getClass.getCanonicalName}.$flowName: $errorVal - $errorMessageVal"
        )
      case _ =>
    }
  }

  protected def diffToHour(hour: Int, minute: Int): Long = {
    val cal = Calendar.getInstance()
    cal.set(cal.get(Calendar.YEAR), cal.get(Calendar.MONTH), cal.get(Calendar.DAY_OF_MONTH), hour, minute, 0)
    cal.set(Calendar.MILLISECOND, 0)

    val currentTime = System.currentTimeMillis()
    if (cal.getTimeInMillis < currentTime) cal.add(Calendar.DAY_OF_MONTH, 1)

    cal.getTimeInMillis - currentTime
  }

  protected def calcNearestTime(seq: (Int, Int)*): Long = {
    seq.map(tple => diffToHour(tple._1, tple._2)).min
  }

  protected def withAsyncLock[T](asyncMutex: AsyncMutex)(fn: => Future[T]): Future[T] = {
    asyncMutex.acquire().flatMap(permit => {
      try {
        fn
      } catch {
        case NonFatal(e) =>
          permit.release()
          Future.exception(e)

        case e: Throwable =>
          permit.release()
          //            throw e
          Future.exception(e)
      }
    }.ensure {
      permit.release()
    })
  }

}

object GeneralScheduler {

  trait GeneralScheduleReq

  case class SyncCustomerRanking(intervalInSecond: Int) extends GeneralScheduleReq

  case class SyncCustomerCdpProfile(intervalInSecond: Int) extends GeneralScheduleReq

  case class ReInitRankingDay0(initFromUserId: Long, initToUserId: Long) extends GeneralScheduleReq

  case class ReScanRankingDay0(scanFromUserId: Long, scanToUserId: Long) extends GeneralScheduleReq

  case class DeactivateIdentifyVerificationReq(reqId: String, syncBeforeInMillis: Long) extends GeneralScheduleReq
}

case class GeneralScheduler(
                             metadataSyncService: MetadataSyncService,
                             dataSyncService: DataSyncService,
                             internalProcessLock: AsyncLockManager,
                             customerDAO: CustomerDAO,
                             identityVerificationDAO: CustomerIdentityVerificationDAO,
                             parent: ActorRef
                           ) extends Actor with RAkkaFuture with BaseScheduler {

  import AkkaImplicits.CommonActorImplicits
  import context._

  private val clazz: String = getClass.getCanonicalName

  private val activeChildActors = scala.collection.mutable.Map[String, ActorRef]()

  private def getOrCreateChildActor[T <: GeneralScheduleReq](req: T): ActorRef = {

    val requestClassName = req.getClass.getCanonicalName

    val childActor = activeChildActors.get(requestClassName) match {
      case Some(v) => v
      case _ =>
        synchronized {
          activeChildActors.get(requestClassName) match {
            case Some(v) => v
            case _ =>
              activeChildActors(requestClassName) = context.actorOf(
                Props(
                  classOf[GeneralScheduler], metadataSyncService, dataSyncService, internalProcessLock, customerDAO, identityVerificationDAO, self
                ),
                requestClassName
              )
              activeChildActors(requestClassName)
          }
        }
    }

    // Watch the child actor to detect termination
    context.watch(childActor)

    childActor

  }

  // Add this method to determine if this is a child actor
  private def isChildActor: Boolean = {
    parent != null
  }

  private def isParentActor: Boolean = {
    !isChildActor
  }

  override def receive: Receive = {

    case req: SyncCustomerRanking if isParentActor => Profiler(s"$clazz.SyncCustomerRanking-child=$isChildActor") {
      getOrCreateChildActor(req).forward(req)
    }
    case req: SyncCustomerRanking if isChildActor => Profiler(s"$clazz.SyncCustomerRanking-child=$isChildActor") {
      akkaAsync {
        val _sender = sender()
        syncCustomerRanking(req)
          .respond(result => notifyIfReceiveError(_sender, req, result))
      }.respond(result => {
        if (result.isReturn && result.get().exists(_ > 0L)) {
          system.scheduleOnce(10.milli) {
            self ! req
          }
        } else {
          val intervalInSecond = if (result.isReturn) req.intervalInSecond else Math.max(Random.nextInt(10) + 20, req.intervalInSecond * 2)
          system.scheduleOnce(intervalInSecond.second) {
            self ! req
          }
        }

      })
    }

    case req: SyncCustomerCdpProfile if isParentActor => Profiler(s"$clazz.SyncCustomerCdpProfile-child=$isChildActor") {
      getOrCreateChildActor(req).forward(req)
    }
    case req: SyncCustomerCdpProfile if isChildActor => Profiler(s"$clazz.SyncCustomerCdpProfile-child=$isChildActor") {
      akkaAsync {
        val _sender = sender()
        syncCustomerCdpProfile(req)
          .respond(result => notifyIfReceiveError(_sender, req, result))
      }.respond(result => {
        if (result.isReturn && result.get().exists(_ > 0L)) {
          system.scheduleOnce(10.milli) {
            self ! req
          }
        } else {
          val intervalInSecond = if (result.isReturn) req.intervalInSecond else Math.max(Random.nextInt(10) + 20, req.intervalInSecond * 2)
          system.scheduleOnce(intervalInSecond.second) {
            self ! req
          }
        }
      })
    }

    case req: ReInitRankingDay0 if isParentActor => Profiler(s"$clazz.ReInitRankingDay0-child=$isChildActor") {
      getOrCreateChildActor(req).forward(req)
    }
    case req: ReInitRankingDay0 if isChildActor => Profiler(s"$clazz.ReInitRankingDay0-child=$isChildActor") {
      akkaAsync {
        val _sender = sender()
        reInitRankingDay0(req)
          .respond(result => notifyIfReceiveError(_sender, req, result))
      }.respond(result => {
        if (result.isReturn && result.get().exists(_ > 0L)) {
          system.scheduleOnce(10.milli) {
            self ! req
          }
        }
      })
    }

    case req: ReScanRankingDay0 if isParentActor => Profiler(s"$clazz.ReScanRankingDay0-child=$isChildActor") {
      getOrCreateChildActor(req).forward(req)
    }
    case req: ReScanRankingDay0 if isChildActor => Profiler(s"$clazz.ReScanRankingDay0-child=$isChildActor") {
      akkaAsync {
        val _sender = sender()
        reScanRankingDay0(req)
          .respond(result => notifyIfReceiveError(_sender, req, result))
      }.respond(result => {
        if (result.isReturn && result.get().exists(_ > 0L)) {
          system.scheduleOnce(10.milli) {
            self ! req
          }
        }
      })
    }

    case req: DeactivateIdentifyVerificationReq if isParentActor => Profiler(s"$clazz.DeactivateIdentifyVerificationReq-child=$isChildActor") {
      getOrCreateChildActor(req).forward(req)
    }
    case req: DeactivateIdentifyVerificationReq if isChildActor => Profiler(s"$clazz.DeactivateIdentifyVerificationReq-child=$isChildActor") {
      akkaAsync {
        val _sender = sender()
        deactivateIdentifyVerificationWhenDeactivatedCustomer(req)
          .respond(result => notifyIfReceiveError(_sender, req, result))
          .respond(_ => info(s"[DeactivateIdentityVerificationScheduler] DoneReq-${req.reqId}"))
      }
    }

    case akka.actor.Terminated(childRef) =>
      Profiler(s"$clazz.TerminatedReq-child=$isChildActor") {
        // Remove terminated actor from map
        activeChildActors.find(_._2 == childRef).foreach { case (key, _) =>
          activeChildActors.remove(key)
        }
      }

  }

  private val customerRankingMetadataSyncKey = "vclub_customer_ranking_progress"
  private val lastSyncTimestampField = "last_sync_timestamp"
  private val lastSyncCustomerRankingId = "last_synced_customer_ranking_id"

  private def syncCustomerRanking(req: SyncCustomerRanking): Future[Option[Long]] = Profiler(s"$clazz.syncCustomerRanking") {
    internalProcessLock.tryLock(customerRankingMetadataSyncKey) {
      case true =>
        Profiler(s"$clazz.syncCustomerRanking - insideLock") {
          info("Start SyncCustomerRanking - insideLock")
          for {
            syncMetadata <- metadataSyncService.getMetadata(customerRankingMetadataSyncKey).map(metadata => {
              if (metadata.isEmpty) {
                Map(lastSyncTimestampField -> 0L, lastSyncCustomerRankingId -> "")
              } else metadata
            })

            result <- dataSyncService.singleSyncCustomerRanking(
              syncMetadata.get(lastSyncCustomerRankingId).map(_.toString).getOrElse(""),
              syncMetadata.get(lastSyncTimestampField).map(_.asInstanceOf[Long]).filter(_ > 0L).getOrElse(0L), None,
              (newSyncTimestamp, nextKey) => metadataSyncService.setMetadata(
                customerRankingMetadataSyncKey, Map(lastSyncTimestampField -> newSyncTimestamp, lastSyncCustomerRankingId -> nextKey)
              )
            )
          } yield result
        }.onFailure(ex => {
          error(s"Error when syncCustomerRanking: ${ex.getMessage}", ex)
        }).ensure(
          info("End SyncCustomerRanking - insideLock")
        )
      case _ => Profiler(s"$clazz.syncCustomerRanking - ignoreLock") {
        async {
          system.scheduleOnce(5.second) {
            self ! req
          }
          None
        }
      }
    }

  }

  private val customerCdpProfileMetadataSyncKey = "vclub_customer_cdp_profile"
  private val lastSyncCustomerCdpProfileId = "last_synced_customer_cdp_profile_id"

  private def syncCustomerCdpProfile(req: SyncCustomerCdpProfile): Future[Option[Long]] = Profiler(s"$clazz.syncCustomerCdpProfile") {
    internalProcessLock.tryLock(customerCdpProfileMetadataSyncKey) {
      case true =>
        Profiler(s"$clazz.syncCustomerCdpProfile - insideLock") {
          info("Start SyncCustomerCdpProfile - insideLock")
          for {
            syncMetadata <- metadataSyncService.getMetadata(customerCdpProfileMetadataSyncKey).map(metadata => {
              if (metadata.isEmpty) {
                Map(lastSyncTimestampField -> 0L, lastSyncCustomerCdpProfileId -> "")
              } else metadata
            })

            result <- dataSyncService.singleSyncCustomerCdpProfile(
              syncMetadata.get(lastSyncCustomerCdpProfileId).map(_.toString).getOrElse(""),
              syncMetadata.get(lastSyncTimestampField).map(_.asInstanceOf[Long]).filter(_ > 0L).getOrElse(0L), None,
              (newSyncTimestamp, nextKey) => metadataSyncService.setMetadata(
                customerCdpProfileMetadataSyncKey, Map(lastSyncTimestampField -> newSyncTimestamp, lastSyncCustomerCdpProfileId -> nextKey)
              )
            )
          } yield result
        }.onFailure(ex => {
          error(s"Error when syncCustomerCdpProfile: ${ex.getMessage}", ex)
        }).ensure(
          info("End SyncCustomerCdpProfile - insideLock")
        )
      case _ => Profiler(s"$clazz.syncCustomerCdpProfile - ignoreLock") {
        async {
          system.scheduleOnce(5.second) {
            self ! req
          }
          None
        }
      }
    }
  }

  private val customerReInitRankingDay0SyncKey = "vclub_customer_reinit_ranking_day0_progress"
  private val lastSyncCustomerId = "last_synced_customer_id"

  private def reInitRankingDay0(req: ReInitRankingDay0): Future[Option[Long]] = Profiler(s"$clazz.reInitRankingDay0") {
    internalProcessLock.tryLock(customerReInitRankingDay0SyncKey) {
      case true =>
        Profiler(s"$clazz.reInitRankingDay0 - insideLock") {
          info("Start ReInitRankingDay0 - insideLock")
          for {
            syncMetadata <- metadataSyncService.getMetadata(customerReInitRankingDay0SyncKey).map(metadata => {
              if (metadata.isEmpty) {
                Map(lastSyncTimestampField -> 0L, lastSyncCustomerId -> req.initFromUserId)
              } else metadata
            })

            result <- dataSyncService.reInitRankingDay0(
              syncMetadata.get(lastSyncCustomerId).map(_.asInstanceOf[Long]).getOrElse(0L), req.initToUserId,
              (newSyncTimestamp, nextKey) => metadataSyncService.setMetadata(
                customerReInitRankingDay0SyncKey, Map(lastSyncTimestampField -> newSyncTimestamp, lastSyncCustomerId -> nextKey)
              )
            )
          } yield result

        }.onFailure(ex => {
          error(s"Error when reInitRankingDay0: ${ex.getMessage}", ex)
        }).ensure(
          info("End ReInitRankingDay0 - insideLock")
        )

      case _ => Profiler(s"$clazz.reInitRankingDay0 - ignoreLock") {
        async {
          system.scheduleOnce(5.second) {
            self ! req
          }
          None
        }
      }
    }
  }

  private val customerReScanRankingDay0SyncKey = "vclub_customer_rescan_ranking_day0"

  private def reScanRankingDay0(req: ReScanRankingDay0): Future[Option[Long]] = Profiler(s"$clazz.reScanRankingDay0") {
    internalProcessLock.tryLock(customerReScanRankingDay0SyncKey) {
      case true =>
        Profiler(s"$clazz.reScanRankingDay0 - insideLock") {
          info("Start ReScanRankingDay0 - insideLock")
          for {
            syncMetadata <- metadataSyncService.getMetadata(customerReScanRankingDay0SyncKey).map(metadata => {
              if (metadata.isEmpty) {
                Map(lastSyncTimestampField -> 0L, lastSyncCustomerId -> req.scanFromUserId)
              } else metadata
            })

            result <- dataSyncService.reScanRankingDay0(
              syncMetadata.get(lastSyncCustomerId).map(_.asInstanceOf[Long]).getOrElse(0L), req.scanToUserId,
              (newSyncTimestamp, nextKey) => metadataSyncService.setMetadata(
                customerReScanRankingDay0SyncKey, Map(lastSyncTimestampField -> newSyncTimestamp, lastSyncCustomerId -> nextKey)
              )
            )
          } yield result

        }.onFailure(ex => {
          error(s"Error when reScanRankingDay0: ${ex.getMessage}", ex)
        }).ensure(
          info("End ReScanRankingDay0 - insideLock")
        )

      case _ => Profiler(s"$clazz.reScanRankingDay0 - ignoreLock") {
        async {
          system.scheduleOnce(5.second) {
            self ! req
          }
          None
        }
      }
    }
  }

  private val deactivateIdentifyVerificationWhenDeactivatedCustomerKey = "deactivate_identify_verification_when_deactivated_customer_key"

  private def deactivateIdentifyVerificationWhenDeactivatedCustomer(req: DeactivateIdentifyVerificationReq): Future[Unit] = Profiler(s"$clazz.deactivateIdentifyVerificationWhenDeactivatedCustomer") {

    def getDeactivatedCustomersAndProcessUtilEmpty(from: Int, size: Int,
                                                   fromDeleteAt: Long,
                                                   process: Seq[String] => Future[Unit]): Future[Unit] = Profiler(s"$clazz.getDeactivatedCustomersAndProcessUtilEmpty") {

      async {
        customerDAO.selectUserByActive(from = from, size = size, active = false, fromDeleteAt = fromDeleteAt.toSome, None)
      }.flatMap(userIds => {
        process(userIds).flatMap(_ => {
          if (userIds.length >= size) getDeactivatedCustomersAndProcessUtilEmpty(from + size, size, fromDeleteAt, process)
          else Future.Unit
        })
      })
    }

    internalProcessLock.tryLock(deactivateIdentifyVerificationWhenDeactivatedCustomerKey) {
      case true =>
        Profiler(s"$clazz.deactivateIdentifyVerificationWhenDeactivatedCustomer - insideLock") {

          getDeactivatedCustomersAndProcessUtilEmpty(0, 1000, System.currentTimeMillis() - req.syncBeforeInMillis, customerIds => {
            if (customerIds.isEmpty) Future.Unit
            else {
              info(s"deactivateIdentifyVerification by customers ${JsonHelper.toJson(customerIds)}")
              for {
                _ <- async(identityVerificationDAO.deactivateByCustomerIdsAndActive(customerIds.map(_.toLong), active = true))
              } yield {}
            }
          })
            .rescue {
              case e: Exception => async {
                error(s"Failed deactivateIdentifyVerificationWhenDeactivatedCustomer", e)
              }
            }

        }

      case _ => Profiler(s"$clazz.deactivateIdentifyVerificationWhenDeactivatedCustomer - ignoreLock") {
        async {
          system.scheduleOnce(5.second) {
            self ! req
          }
        }
      }
    }

  }

}
