package profile.service.scheduler

import akka.actor.{Actor, ActorRef}
import akka.vhm.{AkkaImplicits, RAkkaFuture}
import com.twitter.finagle.http.Request
import com.twitter.finatra.jackson.caseclass.exceptions.CaseClassMappingException
import com.twitter.inject.Logging
import com.twitter.util.{Future, Try}
import profile.controller.http.request.RegisterByAdminData
import profile.exception.{AlreadyExistUserForInternalException, InvalidParamException}
import profile.service.AuthenJwtService
import profile.util.Constant
import vn.vhm.common.client.ssdb.SSDBClient
import vn.vhm.common.domain.Implicits.async
import vn.vhm.common.domain.profiling.Profiler
import vn.vhm.common.exception.{UnsupportedException, VhmException}
import vn.vhm.common.service.DebugServiceNotification
import vn.vhm.common.util.{<PERSON><PERSON><PERSON><PERSON><PERSON>, ZConfig}

import scala.concurrent.duration.DurationInt
import profile.service.scheduler.UserRegistrationScheduler._
import scala.collection.JavaConverters._

/**
 * Scheduler to create users from data in SSDB queue.
 */
object UserRegistrationScheduler {
  trait UserRegistrationScheduleReq

  case class ProcessUserRegistrationQueue(intervalInSecond: Int) extends UserRegistrationScheduleReq

  case class UserRegistrationData(
                                   pnl: String,
                                   phone: Option[String],
                                   phoneVerified: Boolean,
                                   email: Option[String],
                                   emailVerified: Boolean,
                                   fullName: Option[String],
                                   referralCode: Option[String]
                                 )
}

case class UserRegistrationScheduler(
                                      ssdbClient: SSDBClient,
                                      authService: AuthenJwtService
                                    ) extends Actor with RAkkaFuture with Logging {

  import AkkaImplicits.CommonActorImplicits
  import context._

  private val clazz: String = getClass.getCanonicalName
  private val queueConfig = ZConfig.getConf("user_registration_queue")
  private val queueKey = queueConfig.getString("queue_key")
  private val batchSize = queueConfig.getInt("batch_size")
  private val intervalInSecond = queueConfig.getInt("interval_in_second")
  private val enabled = queueConfig.getBoolean("enable")

  // Start the scheduler 5 seconds after server start if enabled
  if (enabled) {
    system.scheduleOnce(5.second) {
      self ! ProcessUserRegistrationQueue(intervalInSecond)
    }
  }

  override def receive: Receive = {
    case req: ProcessUserRegistrationQueue => Profiler(s"$clazz.ProcessUserRegistrationQueue") {
      akkaAsync {
        val _sender = sender()
        processUserRegistrationQueue(req)
          .respond(result => notifyIfReceiveError(_sender, req, result))
      }.respond(result => {
        if (result.isReturn && result.get().exists(_ > 0L)) {
          system.scheduleOnce(10.milli) {
            self ! req
          }
        } else {
          system.scheduleOnce(req.intervalInSecond.second) {
            self ! req
          }
        }
      })
    }
  }

  private def processUserRegistrationQueue(req: ProcessUserRegistrationQueue): Future[Option[Long]] = Profiler(s"$clazz.processUserRegistrationQueue") {
    info("Start processing user registration queue")
    for {
      // Pop up to batchSize messages from the queue
      messages <- popMessagesFromQueue(batchSize)

      result <- if (messages.isEmpty) {
        Future.value(None)
      } else {
        // Process messages concurrently
        val futures = messages.map { message =>
          processMessage(message)
        }

        // Join all futures and count successful operations
        Future.collect(futures).map { results =>
          val successCount = results.count(success => success)
          Some(successCount.toLong)
        }
      }

    } yield result
  }.onFailure(ex => {
    error(s"Error when processing user registration queue: ${ex.getMessage}", ex)
  }).ensure(
    info("End processing user registration queue")
  )

  private def popMessagesFromQueue(count: Int): Future[Seq[String]] = Profiler(s"$clazz.popMessagesFromQueue") {
    async {
      if (count <= 0) {
        Seq.empty[String]
      } else {
        // Use batch qpop_front with count parameter to get multiple items at once
        val result = ssdbClient.exec(_.qpop_front(queueKey, count))

        if (result.ok()) {
          // When count > 1, the result is a list of strings
          if (count > 1) {
            result.listString().asScala.toSeq
          } else {
            // When count = 1, the result is a single string
            Seq(result.asString())
          }
        } else if (result.notFound()) {
          // Queue is empty
          Seq.empty[String]
        } else {
          error(s"Failed to pop from queue: ${ssdbClient.getError(result)}")
          Seq.empty[String]
        }
      }
    }
  }


  // Put a message back at the end of the queue
  private def putMessageBackToQueue(message: String): Future[Unit] = Profiler(s"$clazz.putMessageBackToQueue") {
    async {
      try {
        ssdbClient.exec(_.qpush_back(queueKey, message))
      } catch {
        case e: Exception =>
          error(s"Error putting message back to queue: ${e.getMessage}", e)
      }
      ()
    }
  }

  private def processMessage(message: String): Future[Boolean] = Profiler(s"$clazz.processMessage") {
    val fn = for {
      // Parse the message data as JSON
      userData <- try {
        Future.value(JsonHelper.fromJson[UserRegistrationData](message))
      } catch {
        case _: Exception =>
          Future.exception(InvalidParamException("invalid_json_format"))
      }

      _ <- if (userData.phone.exists(_.nonEmpty) && !userData.phoneVerified) {
        Future.exception(InvalidParamException("phone_not_verified"))
      } else Future.Unit

      _ <- if (userData.email.exists(_.nonEmpty) && !userData.emailVerified) {
        Future.exception(InvalidParamException("email_not_verified"))
      } else Future.Unit

      // Create the registration request
      registerData = RegisterByAdminData(
        phone = userData.phone,
        email = userData.email.map(_.toLowerCase),
        fullName = userData.fullName.orElse(Some("-")),
        phoneVerified = userData.phoneVerified,
        emailVerified = userData.emailVerified,
        pnl = Some(userData.pnl),
        referralCode = userData.referralCode,
        cmsUsername = Constant.USER_SYSTEM
      )

      _ <- if (!registerData.isValid) {
        Future.exception(InvalidParamException("invalid_registration_request"))
      } else Future.Unit

      // Call the registration service
      result <- authService.registerByAdmin(Request(), registerData).map(_ => true)
        .rescue {
          case e: AlreadyExistUserForInternalException =>
            Future.exception(e)
          case e: UnsupportedException =>
            Future.exception(e)
          case e: InvalidParamException =>
            Future.exception(e)
          case e: CaseClassMappingException =>
            Future.exception(InvalidParamException("invalid_registration_request", e.getMessage))
          case e: Exception =>
            Future.exception(e)
        }
    } yield result

    // Handle all exceptions and convert them to successful futures with appropriate return values
    fn.rescue {
      case e: AlreadyExistUserForInternalException =>
        error(s"User already exist: $message\t${e.getMessage}")
        Future.value(false)
      case e: UnsupportedException =>
        error(s"Unsupported registration request: $message\t${e.getMessage}")
        Future.value(false)
      case e: InvalidParamException =>
        error(s"Invalid registration request: $message\t${e.getMessage}")
        Future.value(false)
      case e: CaseClassMappingException =>
        error(s"Invalid registration request: $message\t${e.getMessage}")
        Future.value(false)
      case e: Exception =>
        error(s"Error processing message: $message\t${e.getMessage}", e)
        // Put message back in queue to try again later
        putMessageBackToQueue(message).map(_ => false)
    }
  }


  def notifyIfReceiveError[T](_sender: ActorRef, req: UserRegistrationScheduleReq, result: Try[T]): Unit = {
    result match {
      case com.twitter.util.Throw(e) =>
        val (errorVal, errorMessageVal) = e match {
          case e: VhmException => (e.error, e.getMessage)
          case _ => (e.getClass.getCanonicalName, e.getMessage)
        }
        error(s"Receive error of flow ${req.getClass.getCanonicalName}: $errorVal - $errorMessageVal")
        DebugServiceNotification.notify(
          s"Receive error of flow ${getClass.getCanonicalName}.${req.getClass.getCanonicalName}: $errorVal - $errorMessageVal"
        )
      case _ =>
    }
  }
}
