package profile.service

import com.twitter.finagle.http.Request
import com.twitter.inject.Logging
import com.twitter.util.{Future, Return, Throw}
import core.quota.domain.QuotaFilterContext.QuotaFilterContextSyntax
import core.ratelimiter.service.CaptchaService
import profile.controller.http.filter.phone.PhoneQuotaJwtReq
import profile.controller.http.filter.{EmailQuotaJwtReq, UserQuotaJwtReq}
import profile.exception._
import vn.vhm.common.domain.Implicits.async
import vn.vhm.common.domain.OptionImplicits.OptionImplicitAny
import vn.vhm.common.domain.profiling.Profiler
import vn.vhm.common.util.ZConfig

import scala.concurrent.duration.{Duration, DurationInt}

/**
 * <AUTHOR> 2/14/24 12:24
 */
trait HttpQuotaService {

  def checkQuotaPhone(
                       request: Request,
                       needCheckExist: <PERSON><PERSON><PERSON>, applyToExist: <PERSON><PERSON><PERSON>,
                       reqData: PhoneQuotaJwtReq,
                       checkQuotaCountry: <PERSON><PERSON><PERSON>, checkQuotaIP: Boolean): Future[Unit]

  def checkQuotaEmail(
                       request: Request,
                       needCheckExist: Boolean, applyToExist: Boolean,
                       reqData: EmailQuotaJwtReq,
                       checkQuotaCountry: Boolean, checkQuotaIP: Boolean): Future[Unit]

  def checkQuotaLoginPhone(reqData: PhoneQuotaJwtReq): Future[Unit]

  def checkQuotaLoginEmail(reqData: EmailQuotaJwtReq): Future[Unit]

  def calcQuotaPhoneMac[T](reqData: PhoneQuotaJwtReq, mac: String, ttl: Duration, numCheck: Int, deleteIfSuccess: Boolean)(fn: => Future[T]): Future[T]

  def calcQuotaPhoneSendCode[T](request: Request,
                                reqData: PhoneQuotaJwtReq,
                                checkQuotaCountry: Boolean,
                                checkQuotaIP: Boolean)(fn: => Future[T]): Future[T]

  def calcQuotaPhoneSendCode[T](reqData: PhoneQuotaJwtReq)(fn: => Future[T]): Future[T]

  def calcQuotaPhoneVerifyCode[T](reqData: PhoneQuotaJwtReq, mac: String, ttl: Duration, numCheck: Int)(fn: => Future[T]): Future[T]

  def calcQuotaEmailMac[T](reqData: EmailQuotaJwtReq, mac: String, ttl: Duration, numCheck: Int, deleteIfSuccess: Boolean)(fn: => Future[T]): Future[T]


  def calcQuotaEmailSendCode[T](request: Request,
                                reqData: EmailQuotaJwtReq,
                                checkQuotaCountry: Boolean,
                                checkQuotaIP: Boolean)(fn: => Future[T]): Future[T]

  def calcQuotaEmailSendCode[T](reqData: EmailQuotaJwtReq)(fn: => Future[T]): Future[T]

  def calcQuotaEmailVerifyCode[T](reqData: EmailQuotaJwtReq, mac: String, ttl: Duration, numCheck: Int)(fn: => Future[T]): Future[T]

  def calcQuotaUserMac[T](reqData: UserQuotaJwtReq, mac: String, ttl: Duration, numCheck: Int, deleteIfSuccess: Boolean)(fn: => Future[T]): Future[T]

  def calcQuotaUserMac2[T](reqData: UserQuotaJwtReq, mac: String, ttl: Duration, numCheck: Int, deleteIfSuccess: Boolean)(fn: (Int, Int) => Future[T]): Future[T]

  def calcQuotaUserMac3[T](request: Request,
                           reqData: UserQuotaJwtReq,
                           mac: String, ttlMac: Duration, numCheckMac: Int, deleteIfSuccess: Boolean,
                           checkQuotaIP: Option[Boolean] = false.toSome, ttlIp: Option[Duration] = None, numCheckIp: Option[Int] = None)(fn: => Future[T]): Future[T]

  def calcQuotaEntityMac[T](entityType: String, entityId: String, mac: String,
                            tokenCaptcha: Option[String],
                            ttl: Duration, numCheck: Int, deleteIfSuccess: Boolean)(fn: => Future[T]): Future[T]
}

case class HttpQuotaWithLockServiceImpl(profileService: ProfileService,
                                        verifyDataService: VerifyDataService) extends HttpQuotaService with Logging {

  private val clazz = getClass.getCanonicalName

  private val defaultLockInSecond = ZConfig.getInt("common.lock_in_second", 60)

  //region Phone

  override def checkQuotaPhone(request: Request,
                               needCheckExist: Boolean, applyToExist: Boolean, reqData: PhoneQuotaJwtReq,
                               checkQuotaCountry: Boolean, checkQuotaIP: Boolean): Future[Unit] = Profiler(s"$clazz.checkQuotaPhone") {
    request.quotaChecked match {
      case true => Future.Unit
      case _ =>
        _checkQuotaPhone(request, needCheckExist, applyToExist, reqData, checkQuotaCountry, checkQuotaIP)
          .map(_ => request.setQuotaChecked())
    }
  }

  private def _checkQuotaPhone(request: Request, needCheckExist: Boolean, applyToExist: Boolean, reqData: PhoneQuotaJwtReq,
                               checkQuotaCountry: Boolean, checkQuotaIP: Boolean): Future[Unit] = Profiler(s"$clazz._checkQuotaPhone") {

    val optPhone = reqData.optNormPhone()

    val optCountry = getValueIfNeeded(checkQuotaCountry, request.optCountry)
    val countryIsVN = getValueIfNeeded(checkQuotaCountry, request.countryIsVN)
    val optIp = getValueIfNeeded(checkQuotaIP, request.optIP)

    optPhone match {
      case Some(phone) if needCheckExist =>
        profileService.getActiveUsernameByPhone(phone).flatMap {
          case Some(_) if applyToExist => _checkQuotaPhone2(phone, optIp, optCountry, countryIsVN, reqData)
          case None if !applyToExist => _checkQuotaPhone2(phone, optIp, optCountry, countryIsVN, reqData)
          case _ => Future.Unit
        }

      case Some(phone) => _checkQuotaPhone2(phone, optIp, optCountry, countryIsVN, reqData)

      case _ => Future.Unit // Future.exception(ExceedQuotaException())
    }
  }

  private def _checkQuotaPhone2(phone: String,
                                optIp: Option[String], optCountry: Option[String],
                                countryIsVN: Option[Boolean],
                                reqData: PhoneQuotaJwtReq): Future[Unit] = Profiler(s"$clazz._checkQuotaPhone2") {
    val fn = for {
      _ <- verifyDataService.getLockTimeByEntity("phone", phone, "").map(lockTime => {
        if (lockTime.nonEmpty) {
          throw LockedException(s"phone:true,country:${optCountry.getOrElse("undef")}", data = Map("locked_until_time" -> lockTime))
        }
      })

      _ <- verifyDataService.isExceedQuotaPhone(phone, None).flatMap(exceedQuotaPhone => {
        if (exceedQuotaPhone) {
          verifyDataService.buildEntityWithLockTime("phone", phone, "", reqData.optLockInDuration().getOrElse(defaultLockInSecond.second))
            .onSuccess(_ => verifyDataService.deleteQuotaPhone(phone))
            .flatMap(lockTime => {
              Future.exception(LockedException(s"phone:true,country:undef", data = Map("locked_until_time" -> lockTime)))
            })
        } else Future.Unit
      })

      // check quota ip
      _ <- optIp match {
        case Some(ip) =>
          verifyDataService.isExceedQuotaIP(ip) flatMap {
            case true =>
              verifyDataService.buildEntityWithLockTime("phone", phone, "", reqData.optLockInDuration().getOrElse(defaultLockInSecond.second))
                .onSuccess(_ => verifyDataService.deleteQuotaIP(ip))
                .flatMap(lockTime => {
                  Future.exception(LockedException(s"ip:true,country:${optCountry.getOrElse("undef")}", data = Map("locked_until_time" -> lockTime)))
                })
            case _ =>
              verifyDataService.incrQuotaIP(ip)
          }
        case _ => Future.Unit
      }


      // check country
      //      _ <- {
      //        if (countryIsVN.forall(_ == true)) Future.Unit
      //        else {
      //          verifyDataService.buildEntityWithLockTime("phone", phone, "", None)
      //            .flatMap(lockTime => {
      //              Future.exception(LockedException(s"ip:true,country:${optCountry.getOrElse("undef")}", data = Map("locked_until_time" -> lockTime)))
      //            })
      //        }
      //      }

    } yield {}

    fn.rescue {
      case _: IgnoreProcess => Future.Unit
    } respond {
      case Return(_) => Profiler(s"${getClass.getCanonicalName}.apply-true") {}
      case Throw(e) => e match {
        case e1: LockedException => Profiler(s"${getClass.getCanonicalName}.apply-false-${e1.message}") {}
        case _ => Profiler(s"${getClass.getCanonicalName}.apply-false-${e.getClass.getCanonicalName}-${e.getMessage}") {}
      }
    }

  }

  //endregion

  //region Email

  override def checkQuotaEmail(request: Request, needCheckExist: Boolean, applyToExist: Boolean,
                               reqData: EmailQuotaJwtReq,
                               checkQuotaCountry: Boolean, checkQuotaIP: Boolean): Future[Unit] = Profiler(s"$clazz.checkQuotaEmail") {
    request.quotaChecked match {
      case true => Future.Unit
      case _ =>
        _checkQuotaEmail(request, needCheckExist, applyToExist, reqData, checkQuotaCountry, checkQuotaIP)
          .map(_ => request.setQuotaChecked())
    }
  }

  private def getValueIfNeeded[T](isNeed: Boolean, value: Option[T]): Option[T] = {
    if (isNeed) value else None
  }

  private def _checkQuotaEmail(request: Request, needCheckExist: Boolean, applyToExist: Boolean,
                               reqData: EmailQuotaJwtReq,
                               checkQuotaCountry: Boolean, checkQuotaIP: Boolean): Future[Unit] = Profiler(s"$clazz._checkQuotaEmail") {

    val optEmail = reqData.optQuotaEmail()

    val optCountry = getValueIfNeeded(checkQuotaCountry, request.optCountry)
    val countryIsVN = getValueIfNeeded(checkQuotaCountry, request.countryIsVN)


    val optIp = getValueIfNeeded(checkQuotaIP, request.optIP)

    optEmail match {
      case Some(email) if needCheckExist =>
        profileService.getActiveUsernameByEmail(email).flatMap {
          case Some(_) if applyToExist => _checkQuotaEmail2(email, optIp, optCountry, countryIsVN, reqData)
          case None if !applyToExist => _checkQuotaEmail2(email, optIp, optCountry, countryIsVN, reqData)
          case _ => Future.Unit
        }
      case Some(email) => _checkQuotaEmail2(email, optIp, optCountry, countryIsVN, reqData)

      case _ => Future.Unit // Future.exception(ExceedQuotaException())
    }
  }

  private def _checkQuotaEmail2(email: String,
                                optIp: Option[String], optCountry: Option[String],
                                countryIsVN: Option[Boolean],
                                reqData: EmailQuotaJwtReq): Future[Unit] = Profiler(s"$clazz._checkQuotaEmail2") {
    val fn = for {
      _ <- verifyDataService.getLockTimeByEntity("email", email, "").map(lockTime => {
        if (lockTime.nonEmpty) {
          throw LockedException(s"email:true,country:undef", data = Map("locked_until_time" -> lockTime))
        }
      })

      // check quota email
      _ <- verifyDataService.isExceedQuotaEmail(email, None).flatMap(exceedQuotaEmail => {
        if (exceedQuotaEmail) {
          verifyDataService.buildEntityWithLockTime("email", email, "", reqData.optLockInDuration().getOrElse(defaultLockInSecond.second))
            .onSuccess(_ => verifyDataService.deleteQuotaEmail(email))
            .flatMap(lockTime => {
              Future.exception(LockedException(s"email:true,country:${optCountry.getOrElse("undef")}", data = Map("locked_until_time" -> lockTime)))
            })
        }
        else Future.Unit
      })

      // check quota ip
      _ <- optIp match {
        case Some(ip) =>
          verifyDataService.isExceedQuotaIP(ip) flatMap {
            case true =>
              verifyDataService.buildEntityWithLockTime("email", email, "", reqData.optLockInDuration().getOrElse(defaultLockInSecond.second))
                .onSuccess(_ => verifyDataService.deleteQuotaIP(ip))
                .flatMap(lockTime => {
                  Future.exception(LockedException(s"ip:true,country:${optCountry.getOrElse("undef")}", data = Map("locked_until_time" -> lockTime)))
                })
            case _ =>
              verifyDataService.incrQuotaIP(ip)
          }
        case _ => Future.Unit
      }

      // check country

    } yield {}

    fn.rescue {
      case _: IgnoreProcess => Future.Unit
    } respond {
      case Return(_) => Profiler(s"${getClass.getCanonicalName}.apply-true") {}
      case Throw(e) => e match {
        case e1: LockedException => Profiler(s"${getClass.getCanonicalName}.apply-false-${e1.message}") {}
        case _ => Profiler(s"${getClass.getCanonicalName}.apply-false-${e.getClass.getCanonicalName}-${e.getMessage}") {}
      }
    }
  }

  //endregion

  override def checkQuotaLoginPhone(reqData: PhoneQuotaJwtReq): Future[Unit] = Future.Unit

  override def calcQuotaPhoneSendCode[T](request: Request,
                                         reqData: PhoneQuotaJwtReq,
                                         checkQuotaCountry: Boolean,
                                         checkQuotaIP: Boolean)(fn: => Future[T]): Future[T] = Profiler(s"$clazz.calcQuotaPhoneSendCode") {
    val phone = reqData.optNormPhone().getOrElse("")
    val optCountry = getValueIfNeeded(checkQuotaCountry, request.optCountry)
    val countryIsVN = getValueIfNeeded(checkQuotaCountry, request.countryIsVN)
    val optIp = getValueIfNeeded(checkQuotaIP, request.optIP)

    for {
      _ <- verifyDataService.getLockTimeByEntity("phone", phone, "").map(lockTime => {
        if (lockTime.nonEmpty) {
          throw LockedException(s"phone:true,country:${optCountry.getOrElse("undef")}", data = Map("locked_until_time" -> lockTime))
        }
      })

      _ <- verifyDataService.isExceedQuotaPhone(phone, None).flatMap(exceedQuotaPhone => {
        if (exceedQuotaPhone) {
          verifyDataService.buildEntityWithLockTime("phone", phone, "", reqData.optLockInDuration().getOrElse(defaultLockInSecond.second))
            .onSuccess(_ => verifyDataService.deleteQuotaPhone(phone))
            .flatMap(lockTime => {
              Future.exception(LockedException(s"phone:true,country:undef", data = Map("locked_until_time" -> lockTime)))
            })
        } else Future.Unit
      })

      // check quota ip
      _ <- optIp match {
        case Some(ip) =>
          verifyDataService.isExceedQuotaIP(ip) flatMap {
            case true =>
              verifyDataService.buildEntityWithLockTime("phone", phone, "", reqData.optLockInDuration().getOrElse(defaultLockInSecond.second))
//                .onSuccess(_ => verifyDataService.deleteQuotaIP(ip))
                .flatMap(lockTime => {
                  Future.exception(LockedException(s"ip:true,country:${optCountry.getOrElse("undef")}", data = Map("locked_until_time" -> lockTime)))
                })
            case _ =>
              verifyDataService.incrQuotaIP(ip)
          }
        case _ => Future.Unit
      }

      result <- fn

    } yield result
  }

  override def calcQuotaPhoneSendCode[T](reqData: PhoneQuotaJwtReq)(fn: => Future[T]): Future[T] = Profiler(s"$clazz.calcQuotaPhoneSendCode-simple") {
    val phone = reqData.optNormPhone().getOrElse("")
    for {
      _ <- verifyDataService.getLockTimeByEntity("phone", phone, "").map(lockTime => {
        if (lockTime.nonEmpty) {
          throw LockedException(s"phone:true", data = Map("locked_until_time" -> lockTime))
        }
      })

      result <- fn.transform {
        case Return(r) =>
          for {
            _ <- verifyDataService.isExceedQuotaPhone(phone, None).flatMap(exceedQuotaPhone => {
              if (exceedQuotaPhone) {
                verifyDataService.buildEntityWithLockTime("phone", phone, "", reqData.optLockInDuration().getOrElse(defaultLockInSecond.second))
                  .onSuccess(_ => verifyDataService.deleteQuotaPhone(phone))
                  .flatMap(lockTime => {
                    Future.exception(LockedException(s"phone:true,country:undef", data = Map("locked_until_time" -> lockTime)))
                  })
              } else Future.Unit
            })
          } yield r
        case Throw(e) => Future.exception(e)
      }
    } yield result
  }

  override def calcQuotaPhoneVerifyCode[T](reqData: PhoneQuotaJwtReq, mac: String,
                                           ttl: Duration, numCheck: Int)(fn: => Future[T]): Future[T] = Profiler(s"$clazz.calcQuotaPhoneVerifyCode") {
    val phone = reqData.optNormPhone().getOrElse("")
    for {
      _ <- verifyDataService.getLockTimeByEntity("phone", phone, mac).map(lockTime => {
        if (lockTime.nonEmpty) {
          throw LockedException(s"phone:true,country:undef", data = Map("locked_until_time" -> lockTime, "max_failed" -> numCheck))
        }
      })

      result <- fn.transform {
        case Return(r) => verifyDataService.deleteQuotaPhoneMac(phone, mac).map(_ => r)
        case Throw(e: InvalidVerifyPhoneCodeException) if e.isExpiredCode => Future.exception(e.copy(data = Map("is_expired_code" -> true)))
        case Throw(e: InvalidVerifyPhoneCodeException) =>
          verifyDataService.incrQuotaPhoneMac(phone, mac, ttl)
            .flatMap(numQuota => {
              if (numQuota < numCheck) {
                Future.exception(e.copy(data = Map(
                  "num_failed" -> numQuota, "max_failed" -> numCheck,
                  "lock_in_millis" -> reqData.optLockInDuration().map(_.toMillis).getOrElse(defaultLockInSecond * 1000L))
                ))
              }
              else {
                verifyDataService.buildEntityWithLockTime("phone", phone, mac, reqData.optLockInDuration().getOrElse(defaultLockInSecond.second))
                  .onSuccess(_ => verifyDataService.deleteQuotaPhoneMac(phone, mac))
                  .flatMap(lockTime => {
                    Future.exception(LockedException(s"phone:true,country:undef", data = Map("locked_until_time" -> lockTime, "max_failed" -> numCheck)))
                  })
              }
            })
        case Throw(e) => Future.exception(e)
      }

    } yield result
  }

  override def calcQuotaPhoneMac[T](reqData: PhoneQuotaJwtReq, mac: String,
                                    ttl: Duration, numCheck: Int, deleteIfSuccess: Boolean)(fn: => Future[T]): Future[T] = Profiler(s"$clazz.checkQuotaPhoneMac") {
    val phone = reqData.optNormPhone().getOrElse("")
    for {
      _ <- verifyDataService.getLockTimeByEntity("phone", phone, mac).map(lockTime => {
        if (lockTime.nonEmpty) {
          throw LockedException(s"phone:true", data = Map("locked_until_time" -> lockTime))
        }
      })

      _ <- {
        verifyDataService.incrQuotaPhoneMac(phone, mac, ttl).flatMap(numQuota => {
          if (numQuota > numCheck) {
            verifyDataService.buildEntityWithLockTime("phone", phone, mac, reqData.optLockInDuration().getOrElse(defaultLockInSecond.second))
              .onSuccess(_ => verifyDataService.deleteQuotaPhoneMac(phone, mac))
              .flatMap(lockTime => {
                Future.exception(LockedException(s"phone:true,country:undef", data = Map("locked_until_time" -> lockTime)))
              })
          } else Future.Unit
        })
      }

      result <- fn

    } yield result

  }

  override def calcQuotaEmailMac[T](reqData: EmailQuotaJwtReq, mac: String,
                                    ttl: Duration, numCheck: Int,
                                    deleteIfSuccess: Boolean)(fn: => Future[T]): Future[T] = Profiler(s"$clazz.calcQuotaEmailMac") {
    val email = reqData.optQuotaEmail().getOrElse("")
    for {
      _ <- verifyDataService.getLockTimeByEntity("email", email, mac).map(lockTime => {
        if (lockTime.nonEmpty) {
          throw LockedException(s"email:true,country:undef", data = Map("locked_until_time" -> lockTime))
        }
      })

      // check quota email
      _ <- verifyDataService.incrQuotaEmailMac(email, mac, ttl).flatMap(numQuota => {
        if (numQuota > numCheck) {
          verifyDataService.buildEntityWithLockTime("email", email, mac, reqData.optLockInDuration().getOrElse(defaultLockInSecond.second))
            .onSuccess(_ => verifyDataService.deleteQuotaEmailMac(email, mac))
            .flatMap(lockTime => {
              Future.exception(LockedException(s"email:true", data = Map("locked_until_time" -> lockTime)))
            })
        } else Future.Unit
      })

      result <- fn

    } yield result

  }

  override def calcQuotaEmailSendCode[T](request: Request,
                                         reqData: EmailQuotaJwtReq,
                                         checkQuotaCountry: Boolean,
                                         checkQuotaIP: Boolean)(fn: => Future[T]): Future[T] = Profiler(s"$clazz.calcQuotaEmailSendCode") {
    val email = reqData.optQuotaEmail().getOrElse("")

    val optCountry = getValueIfNeeded(checkQuotaCountry, request.optCountry)
    val countryIsVN = getValueIfNeeded(checkQuotaCountry, request.countryIsVN)
    val optIp = getValueIfNeeded(checkQuotaIP, request.optIP)

    for {
      _ <- verifyDataService.getLockTimeByEntity("email", email, "").map(lockTime => {
        if (lockTime.nonEmpty) {
          throw LockedException(s"email:true,country:undef", data = Map("locked_until_time" -> lockTime))
        }
      })

      // check quota email
      _ <- verifyDataService.isExceedQuotaEmail(email, None).flatMap(exceedQuotaEmail => {
        if (exceedQuotaEmail) {
          verifyDataService.buildEntityWithLockTime("email", email, "", reqData.optLockInDuration().getOrElse(defaultLockInSecond.second))
            .onSuccess(_ => verifyDataService.deleteQuotaEmail(email))
            .flatMap(lockTime => {
              Future.exception(LockedException(s"email:true,country:${optCountry.getOrElse("undef")}", data = Map("locked_until_time" -> lockTime)))
            })
        }
        else Future.Unit
      })

      // check quota ip
      _ <- optIp match {
        case Some(ip) =>
          verifyDataService.isExceedQuotaIP(ip) flatMap {
            case true =>
              verifyDataService.buildEntityWithLockTime("email", email, "", reqData.optLockInDuration().getOrElse(defaultLockInSecond.second))
                //                .onSuccess(_ => verifyDataService.deleteQuotaIP(ip))
                .flatMap(lockTime => {
                  Future.exception(LockedException(s"ip:true,country:${optCountry.getOrElse("undef")}", data = Map("locked_until_time" -> lockTime)))
                })
            case _ =>
              verifyDataService.incrQuotaIP(ip)
          }
        case _ => Future.Unit
      }

      result <- fn
    } yield result
  }

  override def calcQuotaEmailSendCode[T](reqData: EmailQuotaJwtReq)(fn: => Future[T]): Future[T] = Profiler(s"$clazz.calcQuotaEmailSendCode-simple") {
    val email = reqData.optQuotaEmail().getOrElse("")

    for {
      _ <- verifyDataService.getLockTimeByEntity("email", email, "").map(lockTime => {
        if (lockTime.nonEmpty) {
          throw LockedException(s"email:true,country:undef", data = Map("locked_until_time" -> lockTime))
        }
      })

      result <- fn.transform {
        case Return(r) =>
          for {
            // check quota email
            _ <- verifyDataService.isExceedQuotaEmail(email, None).flatMap(exceedQuotaEmail => {
              if (exceedQuotaEmail) {
                verifyDataService.buildEntityWithLockTime("email", email, "", reqData.optLockInDuration().getOrElse(defaultLockInSecond.second))
                  .onSuccess(_ => verifyDataService.deleteQuotaEmail(email))
                  .flatMap(lockTime => {
                    Future.exception(LockedException(s"email:true", data = Map("locked_until_time" -> lockTime)))
                  })
              }
              else Future.Unit
            })
          } yield r
        case Throw(e) => Future.exception(e)
      }
    } yield result
  }

  override def calcQuotaEmailVerifyCode[T](reqData: EmailQuotaJwtReq, mac: String, ttl: Duration,
                                           numCheck: Int)(fn: => Future[T]): Future[T] = {
    val email = reqData.optQuotaEmail().getOrElse("")
    for {
      _ <- verifyDataService.getLockTimeByEntity("email", email, mac).map(lockTime => {
        if (lockTime.nonEmpty) {
          throw LockedException(s"email:true,country:undef", data = Map("locked_until_time" -> lockTime, "max_failed" -> numCheck))
        }
      })

      result <- fn.transform {
        case Return(r) => verifyDataService.deleteQuotaEmailMac(email, mac).map(_ => r)
        case Throw(e: InvalidVerifyEmailCodeException) if e.isExpiredCode => Future.exception(e.copy(data = Map("is_expired_code" -> true)))

        case Throw(e: InvalidVerifyEmailCodeException) =>
          verifyDataService.incrQuotaEmailMac(email, mac, ttl)
            .flatMap(numQuota => {
              if (numQuota < numCheck) {
                Future.exception(e.copy(data = Map(
                  "num_failed" -> numQuota, "max_failed" -> numCheck,
                  "lock_in_millis" -> reqData.optLockInDuration().map(_.toMillis).getOrElse(defaultLockInSecond * 1000L))
                ))
              }
              else {
                verifyDataService.buildEntityWithLockTime("email", email, mac, reqData.optLockInDuration().getOrElse(defaultLockInSecond.second))
                  .onSuccess(_ => verifyDataService.deleteQuotaEmailMac(email, mac))
                  .flatMap(lockTime => {
                    Future.exception(LockedException(s"email:true,country:undef", data = Map("locked_until_time" -> lockTime, "max_failed" -> numCheck)))
                  })
              }
            })
        case Throw(e) => Future.exception(e)
      }

    } yield result
  }

  override def calcQuotaUserMac[T](reqData: UserQuotaJwtReq, mac: String,
                                   ttl: Duration, numCheck: Int, deleteIfSuccess: Boolean)(fn: => Future[T]): Future[T] = Profiler(s"$clazz.calcQuotaUserMac") {
    val user = reqData.quotaUsername()
    for {
      _ <- verifyDataService.getLockTimeByEntity("user", user, mac).map(lockTime => {
        if (lockTime.nonEmpty) {
          throw LockedException(s"user:true,country:undef", data = Map("locked_until_time" -> lockTime))
        }
      })

      currentVal <- verifyDataService.isExceedQuotaUserMac(user, mac, numCheck).flatMap(exceedQuotaPhone => {
        if (exceedQuotaPhone) {
          verifyDataService.buildEntityWithLockTime("user", user, mac, reqData.optLockInDuration().getOrElse(defaultLockInSecond.second))
            .onSuccess(_ => verifyDataService.deleteQuotaUserMac(user, mac))
            .flatMap(lockTime => {
              Future.exception(LockedException(s"user:true,country:undef", data = Map("locked_until_time" -> lockTime)))
            })
        }
        else verifyDataService.incrQuotaUserMac(user, mac, ttl)
      })

      result <- fn

    } yield result

  }

  override def calcQuotaUserMac2[T](reqData: UserQuotaJwtReq, mac: String,
                                    ttl: Duration, numCheck: Int, deleteIfSuccess: Boolean)(fn: (Int, Int) => Future[T]): Future[T] = Profiler(s"$clazz.calcQuotaUserMac2") {
    val user = reqData.quotaUsername()
    for {
      _ <- verifyDataService.getLockTimeByEntity("user", user, mac).map(lockTime => {
        if (lockTime.nonEmpty) {
          throw LockedException(s"user:true,country:undef", data = Map("locked_until_time" -> lockTime))
        }
      })

      currentVal <- verifyDataService.isExceedQuotaUserMac(user, mac, numCheck).flatMap(exceedQuotaPhone => {
        if (exceedQuotaPhone) {
          verifyDataService.buildEntityWithLockTime("user", user, mac, reqData.optLockInDuration().getOrElse(defaultLockInSecond.second))
            .onSuccess(_ => verifyDataService.deleteQuotaUserMac(user, mac))
            .flatMap(lockTime => {
              Future.exception(LockedException(s"user:true,country:undef", data = Map("locked_until_time" -> lockTime)))
            })
        }
        else verifyDataService.incrQuotaUserMac(user, mac, ttl)
      })

      result <- fn(currentVal, numCheck)

    } yield result

  }

  override def calcQuotaUserMac3[T](request: Request,
                                    reqData: UserQuotaJwtReq,
                                    mac: String, ttlMac: Duration, numCheckMac: Int, deleteIfSuccess: Boolean,
                                    checkQuotaIP: Option[Boolean] = false.toSome, ttlIp: Option[Duration] = None, numCheckIp: Option[Int] = None)(fn: => Future[T]): Future[T] = Profiler(s"$clazz.calcQuotaUserMac3") {
    val user = reqData.quotaUsername()
    val optIp = getValueIfNeeded(checkQuotaIP.getOrElse(false), request.optIP)

    def _checkQuota: Future[Unit] = {
      for {
        _ <- verifyDataService.isExceedQuotaUserMac(user, mac, numCheckMac).flatMap(exceedQuotaUser => {
          if (exceedQuotaUser) {
            verifyDataService.getRemainingTtlQuotaUserMac(user, mac).flatMap {
              case Some(remainTtl) =>
                Future.exception(LockedException(s"user:true,country:undef", data = Map("locked_until_time" -> (System.currentTimeMillis() + remainTtl * 1000L))))
              case _ => Future.Unit
            }
          } else verifyDataService.incrQuotaUserMac(user, mac, ttlMac)
        })

        // check quota ip
        _ <- optIp match {
          case Some(ip) =>
            verifyDataService.isExceedQuotaIP(ip, numCheckIp) flatMap {
              case true =>
                verifyDataService.getRemainingTtlQuotaIP(ip).flatMap {
                  case Some(remainTtl) =>
                    Future.exception(LockedException(s"user:true,country:undef", data = Map("locked_until_time" -> (System.currentTimeMillis() + remainTtl * 1000L))))
                  case _ => Future.Unit
                }
              case _ =>
                verifyDataService.incrQuotaIP(ip, ttlIp)
            }
          case _ => Future.Unit
        }
      } yield {}
    }

    for {
      result <- fn.transform {
        case Return(r) =>
          _checkQuota.map(_ => r)
        case Throw(e: LockedException) =>
          Future.exception(e)
        case Throw(e) =>
          _checkQuota.flatMap(_ => Future.exception(e))
      }
    } yield result
  }

  override def checkQuotaLoginEmail(reqData: EmailQuotaJwtReq): Future[Unit] = Future.Unit

  override def calcQuotaEntityMac[T](entityType: String, entityId: String, mac: String,
                                     tokenCaptcha: Option[String],
                                     ttl: Duration, numCheck: Int, deleteIfSuccess: Boolean)(fn: => Future[T]): Future[T] = Profiler(s"$clazz.calcQuotaEntityMac") {
    for {

      exceedQuotaEntity <- verifyDataService.isExceedQuotaEntityMac(entityType, entityId, mac, numCheck)
        .respond(_ => verifyDataService.incrQuotaEntityMac(entityType, entityId, mac, ttl))


      _ <- {
        if (exceedQuotaEntity) Future.exception(ExceedQuotaException(s"entity_type=$entityType,entity:true,country:undef"))
        else Future.Unit
      }

      result <- {
        fn
          .onSuccess(_ => if (deleteIfSuccess) verifyDataService.deleteQuotaEntityMac(entityType, entityId, mac))
      }

    } yield result

  }
}

case class HttpQuotaWithCaptchaServiceImpl(
                                            profileService: ProfileService,
                                            verifyDataService: VerifyDataService,
                                            captchaService: CaptchaService) extends HttpQuotaService {

  private val clazz = getClass.getCanonicalName

  //region Phone

  override def checkQuotaPhone(request: Request,
                               needCheckExist: Boolean, applyToExist: Boolean, reqData: PhoneQuotaJwtReq,
                               checkQuotaCountry: Boolean, checkQuotaIP: Boolean): Future[Unit] = Profiler(s"$clazz.checkQuotaPhone") {

    request.quotaChecked match {
      case true => Future.Unit
      case _ =>
        _checkQuotaPhone(request, needCheckExist, applyToExist, reqData, checkQuotaCountry, checkQuotaIP)
          .map(_ => request.setQuotaChecked())
    }
  }

  private def _checkQuotaPhone(request: Request, needCheckExist: Boolean, applyToExist: Boolean, reqData: PhoneQuotaJwtReq,
                               checkQuotaCountry: Boolean, checkQuotaIP: Boolean): Future[Unit] = Profiler(s"$clazz._checkQuotaPhone") {

    val optPhone = reqData.optNormPhone()

    val optCountry = getValueIfNeeded(checkQuotaCountry, request.optCountry)
    val countryIsVN = getValueIfNeeded(checkQuotaCountry, request.countryIsVN)
    val optIp = getValueIfNeeded(checkQuotaIP, request.optIP)

    val optTokenCaptcha = reqData.optTokenCaptcha()

    optPhone match {
      case Some(phone) if needCheckExist =>
        profileService.getActiveUsernameByPhone(phone).flatMap {
          case Some(_) if applyToExist => _checkQuotaPhone2(phone, optTokenCaptcha, optIp, optCountry, countryIsVN)
          case None if !applyToExist => _checkQuotaPhone2(phone, optTokenCaptcha, optIp, optCountry, countryIsVN)
          case _ => Future.Unit
        }

      case Some(phone) => _checkQuotaPhone2(phone, optTokenCaptcha, optIp, optCountry, countryIsVN)

      case _ => Future.Unit // Future.exception(ExceedQuotaException())
    }
  }

  private def _checkQuotaPhone2(phone: String, optTokenCaptcha: Option[String],
                                optIp: Option[String], optCountry: Option[String], countryIsVN: Option[Boolean]): Future[Unit] = Profiler(s"$clazz._checkQuotaPhone2") {
    val fn = for {
      // check quota phone
      exceedQuotaPhone <- verifyDataService.isExceedQuotaPhone(phone, None)
      _ <- optTokenCaptcha match {
        case None if exceedQuotaPhone => Future.exception(ExceedQuotaException(s"captcha:missing,phone:true,country:${optCountry.getOrElse("undef")}"))
        case Some(tokenCaptcha) if exceedQuotaPhone =>
          captchaService.verifyCaptchaWithCache(tokenCaptcha).flatMap {
            case true =>
              optIp.foreach(realIP => verifyDataService.incrQuotaIP(realIP))
              Future.exception(IgnoreProcess())
            case _ => Future.exception(InvalidTokenCaptchaException(s"captcha:invalid,phone:true,country:${optCountry.getOrElse("undef")}"))
          }
        case _ => Future.Unit
      }

      // check quota ip
      _ <- optIp match {
        case Some(ip) =>
          verifyDataService.isExceedQuotaIP(ip) flatMap {
            case true if optTokenCaptcha.isEmpty => Future.exception(ExceedQuotaException(s"captcha:missing,ip:true,country:${optCountry.getOrElse("undef")}"))
            case true if optTokenCaptcha.isDefined =>
              captchaService.verifyCaptchaWithCache(optTokenCaptcha.get).flatMap {
                case true =>
                  optIp.foreach(realIP => verifyDataService.incrQuotaIP(realIP).liftToTry)
                  Future.exception(IgnoreProcess())
                case _ => Future.exception(InvalidTokenCaptchaException(s"captcha:invalid,ip:true,country:${optCountry.getOrElse("undef")}"))
              }
            case _ =>
              optIp.foreach(realIP => verifyDataService.incrQuotaIP(realIP).liftToTry)
              Future.Unit
          }
        case _ => Future.Unit
      }

      // check country
      _ <- {
        if (countryIsVN.forall(_ == true)) Future.Unit
        else if (optTokenCaptcha.isEmpty) Future.exception(ExceedQuotaException(s"captcha:missing,country:${optCountry.getOrElse("undef")}"))
        else {
          captchaService.verifyCaptchaWithCache(optTokenCaptcha.get).flatMap {
            case true => Future.exception(IgnoreProcess())
            case _ => Future.exception(InvalidTokenCaptchaException(s"captcha:invalid,country:${optCountry.getOrElse("undef")}"))
          }
        }
      }

    } yield {}

    fn.rescue {
      case _: IgnoreProcess => Future.Unit
    } respond {
      case Return(_) => Profiler(s"${getClass.getCanonicalName}.apply-true") {}
      case Throw(e) => e match {
        case e1: ExceedQuotaException => Profiler(s"${getClass.getCanonicalName}.apply-false-${e1.message}") {}
        case _ => Profiler(s"${getClass.getCanonicalName}.apply-false-${e.getClass.getCanonicalName}-${e.getMessage}") {}
      }
    }

  }

  //endregion

  //region Email

  override def checkQuotaEmail(request: Request, needCheckExist: Boolean, applyToExist: Boolean,
                               reqData: EmailQuotaJwtReq,
                               checkQuotaCountry: Boolean, checkQuotaIP: Boolean): Future[Unit] = Profiler(s"$clazz.checkQuotaEmail") {
    request.quotaChecked match {
      case true => Future.Unit
      case _ =>
        _checkQuotaEmail(request, needCheckExist, applyToExist, reqData, checkQuotaCountry, checkQuotaIP)
          .map(_ => request.setQuotaChecked())
    }
  }

  private def getValueIfNeeded[T](isNeed: Boolean, value: Option[T]): Option[T] = {
    if (isNeed) value else None
  }

  private def _checkQuotaEmail(request: Request, needCheckExist: Boolean, applyToExist: Boolean,
                               reqData: EmailQuotaJwtReq,
                               checkQuotaCountry: Boolean, checkQuotaIP: Boolean): Future[Unit] = Profiler(s"$clazz._checkQuotaEmail") {

    val optEmail = reqData.optQuotaEmail()

    val optCountry = getValueIfNeeded(checkQuotaCountry, request.optCountry)
    val countryIsVN = getValueIfNeeded(checkQuotaCountry, request.countryIsVN)

    val optIp = getValueIfNeeded(checkQuotaIP, request.optIP)

    val optTokenCaptcha = reqData.optTokenCaptcha()

    optEmail match {
      case Some(email) if needCheckExist =>
        profileService.getActiveUsernameByEmail(email).flatMap {
          case Some(_) if applyToExist => _checkQuotaEmail2(email, optTokenCaptcha, optIp, optCountry, countryIsVN)
          case None if !applyToExist => _checkQuotaEmail2(email, optTokenCaptcha, optIp, optCountry, countryIsVN)
          case _ => Future.Unit
        }
      case Some(email) => _checkQuotaEmail2(email, optTokenCaptcha, optIp, optCountry, countryIsVN)

      case _ => Future.Unit // Future.exception(ExceedQuotaException())
    }
  }

  private def _checkQuotaEmail2(email: String, optTokenCaptcha: Option[String],
                                optIp: Option[String], optCountry: Option[String], countryIsVN: Option[Boolean]): Future[Unit] = Profiler(s"$clazz._checkQuotaEmail2") {
    val fn = for {
      // check quota phone
      exceedQuotaEmail <- verifyDataService.isExceedQuotaEmail(email, None)
      _ <- optTokenCaptcha match {
        case None if exceedQuotaEmail => Future.exception(ExceedQuotaException(s"captcha:missing,email:true,country:${optCountry.getOrElse("undef")}"))
        case Some(tokenCaptcha) if exceedQuotaEmail =>
          captchaService.verifyCaptchaWithCache(tokenCaptcha).flatMap {
            case true => async {
              optIp.foreach(realIP => verifyDataService.incrQuotaIP(realIP))
              throw IgnoreProcess()
            }
            case _ => Future.exception(InvalidTokenCaptchaException(s"captcha:invalid,email:true,country:${optCountry.getOrElse("undef")}"))
          }
        case _ => Future.Unit
      }

      // check quota ip
      _ <- optIp match {
        case Some(ip) =>
          verifyDataService.isExceedQuotaIP(ip) flatMap {
            case true if optTokenCaptcha.isEmpty => Future.exception(ExceedQuotaException(s"captcha:missing,ip:true,country:${optCountry.getOrElse("undef")}"))
            case true if optTokenCaptcha.isDefined =>
              captchaService.verifyCaptchaWithCache(optTokenCaptcha.get).flatMap {
                case true => async {
                  optIp.foreach(realIP => verifyDataService.incrQuotaIP(realIP).liftToTry)
                  throw IgnoreProcess()
                }
                case _ => Future.exception(InvalidTokenCaptchaException(s"captcha:invalid,ip:true,country:${optCountry.getOrElse("undef")}"))
              }
            case _ => async {
              optIp.foreach(realIP => verifyDataService.incrQuotaIP(realIP).liftToTry)
            }
          }
        case _ => Future.Unit
      }

      // check country
      _ <- {
        if (countryIsVN.forall(_ == true)) Future.Unit
        else if (optTokenCaptcha.isEmpty) Future.exception(ExceedQuotaException(s"captcha:missing,country:${optCountry.getOrElse("undef")}"))
        else {
          captchaService.verifyCaptchaWithCache(optTokenCaptcha.get).flatMap {
            case true => Future.exception(IgnoreProcess())
            case _ => Future.exception(InvalidTokenCaptchaException(s"captcha:invalid,country:${optCountry.getOrElse("undef")}"))
          }
        }
      }

    } yield {}

    fn.rescue {
      case _: IgnoreProcess => Future.Unit
    } respond {
      case Return(_) => Profiler(s"${getClass.getCanonicalName}.apply-true") {}
      case Throw(e) => e match {
        case e1: ExceedQuotaException => Profiler(s"${getClass.getCanonicalName}.apply-false-${e1.message}") {}
        case _ => Profiler(s"${getClass.getCanonicalName}.apply-false-${e.getClass.getCanonicalName}-${e.getMessage}") {}
      }
    }
  }

  //endregion

  override def checkQuotaLoginPhone(reqData: PhoneQuotaJwtReq): Future[Unit] = Profiler(s"$clazz.checkQuotaLoginPhone") {
    for {
      exceedQuotaPhone <- reqData.optNormPhone() match {
        case Some(phone) => verifyDataService.isExceedQuotaPhone(phone, 10.toSome)
        case _ => Future.True
      }
      _ <- reqData.optTokenCaptcha() match {
        case None if exceedQuotaPhone => Future.exception(ExceedQuotaException(s"captcha:missing,phone:true,country:undef"))
        case Some(tokenCaptcha) if exceedQuotaPhone =>
          captchaService.verifyCaptchaWithCache(tokenCaptcha).map {
            case true =>
            case _ => throw InvalidTokenCaptchaException(s"captcha:invalid,phone:true,country:undef")
          }
        case _ => Future.Unit
      }
    } yield {}
  }

  override def calcQuotaPhoneMac[T](reqData: PhoneQuotaJwtReq, mac: String,
                                    ttl: Duration, numCheck: Int, deleteIfSuccess: Boolean)(fn: => Future[T]): Future[T] = Profiler(s"$clazz.checkQuotaPhoneMac") {
    for {

      exceedQuotaPhone <- reqData.optNormPhone() match {
        case Some(phone) =>
          verifyDataService.isExceedQuotaPhoneMac(phone, mac, numCheck)
            .respond(_ => verifyDataService.incrQuotaPhoneMac(phone, mac, ttl))
        case _ => Future.True
      }

      _ <- reqData.optTokenCaptcha() match {
        case None if exceedQuotaPhone => Future.exception(ExceedQuotaException(s"captcha:missing,phone:true,country:undef"))
        case Some(tokenCaptcha) if exceedQuotaPhone =>
          captchaService.verifyCaptchaWithCache(tokenCaptcha).map {
            case true =>
            case _ => throw InvalidTokenCaptchaException(s"captcha:invalid,phone:true,country:undef")
          }
        case _ => Future.Unit
      }

      result <- {
        fn
          .onSuccess(_ => reqData.optNormPhone().foreach(phone => if (deleteIfSuccess) verifyDataService.deleteQuotaPhoneMac(phone, mac)))
      }

    } yield result

  }

  override def calcQuotaEmailMac[T](reqData: EmailQuotaJwtReq, mac: String,
                                    ttl: Duration, numCheck: Int, deleteIfSuccess: Boolean)(fn: => Future[T]): Future[T] = Profiler(s"$clazz.calcQuotaEmailMac") {
    for {

      exceedQuotaEmail <- reqData.optQuotaEmail() match {
        case Some(email) =>
          verifyDataService.isExceedQuotaEmailMac(email, mac, numCheck)
            .respond(_ => verifyDataService.incrQuotaEmailMac(email, mac, ttl))
        case _ => Future.False
      }

      _ <- reqData.optTokenCaptcha() match {
        case None if exceedQuotaEmail => Future.exception(ExceedQuotaException(s"captcha:missing,email:true,country:undef"))
        case Some(tokenCaptcha) if exceedQuotaEmail =>
          captchaService.verifyCaptchaWithCache(tokenCaptcha).map {
            case true =>
            case _ => throw InvalidTokenCaptchaException(s"captcha:invalid,email:true,country:undef")
          }
        case _ => Future.Unit
      }

      result <- {
        fn
          .onSuccess(_ => reqData.optQuotaEmail().foreach(email => if (deleteIfSuccess) verifyDataService.deleteQuotaEmailMac(email, mac)))
      }

    } yield result

  }

  override def calcQuotaUserMac[T](reqData: UserQuotaJwtReq, mac: String,
                                   ttl: Duration, numCheck: Int, deleteIfSuccess: Boolean)(fn: => Future[T]): Future[T] = Profiler(s"$clazz.calcQuotaUserMac") {
    for {

      exceedQuotaUser <- verifyDataService.isExceedQuotaUserMac(reqData.quotaUsername(), mac, numCheck)
        .respond(_ => verifyDataService.incrQuotaUserMac(reqData.quotaUsername(), mac, ttl))

      _ <- reqData.optTokenCaptcha() match {
        case None if exceedQuotaUser => Future.exception(ExceedQuotaException(s"captcha:missing,user:true,country:undef"))
        case Some(tokenCaptcha) if exceedQuotaUser =>
          captchaService.verifyCaptchaWithCache(tokenCaptcha).map {
            case true =>
            case _ => throw InvalidTokenCaptchaException(s"captcha:invalid,user:true,country:undef")
          }
        case _ => Future.Unit
      }

      result <- {
        fn
          .onSuccess(_ => if (deleteIfSuccess) verifyDataService.deleteQuotaUserMac(reqData.quotaUsername(), mac))
      }

    } yield result

  }

  override def calcQuotaUserMac2[T](reqData: UserQuotaJwtReq, mac: String,
                                    ttl: Duration, numCheck: Int, deleteIfSuccess: Boolean)(fn: (Int, Int) => Future[T]): Future[T] = Profiler(s"$clazz.calcQuotaUserMac2") {
    for {

      exceedQuotaUser <- verifyDataService.isExceedQuotaUserMac(reqData.quotaUsername(), mac, numCheck)

      currCheck <- verifyDataService.incrQuotaUserMac(reqData.quotaUsername(), mac, ttl)

      _ <- reqData.optTokenCaptcha() match {
        case None if exceedQuotaUser => Future.exception(ExceedQuotaException(s"captcha:missing,user:true,country:undef"))
        case Some(tokenCaptcha) if exceedQuotaUser =>
          captchaService.verifyCaptchaWithCache(tokenCaptcha).map {
            case true =>
            case _ => throw InvalidTokenCaptchaException(s"captcha:invalid,user:true,country:undef")
          }
        case _ => Future.Unit
      }

      result <- {
        fn(currCheck, numCheck)
          .onSuccess(_ => if (deleteIfSuccess) verifyDataService.deleteQuotaUserMac(reqData.quotaUsername(), mac))
      }

    } yield result

  }

  override def calcQuotaUserMac3[T](request: Request,
                                    reqData: UserQuotaJwtReq, mac: String, ttlMac: Duration, numCheckMac: Int, deleteIfSuccess: Boolean,
                                    checkQuotaIP: Option[Boolean], ttlIp: Option[Duration], numCheckIp: Option[Int])(fn: => Future[T]): Future[T] = Profiler(s"$clazz.calcQuotaUserMac3") {
    val user = reqData.quotaUsername()
    val optIp = getValueIfNeeded(checkQuotaIP.getOrElse(false), request.optIP)

    for {
      exceedQuotaUser <- verifyDataService.isExceedQuotaUserMac(user, mac, numCheckMac)
        .respond(_ => verifyDataService.incrQuotaUserMac(user, mac, ttlMac))

      // check quota ip
      exceedQuotaIp <- optIp match {
        case Some(ip) =>
          verifyDataService.isExceedQuotaIP(ip, numCheckIp)
            .respond(_ => verifyDataService.incrQuotaIP(ip, ttlIp))
        case _ => Future.False
      }

      _ <- reqData.optTokenCaptcha() match {
        case None if exceedQuotaUser => Future.exception(ExceedQuotaException(s"captcha:missing,user:true,country:undef"))
        case None if exceedQuotaIp => Future.exception(ExceedQuotaException(s"captcha:missing,ip:true,country:undef"))
        case Some(tokenCaptcha) if exceedQuotaUser =>
          captchaService.verifyCaptchaWithCache(tokenCaptcha).map {
            case true =>
            case _ => throw InvalidTokenCaptchaException(s"captcha:invalid,user:true,country:undef")
          }
        case Some(tokenCaptcha) if exceedQuotaIp =>
          captchaService.verifyCaptchaWithCache(tokenCaptcha).map {
            case true =>
            case _ => throw InvalidTokenCaptchaException(s"captcha:invalid,ip:true,country:undef}")
          }
        case _ => Future.Unit
      }

      result <- {
        fn
          .onSuccess(_ => if (deleteIfSuccess) {
            for {
              _ <- verifyDataService.deleteQuotaUserMac(reqData.quotaUsername(), mac)
              _ <- optIp match {
                case Some(ip) => verifyDataService.deleteQuotaIP(ip)
                case _ => Future.Unit
              }
            } yield {}
          })
      }

    } yield result

  }

  override def checkQuotaLoginEmail(reqData: EmailQuotaJwtReq): Future[Unit] = Profiler(s"$clazz.checkQuotaLoginEmail") {
    for {
      exceedQuotaEmail <- reqData.optQuotaEmail() match {
        case Some(email) => verifyDataService.isExceedQuotaEmail(email, 10.toSome)
        case _ => Future.False
      }
      _ <- reqData.optTokenCaptcha() match {
        case None if exceedQuotaEmail => Future.exception(ExceedQuotaException(s"captcha:missing,email:true,country:undef"))
        case Some(tokenCaptcha) if exceedQuotaEmail =>
          captchaService.verifyCaptchaWithCache(tokenCaptcha).map {
            case true =>
            case _ => throw InvalidTokenCaptchaException(s"captcha:invalid,email:true,country:undef")
          }
        case _ => Future.Unit
      }
    } yield {}
  }

  override def calcQuotaEntityMac[T](entityType: String, entityId: String, mac: String,
                                     tokenCaptcha: Option[String],
                                     ttl: Duration, numCheck: Int, deleteIfSuccess: Boolean)(fn: => Future[T]): Future[T] = Profiler(s"$clazz.calcQuotaEntityMac") {
    for {

      exceedQuotaEntity <- verifyDataService.isExceedQuotaEntityMac(entityType, entityId, mac, numCheck)
        .respond(_ => verifyDataService.incrQuotaEntityMac(entityType, entityId, mac, ttl))

      _ <- tokenCaptcha match {
        case None if exceedQuotaEntity => Future.exception(ExceedQuotaException(s"captcha:missing,entity_type=$entityType,entity:true,country:undef"))
        case Some(tokenCaptcha) if exceedQuotaEntity =>
          captchaService.verifyCaptchaWithCache(tokenCaptcha).map {
            case true =>
            case _ => throw InvalidTokenCaptchaException(s"captcha:invalid,entity_type=$entityType,entity:true,country:undef")
          }
        case _ => Future.Unit
      }

      result <- {
        fn
          .onSuccess(_ => if (deleteIfSuccess) verifyDataService.deleteQuotaEntityMac(entityType, entityId, mac))
      }

    } yield result

  }

  override def calcQuotaPhoneVerifyCode[T](reqData: PhoneQuotaJwtReq, mac: String,
                                           ttl: Duration, numCheck: Int)(fn: => Future[T]): Future[T] = Profiler(s"$clazz.calcQuotaPhoneVerifyCode") {
    fn
  }

  override def calcQuotaEmailVerifyCode[T](reqData: EmailQuotaJwtReq, mac: String, ttl: Duration,
                                           numCheck: Int)(fn: => Future[T]): Future[T] = Profiler(s"$clazz.calcQuotaEmailVerifyCode") {
    fn
  }

  override def calcQuotaPhoneSendCode[T](request: Request, reqData: PhoneQuotaJwtReq, checkQuotaCountry: Boolean, checkQuotaIP: Boolean)(fn: => Future[T]): Future[T] = {
    fn
  }

  override def calcQuotaEmailSendCode[T](request: Request, reqData: EmailQuotaJwtReq, checkQuotaCountry: Boolean, checkQuotaIP: Boolean)(fn: => Future[T]): Future[T] = {
    fn
  }

  override def calcQuotaPhoneSendCode[T](reqData: PhoneQuotaJwtReq)(fn: => Future[T]): Future[T] = fn

  override def calcQuotaEmailSendCode[T](reqData: EmailQuotaJwtReq)(fn: => Future[T]): Future[T] = fn
}

