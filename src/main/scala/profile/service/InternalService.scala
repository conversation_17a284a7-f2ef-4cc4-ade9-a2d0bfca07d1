package profile.service

import com.google.common.cache.CacheBuilder
import com.twitter.inject.Logging
import com.twitter.util.Future
import com.typesafe.config.Config
import profile.domain.entity.Tier
import scalaj.http.Http
import vn.vhm.common.domain.Implicits.async
import vn.vhm.common.domain.profiling.Profiler
import vn.vhm.common.util.{<PERSON><PERSON><PERSON><PERSON><PERSON>, ZConfig}

import java.util.concurrent.TimeUnit
import scala.concurrent.duration.{DurationInt, DurationLong}

/**
 * <AUTHOR> 7/20/24 06:45
 */
trait InternalService {

  def getTiersCodeOrdered(): Future[Seq[Tier]]

  def getMasterDataValues(masterdataCode: String): Future[Map[String, Map[String, String]]]

  def getMasterDataIdentityRejectReason(): Future[Map[String, Map[String, String]]]

  def mgetSegmentCodes(ids: Seq[Long]): Future[Map[Long, String]]

}

case class InternalServiceImpl(coreConfig: Config) extends InternalService with Logging {

  private val clazz = getClass.getCanonicalName

  import vn.vhm.common.util.ZConfig.ImplicitConfig

  private val authzToken = coreConfig.getString("authz_token")
  private val connectTimeout = coreConfig.getInt("connect_timeout_ms", 30000)
  private val requestTimeout = coreConfig.getInt("request_timeout_ms", 30000)
  private val baseUrl = coreConfig.getString("base_url")

  private val tierListUrl = baseUrl + "/tiers"

  private val mgetSegmentUrls = baseUrl + "/internal/segments/mget"

  private def masterDataValuesUrl(mType: String) = {
    baseUrl + "/internal/master-data/type/" + mType
  }

  private val tierListCache = CacheBuilder.newBuilder()
    .expireAfterWrite(365, TimeUnit.DAYS)
    .maximumSize(3)
    .build[String, CacheKeyHolder[Seq[Tier]]]()
  private val refreshTierListCache = 1.hour.toMillis

  private val masterdataValuesCache = CacheBuilder.newBuilder()
    .expireAfterWrite(365, TimeUnit.DAYS)
    .maximumSize(500)
    .build[String, CacheKeyHolder[Map[String, Map[String, String]]]]()
  private val refreshMasterdataValuesCache = 1.hour.toMillis

  private val segmentCodesCache = CacheBuilder.newBuilder()
    .expireAfterWrite(365, TimeUnit.DAYS)
    .maximumSize(1000)
    .build[java.lang.Long, CacheKeyHolder[String]]()

  private val longRefreshSegmentCodesCache = ZConfig.getLong("cache.segment_codes.long_refresh_ttl_in_second", 300).seconds.toMillis
  private val shortRefreshSegmentCodesCache = ZConfig.getLong("cache.segment_codes.short_refresh_ttl_in_second", 30).seconds.toMillis

  override def getTiersCodeOrdered(): Future[Seq[Tier]] = Profiler(s"$clazz.getTiersCodeOrdered") {
    tierListCache.getIfPresent("-") match {
      case tiers if tiers != null && !tiers.isExpired => Profiler(s"$clazz.getTiersCodeOrdered-cacheHit") {
        Future.value(tiers.data)
      }
      case tiers if tiers != null && tiers.isExpired => Profiler(s"$clazz.getTiersCodeOrdered-cacheHitExpired") {
        _getTiersCodeOrdered().map { tiers =>
          if (tiers.nonEmpty) {
            tierListCache.put("-", CacheKeyHolder(tiers, ttl = refreshTierListCache))
          }
        }
        Future.value(tiers.data)
      }
      case null => Profiler(s"$clazz.getTiersCodeOrdered-cacheMiss") {
        _getTiersCodeOrdered().map { tiers =>
          info(s"getTiersCodeOrdered()\t${JsonHelper.toJson(tiers)}")
          tierListCache.put("-", CacheKeyHolder(tiers, ttl = refreshTierListCache))
          tiers
        }
      }
    }

  }

  private def _getTiersCodeOrdered(): Future[Seq[Tier]] = Profiler(s"$clazz._getTiersCodeOrdered") {
    async {
      val httpResp = Http(tierListUrl)
        .timeout(connTimeoutMs = connectTimeout, readTimeoutMs = requestTimeout)
        .header("Authorization", authzToken)
        .header("Content-Type", "application/json")
        .asString

      httpResp.code match {
        case 200 =>

          val result = JsonHelper.fromJson[VClubCoreResp[TierListResp]](httpResp.body)

          val tiers: Seq[Tier] = result.data.data
          val tierMap = tiers.map(tier => tier.code -> tier).toMap

          def getSortOrder(tier: Tier, visited: Set[String] = Set()): Int = {
            tier.previousTier match {
              case Some(prev) if !visited.contains(prev.code) =>
                1 + getSortOrder(tierMap(prev.code), visited + tier.code)
              case _ => 0
            }
          }

          tiers.sortBy(tier => getSortOrder(tier))

        case _ =>
          error(s"getTiersCodeOrdered\t${httpResp.code} - ${httpResp.body}")
          Nil
      }

    }
  }

  override def getMasterDataValues(masterdataCode: String): Future[Map[String, Map[String, String]]] = Profiler(s"$clazz.getMasterDataValues") {
    masterdataValuesCache.getIfPresent(masterdataCode) match {
      case values if values != null && !values.isExpired => Profiler(s"$clazz.getMasterDataValues-cacheHit") {
        Future.value(values.data)
      }
      case values if values != null && values.isExpired => Profiler(s"$clazz.getMasterDataValues-cacheHitExpired") {
        _getMasterDataValues(masterdataCode).map { values =>
          if (values.nonEmpty) {
            masterdataValuesCache.put(masterdataCode, CacheKeyHolder(values, ttl = refreshMasterdataValuesCache))
          }
        }
        Future.value(values.data)
      }
      case null => Profiler(s"$clazz.getMasterDataValues-cacheMiss") {
        _getMasterDataValues(masterdataCode).map { values =>
          info(s"getMasterDataValues($masterdataCode)\t${JsonHelper.toJson(values)}")
          if (masterdataCode.nonEmpty) {
            masterdataValuesCache.put(masterdataCode, CacheKeyHolder(values, ttl = refreshMasterdataValuesCache))
          }
          values
        }
      }
    }
  }

  private def _getMasterDataValues(masterdataCode: String): Future[Map[String, Map[String, String]]] = Profiler(s"$clazz._getMasterDataValues") {
    async {
      val resp = Http(masterDataValuesUrl(masterdataCode))
        .timeout(connTimeoutMs = connectTimeout, readTimeoutMs = requestTimeout)
        .header("Authorization", authzToken)
        .header("Content-Type", "application/json")
        .asString

      resp.code match {
        case 200 =>

          val result = JsonHelper.fromJson[VClubCoreResp[Seq[MasterDataValueResp]]](resp.body)

          result.data.map(item => item.id -> item.value).toMap

        case _ =>
          error(s"_getMasterDataValues($masterdataCode)\t${resp.code} - ${resp.body}")
          Map.empty[String, Map[String, String]]
      }

    } rescue {
      case e: Exception => async {
        error(s"_getMasterDataValues($masterdataCode)", e)
        Map.empty[String, Map[String, String]]
      }
    }
  }

  override def getMasterDataIdentityRejectReason(): Future[Map[String, Map[String, String]]] = getMasterDataValues("CUSTOMERS_IDENTITY_REJECT_REASON")

  override def mgetSegmentCodes(ids: Seq[Long]): Future[Map[Long, String]] = Profiler(s"$clazz.mgetSegmentCodes") {
    import scala.collection.JavaConverters._
    segmentCodesCache.getAllPresent(ids.map(long2Long).asJava).asScala match {
      case cachedValues if cachedValues.nonEmpty =>

        cachedValues.filter(_._2.isExpired).keys.toSeq.map(Long2long) match {
          case ids if ids.nonEmpty => Profiler(s"$clazz.mgetSegmentCodes-cacheHitPartial") {
            _mgetSegmentCodes(ids).foreach { result =>
              ids.foreach { id =>
                val code = result.getOrElse(id, "")
                val ttl = if (code.isEmpty) shortRefreshSegmentCodesCache else longRefreshSegmentCodesCache
                segmentCodesCache.put(id, CacheKeyHolder(code, ttl = ttl))
              }
              info(s"mgetSegmentCodes(${ids.mkString(",")})\tRefresh\t${JsonHelper.toJson(result)}")
            }
          }
          case _ =>
        }

        val idsNotInCache = ids.filterNot(id => cachedValues.contains(id))
        if (idsNotInCache.nonEmpty) {
          Profiler(s"$clazz.mgetSegmentCodes-cacheMissPartial") {
            _mgetSegmentCodes(idsNotInCache).map { newResult =>

              idsNotInCache.foreach { id =>
                val code = newResult.getOrElse(id, "")
                val ttl = if (code.isEmpty) shortRefreshSegmentCodesCache else longRefreshSegmentCodesCache
                segmentCodesCache.put(id, CacheKeyHolder(code, ttl = ttl))
              }

              info(s"mgetSegmentCodes(${idsNotInCache.mkString(",")})\tOldPartialEmpty\t${JsonHelper.toJson(newResult)}")
              newResult ++ cachedValues.map(pair => Long2long(pair._1) -> pair._2.data).toMap.filter(_._2.nonEmpty)
            }
          }
        } else Profiler(s"$clazz.mgetSegmentCodes-cacheHit") {
          Future.value(cachedValues.map(pair => Long2long(pair._1) -> pair._2.data).toMap)
        }
      case _ => Profiler(s"$clazz.mgetSegmentCodes-cacheMiss") {
        _mgetSegmentCodes(ids).map { newResult =>

          ids.foreach { id =>
            val code = newResult.getOrElse(id, "")
            val ttl = if (code.isEmpty) shortRefreshSegmentCodesCache else longRefreshSegmentCodesCache
            segmentCodesCache.put(id, CacheKeyHolder(code, ttl = ttl))
          }

          info(s"mgetSegmentCodes(${ids.mkString(",")})\tOldIsEmpty\t${JsonHelper.toJson(newResult)}")
          newResult
        }
      }
    }
  }

  private def _mgetSegmentCodes(ids: Seq[Long]): Future[Map[Long, String]] = Profiler(s"$clazz._mgetSegmentCodes") {
    async {
      val resp = Http(mgetSegmentUrls)
        .timeout(connTimeoutMs = connectTimeout, readTimeoutMs = requestTimeout)
        .header("Authorization", authzToken)
        .header("Content-Type", "application/json")
        .param("ids", ids.mkString(","))
        .asString

      resp.code match {
        case 200 =>

          val result = JsonHelper.fromJson[VClubCoreResp[Seq[SegmentResponse]]](resp.body)

          result.data.filter(_.isActive).map(item => item.id -> item.code).toMap

        case _ =>
          error(s"_mgetSegmentCodes(${ids.mkString(",")})\t${resp.code} - ${resp.body}")
          Map.empty[Long, String]
      }

    } rescue {
      case e: Exception => async {
        error(s"_mgetSegmentCodes(${ids.mkString(",")})", e)
        Map.empty[Long, String]
      }
    }
  }

}


case class VClubCoreResp[T](code: Int, message: Seq[String], data: T)

case class TierListResp(data: Seq[Tier])

case class MasterDataValueResp(id: String, value: Map[String, String])

case class SegmentResponse(id: Long, code: String, status: String, active: Boolean) {
  def isActive: Boolean = active && status == "ACTIVE"
}

case class CacheKeyHolder[T](data: T, ttl: Long = 15.minute.toMillis, timestamp: Long = System.currentTimeMillis()) {

  def isExpired: Boolean = timestamp + ttl < System.currentTimeMillis()

}