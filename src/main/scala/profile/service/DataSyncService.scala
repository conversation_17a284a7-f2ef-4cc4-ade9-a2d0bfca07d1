package profile.service

import com.fasterxml.jackson.databind.JsonNode
import com.fasterxml.jackson.databind.node.ObjectNode
import com.google.inject.name.Named
import com.twitter.inject.Logging
import com.twitter.util.Future
import profile.domain.customer._
import profile.util.Constant
import vn.vhm.common.client.ssdb.SSDBClient
import vn.vhm.common.domain.Implicits.async
import vn.vhm.common.domain.OptionImplicits.OptionImplicitAny
import vn.vhm.common.domain.profiling.Profiler
import vn.vhm.common.exception.NotFoundException
import vn.vhm.common.service.DebugServiceNotification
import vn.vhm.common.util.{J<PERSON><PERSON><PERSON><PERSON>, Utils, ZConfig}

import scala.collection.JavaConverters._

/**
 * <AUTHOR> 7/29/24 16:39
 */
case class DataSyncService(
                            profileService: ProfileService,
                            @Named("data") dataSsdbClient: SSDBClient,
                            customerDAO: CustomerDAO) extends Logging {

  protected val clazz: String = getClass.getCanonicalName

  private val hashCustomerRankingKey = ZConfig.getString("ssdb.data.hash_customer_ranking")
  private val zsetCustomerRankingKey = ZConfig.getString("ssdb.data.zset_customer_ranking")

  private val hashCdpProfile = ZConfig.getString("ssdb.data.hash_cdp_profile")
  private val zsetCdpProfile = ZConfig.getString("ssdb.data.zset_cdp_profile")

  def singleSyncCustomerRanking(keyStart: String, fromTime: Long, toTime: Option[Long],
                                fnUpdateSyncTime: (Long, String) => Unit): Future[Option[Long]] = Profiler(s"$clazz.singleSyncCustomerRanking") {

    def parseDataPnlMetric(metricsNode: JsonNode): Map[String, CustomerPnlRankingProgressDataMetric] = {
      metricsNode match {
        case metricsNode: ObjectNode =>
          metricsNode.fieldNames().asScala.map(metricName => metricName -> {
            metricsNode.at(s"/$metricName/type").asText("") match {
              case x if x.equalsIgnoreCase("long") =>
                CustomerPnlRankingProgressDataMetric(metric = metricName, valueType = "long".toSome, valueL = metricsNode.at(s"/$metricName/value").asLong(0).toSome)
              case x if x.equalsIgnoreCase("int") || x.equalsIgnoreCase("integer") =>
                CustomerPnlRankingProgressDataMetric(metric = metricName, valueType = "int".toSome, valueI = metricsNode.at(s"/$metricName/value").asInt(0).toSome)
              case x if x.equalsIgnoreCase("double") =>
                CustomerPnlRankingProgressDataMetric(metric = metricName, valueType = "double".toSome, valueD = metricsNode.at(s"/$metricName/value").asDouble(0d).toSome)
              case x =>
                CustomerPnlRankingProgressDataMetric(metric = metricName, valueType = x.toSome, valueS = metricsNode.at(s"/$metricName/value").asText("").toSome)
            }
          }).toMap
        case _ => Map.empty[String, CustomerPnlRankingProgressDataMetric]
      }
    }

    def parseDataPnlProgress(pnlsRankingDataAsNode: JsonNode): Map[String, CustomerPnlRankingProgressData] = {
      Constant.listPnl.flatMap(pnl => {
        val metrics = parseDataPnlMetric(pnlsRankingDataAsNode.at(s"/$pnl/metrics"))
        val updatedTime = 0L
        val tierCode = Some(pnlsRankingDataAsNode.at(s"/$pnl/pnl_rank_id").asInt(-1))
          .filter(_ >= 0)
          .flatMap(parseTierCode)
        if (metrics.nonEmpty) {
          val pnlRankingData = CustomerPnlRankingProgressData(metrics = metrics, updatedTime = updatedTime)
          pnlRankingData.tierCode = tierCode
          (pnl, pnlRankingData).toSome
        }
        else None
      }).toMap
    }

    def parseTierCode(rankId: Int): Option[String] = {
      rankId match {
        case 0 => "MEMBER".toSome
        case 1 => "GOLD".toSome
        case 2 => "PLATINUM".toSome
        case 3 => "DIAMOND".toSome
        case _ => None
      }
    }

    val reqSize = 8
    val fn = for {

      newCustomerRankings <- async {
        val r = dataSsdbClient.exec(_.zscan(zsetCustomerRankingKey, keyStart, fromTime, toTime.getOrElse(System.currentTimeMillis()), reqSize))
        if (!r.ok)
          Nil
        else
          r.listString().asScala.grouped(2).toSeq.map(seq => (seq.head, seq.last.toLong))
      }

      //      _ <- FutureUtils.processUntilEmpty[(String, Long), Unit](newCustomerRankings, newCustomerRanking => Profiler(s"$clazz.singleSyncCustomerRanking - item") {
      //        dataSsdbClient.hGetString(hashCustomerRankingKey, newCustomerRanking._1) match {
      //          case Some(rankingDataAsString) =>
      //            val rankingDataAsNode = JsonHelper.readTree(rankingDataAsString)
      //            val pnlRankingProgress = CustomerPnlRankingProgress(
      //              updatedTime = rankingDataAsNode.at("/updated_time").asLong(0L),
      //              pnlProgress = parseDataPnlProgress(rankingDataAsNode.at("/data/progress_rank_data/pnl_rank_data")),
      //              tierCode = Some(rankingDataAsNode.at("/data/rank_id").asInt(-1))
      //                .filter(_ >= 0)
      //                .orElse(Some(rankingDataAsNode.at("/data/progress_rank_data/rank_id").asInt(-1)).filter(_ >= 0))
      //                .flatMap(parseTierCode)
      //            )
      //            if (pnlRankingProgress.pnlProgress.nonEmpty)
      //              profileService.updateRankingProgress(newCustomerRanking._1, pnlRankingProgress, None)
      //                .rescue {
      //                  case _: NotFoundException => async {
      //                    error(s"singleSyncCustomerRanking($fromTime, $toTime) - not found customer => Ignore: ${newCustomerRanking._1}")
      //                  }
      //                }
      //            else
      //              Future.Unit
      //
      //          case _ =>
      //            Future.Unit
      //        }
      //      })

      _ <- Future.join(newCustomerRankings.map(newCustomerRanking => Profiler(s"$clazz.singleSyncCustomerRanking - item") {
        dataSsdbClient.hGetString(hashCustomerRankingKey, newCustomerRanking._1) match {
          case Some(rankingDataAsString) =>
            val rankingDataAsNode = JsonHelper.readTree(rankingDataAsString)
            val pnlRankingProgress = CustomerPnlRankingProgress(
              updatedTime = rankingDataAsNode.at("/updated_time").asLong(0L),
              pnlProgress = parseDataPnlProgress(rankingDataAsNode.at("/data/progress_rank_data/pnl_rank_data")),
              tierCode = Some(rankingDataAsNode.at("/data/rank_id").asInt(-1))
                .filter(_ >= 0)
                .orElse(Some(rankingDataAsNode.at("/data/progress_rank_data/rank_id").asInt(-1)).filter(_ >= 0))
                .flatMap(parseTierCode),
              dataTime = Some(rankingDataAsNode.at("/data_time").asLong(-1L)).filter(_ >= 0)
            )
            if (pnlRankingProgress.pnlProgress.nonEmpty)
              profileService.updateRankingProgress(newCustomerRanking._1, pnlRankingProgress, None)
                .rescue {
                  case _: NotFoundException => async {
                    error(s"singleSyncCustomerRanking($fromTime, $toTime) - not found customer => Ignore: ${newCustomerRanking._1}")
                  }
                }
            else
              Future.Unit

          case _ =>
            Future.Unit
        }
      }))

      params = {
        val nextItem = newCustomerRankings.lastOption
        nextItem.foreach(_nextItem => fnUpdateSyncTime(_nextItem._2, _nextItem._1))

        // Calc for next scroll
        if (newCustomerRankings.size == reqSize) { // have other items
          Some(nextItem.get._1, nextItem.get._2, toTime)
        } else {
          None
        }
      }
      //      r <- params match {
      //        case Some(_params) => singleSyncCustomerRanking(_params._1, _params._2, _params._3, fnUpdateSyncTime)
      //        case _ => Future.Done
      //      }

      r <- params match {
        case Some(_params) => Future.value(Some(_params._2))
        case _ => Future.None
      }

    } yield r

    fn.onFailure {
      case e: Exception =>
        error(s"singleSyncCustomerRanking($fromTime, $toTime)", e)
        if (System.currentTimeMillis() % 10 == 0) {
          DebugServiceNotification.notifyTelegram(s"singleSyncCustomerRanking($fromTime, $toTime)-error=${Utils.getStackTraceAsString(e)}")
        }
    }
  }

  def singleSyncCustomerCdpProfile(keyStart: String, fromTime: Long, toTime: Option[Long],
                                   fnUpdateSyncTime: (Long, String) => Unit): Future[Option[Long]] = Profiler(s"$clazz.singleSyncCustomerCdpProfile") {

    def parseDataCdpProfile(cdpProfileDataAsNode: JsonNode): CustomerCdpProfile = {
      val createdTime = cdpProfileDataAsNode.at("/created_time").asLong(0L)
      val updatedTime = cdpProfileDataAsNode.at("/updated_time").asLong(0L)

      val firstAppInteractionTime = cdpProfileDataAsNode.at("/first_app_interaction_time").asLong(-1L).toSome.filter(_ >= 0L)
      val lastAppInteractionTime = cdpProfileDataAsNode.at("/last_app_interaction_time").asLong(-1L).toSome.filter(_ >= 0L)

      val pnlSpendingAmount = Constant.listPnl.map(pnl => {
        val amount = cdpProfileDataAsNode.at(s"/clv_metrics/$pnl/clv").asLong(0L)
        (pnl, amount)
      }).toMap

      val livingAddress = CustomerLivingAddress(
        continent = cdpProfileDataAsNode.at("/living_address/continent").asText("").toSome,
        continentCode = cdpProfileDataAsNode.at("/living_address/continent_code").asText("").toSome,
        country = cdpProfileDataAsNode.at("/living_address/country").asText("").toSome,
        countryCode = cdpProfileDataAsNode.at("/living_address/country_code").asText("").toSome,
        regionId = cdpProfileDataAsNode.at("/living_address/region_id").asInt(-1).toSome.filter(_ >= 0),
        region = cdpProfileDataAsNode.at("/living_address/region").asText("").toSome,
        provinceId = cdpProfileDataAsNode.at("/living_address/province_id").asInt(-1).toSome.filter(_ >= 0),
        province = cdpProfileDataAsNode.at("/living_address/province").asText("").toSome,
        cityId = cdpProfileDataAsNode.at("/living_address/city_id").asInt(-1).toSome.filter(_ >= 0),
        city = cdpProfileDataAsNode.at("/living_address/city").asText("").toSome
      )

      val scores = Constant.listCustomerScoreType.flatMap(typeName => {
        val score = cdpProfileDataAsNode.at(s"/scores/$typeName/score").asText("")
        val rank = cdpProfileDataAsNode.at(s"/scores/$typeName/rank").asText("")
        if (score.nonEmpty || rank.nonEmpty) {
          (typeName, CustomerScore(score = score.toSome, rank = rank.toSome)).toSome
        } else {
          None
        }
      }).toMap

      CustomerCdpProfile(
        createdTime = createdTime,
        updatedTime = updatedTime,
        firstAppInteractionTime = firstAppInteractionTime,
        lastAppInteractionTime = lastAppInteractionTime,
        pnlSpendingAmount = pnlSpendingAmount,
        livingAddress = livingAddress,
        scores = scores
      )
    }

    val reqSize = 8
    val fn = for {

      newCustomerCdpProfiles <- async {
        val r = dataSsdbClient.exec(_.zscan(zsetCdpProfile, keyStart, fromTime, toTime.getOrElse(System.currentTimeMillis()), reqSize))
        if (!r.ok)
          Nil
        else
          r.listString().asScala.grouped(2).toSeq.map(seq => (seq.head, seq.last.toLong))
      }

      _ <- Future.join(newCustomerCdpProfiles.map(newCustomerCdpProfile => Profiler(s"$clazz.singleSyncCustomerCdpProfile - item") {
        val username = newCustomerCdpProfile._1
        dataSsdbClient.hGetString(hashCdpProfile, username) match {
          case Some(cdpProfileDataAsString) =>
            val cdpProflieDataAsNode = JsonHelper.readTree(cdpProfileDataAsString)
            val customerCdpProfile = parseDataCdpProfile(cdpProflieDataAsNode.at("/data"))

            // TODO: change to batch update
            profileService.updateCdpProfileDemographic(username, customerCdpProfile, None)
              .rescue {
                case _: NotFoundException => async {
                  error(s"singleSyncCustomerCdpProfile($fromTime, $toTime) - not found customer => Ignore: $username")
                }
              }
          case _ =>
            Future.Unit
        }
      }))

      params = {
        val nextItem = newCustomerCdpProfiles.lastOption
        nextItem.foreach(_nextItem => fnUpdateSyncTime(_nextItem._2, _nextItem._1))

        // Calc for next scroll
        if (newCustomerCdpProfiles.size == reqSize) { // have other items
          Some(nextItem.get._1, nextItem.get._2, toTime)
        } else {
          None
        }
      }

      r <- params match {
        case Some(_params) => Future.value(Some(_params._2))
        case _ => Future.None
      }

    } yield r

    fn.onFailure {
      case e: Exception =>
        error(s"singleSyncCustomerCdpProfile($fromTime, $toTime)", e)
        if (System.currentTimeMillis() % 10 == 0) {
          DebugServiceNotification.notifyTelegram(s"singleSyncCustomerCdpProfile($fromTime, $toTime)-error=${Utils.getStackTraceAsString(e)}")
        }
    }
  }

  def reInitRankingDay0(userIdStart: Long, userIdTo: Long, fnUpdateSyncTime: (Long, Long) => Unit): Future[Option[Long]] = Profiler(s"$clazz.reInitRankingDay0") {
    val reqSize = 8
    val fn = for {

      customers <- async {
        customerDAO.selectActiveWithGtId(userIdStart, userIdTo, reqSize)
      }

      _ <- Future.join(customers.map(customer => Profiler(s"$clazz.reInitRankingDay0 - item") {
        profileService.reInitPnlMapping(customer, None, None, None)
      }))

      params = {
        val nextItem = customers.lastOption
        nextItem.foreach(_nextItem => fnUpdateSyncTime(_nextItem.createdTime.get, _nextItem.userId.get.toLong))

        // Calc for next scroll
        if (customers.size == reqSize) { // have other items
          Some(nextItem.get.createdTime.get, nextItem.get.userId.get.toLong)
        } else {
          None
        }
      }

      //      r <- params match {
      //        case Some(_params) => Future.value(Some(_params._2))
      //        case _ => Future.None
      //      }
      r = params.map(_._2)
    }

    yield r

    fn.onFailure {
      case e: Exception =>
        error(s"reInitRankingDay0($userIdStart)", e)
        if (System.currentTimeMillis() % 10 == 0) {
          DebugServiceNotification.notifyTelegram(s"reInitRankingDay0($userIdStart)-error=${Utils.getStackTraceAsString(e)}")
        }
    }
  }

  def reScanRankingDay0(userIdStart: Long, userIdTo: Long, fnUpdateSyncTime: (Long, Long) => Unit): Future[Option[Long]] = Profiler(s"$clazz.reScanRankingDay0") {
    val reqSize = 2
    val fn = for {

      customers <- async {
        customerDAO.selectActiveWithGtId(userIdStart, userIdTo, reqSize)
      }

      _ <- Future.join(customers.map(customer => Profiler(s"$clazz.reScanRankingDay0 - item") {
        profileService.getPnlMappings(customer.userId.get, true, Nil)
      }))

      params = {
        val nextItem = customers.lastOption
        nextItem.foreach(_nextItem => fnUpdateSyncTime(_nextItem.createdTime.get, _nextItem.userId.get.toLong))

        // Calc for next scroll
        if (customers.size == reqSize) { // have other items
          Some(nextItem.get.createdTime.get, nextItem.get.userId.get.toLong)
        } else {
          None
        }
      }

      r
        <- params match {
        case Some(_params) => Future.value(Some(_params._2))
        case _ => Future.None
      }

    }

    yield r

    fn.onFailure {
      case e: Exception =>
        error(s"reScanRankingDay0($userIdStart)", e)
        if (System.currentTimeMillis() % 10 == 0) {
          DebugServiceNotification.notifyTelegram(s"reScanRankingDay0($userIdStart)-error=${Utils.getStackTraceAsString(e)}")
        }
    }
  }
}