//package profile.service
//
//import com.twitter.inject.Logging
//import vn.vhm.common.exception.VhmException
//
//import java.util.Base64
//import javax.crypto.Cipher
//import javax.crypto.spec.SecretKeySpec
//
///**
// * <AUTHOR> 8/27/24 13:07
// */
//case class InvalidFileIdException() extends VhmException {
//  override def error: String = "invalid_file_id"
//}
//
//case class FileIdEncoder(combineKey: String, encryptKey: String) extends Logging {
//
//  lazy val _key = new SecretKeySpec(encryptKey.getBytes("UTF-8"), "AES")
//  private val algorithm = "AES/ECB/PKCS5PADDING"
//
//  def encode(rawId: String): String = {
//    val aes = Cipher.getInstance(algorithm)
//    aes.init(1, _key)
//    Base64.getUrlEncoder.encodeToString(aes.doFinal(rawId.getBytes))
//  }
//
//  def decode(publicId: String): Option[String] = {
//    try {
//      val aes = Cipher.getInstance(algorithm)
//      aes.init(2, _key)
//      val decrypted = aes.doFinal(Base64.getUrlDecoder.decode(publicId))
//      val encode = new String(decrypted, "utf-8")
//      Some(encode)
//    } catch {
//      case e: InvalidFileIdException =>
//        warn(s"decode($publicId): InvalidFileId - maybe cheat", e)
//        None
//      case e: Exception =>
//        error(s"decode($publicId)", e)
//        None
//    }
//  }
//
//}
