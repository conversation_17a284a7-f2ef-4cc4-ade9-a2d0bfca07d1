package profile.service

import com.twitter.util.Local

/**
 * <AUTHOR> 3/29/25 11:52
 */
case class ContextInfo(lang: Option[String])

trait ContextHolder {

  def setContext[B](lang: Option[String])(fn: => B): B

  def getLang(): Option[String]

}

case class ContextHolderImpl() extends ContextHolder{

  val local = new Local[Option[ContextInfo]]()

  override def setContext[B](lang: Option[String])(fn: => B): B = {
    local.let(Some(ContextInfo(lang = lang)))(fn)
  }

  override def getLang(): Option[String] = local().flatten.flatMap(_.lang)

}