package profile.service

import com.google.inject.Inject
import com.twitter.inject.Logging
import com.twitter.util.{Future, Return, Throw}
import core.message_delivery.service.DeliveryCodeService
import org.apache.commons.codec.CharEncoding
import org.apache.commons.io.FileUtils
import org.slf4j.LoggerFactory
import profile.controller.http.request.RegisterByAdminData
import profile.domain.entity.{PlatForm, VerifyCodeInfo}
import profile.exception._
import profile.util.{Constant, CustomUtils}
import vn.vhm.common.domain.Implicits.async
import vn.vhm.common.domain.OptionImplicits.OptionImplicitAny
import vn.vhm.common.domain.profiling.Profiler
import vn.vhm.common.exception.NotFoundException
import vn.vhm.common.repository.cache.RedisCacheCommon
import vn.vhm.common.util.{JsonHelper, ZConfig}

import java.io.File
import java.util.UUID
import scala.collection.JavaConverters._
import scala.concurrent.duration.{Duration, DurationInt}

/**
 * <AUTHOR>
 */
trait VerifyDataService {

  //region PHONE

  def genAndSendCodeToPhone(phone: String, mac: String, platform: Option[String] = None): Future[Unit]

  def sendCodeToPhone(normPhone: String, code: String, platform: Option[String]): Future[Unit]

  def verifyCodeWithPhone(phone: String, mac: String, code: String, deleteIfEqual: Boolean): Future[Unit]

  def deleteVerifyCodeWithPhone(phone: String): Future[Unit]

  def deleteVerifyCodeWithPhone(phone: String, mac: String): Future[Unit]

  def isExceedQuotaPhone(phone: String, numCheck: Option[Int]): Future[Boolean]

  def incrQuotaPhone(phone: String): Future[Int]

  def deleteQuotaPhone(phone: String): Future[Unit]

  def genTokenPhone(phone: String): Future[String]

  def genTokenPhone(phone: String, mac: String): Future[String]

  def genToken(entityId: String, mac: String): Future[String]

  def verifyTokenPhone(token: String, delete: Boolean): Future[String]

  def verifyTokenPhone(phone: String, token: String, mac: String, delete: Boolean): Future[Unit]

  def verifyToken(entityId: String, token: String, mac: String, delete: Boolean): Future[Unit]

  def deleteTokenPhone(token: String): Future[Unit]

  def deleteTokenPhone(token: String, mac: String): Future[Unit]

  //endregion

  //region EMAIL

  def genAndSendCodeToEmail(email: String, mac: String, name: Option[String], isForgot: Boolean): Future[Unit]

  def sendCodeToEmail(email: String, code: String, name: Option[String], isForgot: Boolean): Future[Unit]

  def verifyCodeWithEmail(email: String, code: String, mac: String, delete: Boolean): Future[Unit]

  def deleteVerifyCodeWithEmail(email: String, mac: String): Future[Unit]

  def isExceedQuotaEmail(email: String, numCheck: Option[Int]): Future[Boolean]

  def incrQuotaEmail(email: String): Future[Int]

  def deleteQuotaEmail(email: String): Future[Unit]

  //endregion

  //region IP

  def isExceedQuotaIP(ip: String, numCheck: Option[Int] = None): Future[Boolean]

  def incrQuotaIP(ip: String, ttl: Option[Duration] = None): Future[Int]

  def deleteQuotaIP(ip: String): Future[Unit]

  def getRemainingTtlQuotaIP(ip: String): Future[Option[Int]]

  //endregion

  //region Quota with MAC

  def isExceedQuotaPhoneMac(phone: String, mac: String, numCheck: Int): Future[Boolean]

  def incrQuotaPhoneMac(phone: String, mac: String, ttl: Duration): Future[Int]

  def deleteQuotaPhoneMac(phone: String, mac: String): Future[Unit]

  def isExceedQuotaEmailMac(email: String, mac: String, numCheck: Int): Future[Boolean]

  def incrQuotaEmailMac(email: String, mac: String, ttl: Duration): Future[Int]

  def deleteQuotaEmailMac(email: String, mac: String): Future[Unit]

  def isExceedQuotaUserMac(username: String, mac: String, numCheck: Int): Future[Boolean]

  def incrQuotaUserMac(username: String, mac: String, ttl: Duration): Future[Int]

  def deleteQuotaUserMac(username: String, mac: String): Future[Unit]

  def getRemainingTtlQuotaUserMac(username: String, mac: String): Future[Option[Int]]

  //endregion

  //region Entity with MAC

  def isExceedQuotaEntityMac(entityType: String, entityId: String, mac: String, numCheck: Int): Future[Boolean]

  def incrQuotaEntityMac(entityType: String, entityId: String, mac: String, ttl: Duration): Future[Unit]

  def deleteQuotaEntityMac(entityType: String, entityId: String, mac: String): Future[Unit]

  def buildEntityWithLockTime(entityType: String, entityId: String, mac: String, lockTimeIn: Duration): Future[Long]

  def getLockTimeByEntity(entityType: String, entityId: String, mac: String): Future[Option[Long]]

  def deleteLockTimeByEntity(entityType: String, entityId: String, mac: String): Future[Unit]

  //endregion

  def genPnlRequestToken(request: RegisterByAdminData): Future[Unit]

  def verifyAdminRequestToken[T](request: RegisterByAdminData)(fn: => Future[T]): Future[T]

  def getVerifyCodeExpireTimeInSecond: Int

}

case class TemplateHolder(
                           defaultTemplate: String,
                           templateForAndroid: String,
                           templateForIos: String
                         )

class VerifyCodeServiceImpl @Inject()(
                                       keyValueRepo: RedisCacheCommon,
                                       deliveryCodeService: DeliveryCodeService,
                                     ) extends VerifyDataService with Logging {

  private val clazz = getClass.getCanonicalName

  private val verifyDataLogger = LoggerFactory.getLogger("VerifyDataLogger")
  protected val cacheOtpPrefix = Constant.CACHE_CUSTOMER_OTP
  protected val cacheQuotaPrefix = Constant.CACHE_CUSTOMER_QUOTA
  protected val cacheLockPrefix = Constant.CACHE_CUSTOMER_LOCK

  //region Phone config

  protected val verifyCodeExpireTimeInSecond: Int = ZConfig.getInt("verify_phonenumber_service.code_expire_time_in_second", 3 * 60)
  private val smsCodeTemplateDefault: String = ZConfig.getString("verify_phonenumber_service.message_template", "Use $code as your verify code for VinClub")
  private val smsCodeTemplateForAndroidDefault: String = ZConfig.getString("verify_phonenumber_service.message_template_for_android", "Use $code as your verify code for VinClub")
  private val smsCodeTemplateForIosDefault: String = ZConfig.getString("verify_phonenumber_service.message_template_for_ios", "Use $code as your verify code for VinClub")

  private val mapSmsCodeTemplateByPrefix: Map[Set[String], TemplateHolder] = {
    ZConfig.getSeqMap("verify_phonenumber_service.message_template_by_prefix", Nil).map(map => {
      val prefixes = map("prefix_numbers").asInstanceOf[java.util.List[String]].asScala.toSet
      val template = map("message_template").asInstanceOf[String]
      val templateForAndroid = map("message_template_for_android").asInstanceOf[String]
      val templateForIos = map("message_template_for_ios").asInstanceOf[String]
      prefixes -> TemplateHolder(defaultTemplate = template, templateForAndroid = templateForAndroid, templateForIos = templateForIos)
    }).toMap
  }

  private val sendSMSStatsLogger = LoggerFactory.getLogger("SendSMSStatsLogger")

  //endregion

  //region Email config

  private val emailVerifySubjectTemplate = ZConfig.getString("email.send_otp_subject", "[VinClub] Mã xác minh email")
  private val emailVerifyBodyTemplate = FileUtils.readLines(new File("conf/template/email-verify-code.html"), CharEncoding.UTF_8).asScala.mkString("\n")

  private val emailReVerifySubjectTemplate = ZConfig.getString("email.send_otp_reverify_subject", "VinClub - Mã xác thực thiết lập lại mã PIN")
  private val emailReVerifyBodyTemplate = FileUtils.readLines(new File("conf/template/email-reverify-code.html"), CharEncoding.UTF_8).asScala.mkString("\n")

  //endregion

  //region Otp config

  private val mapOtpConstant = ZConfig.getMapString("verify_phonenumber_service.constant_otp", Map.empty)

  //endregion

  //region Limit config

  private val limitQuotaPhone = ZConfig.getInt("sms.quota_phonenumber", 10)
  private val phoneQuotaExpireTimeInSecond = ZConfig.getInt("sms.quota_phonenumber_expire_time_in_second", 2.hour.toSeconds.toInt)

  private val limitQuotaEmail = ZConfig.getInt("email.quota_email", 10)
  private val emailQuotaExpireTimeInSecond = ZConfig.getInt("email.quota_email_expire_time_in_second", 2.hour.toSeconds.toInt)

  private val limitQuotaIP = ZConfig.getInt("common.quota_ip", 20)
  private val ipQuotaExpireTimeInSecond = ZConfig.getInt("common.quota_ip_expire_time_in_second", 2.hour.toSeconds.toInt)

  //endregion

  override def genAndSendCodeToPhone(phone: String, mac: String, platform: Option[String] = None): Future[Unit] = Profiler(s"$clazz.genAndSendCodeToPhone") {
    for {
      code <- genVerifyCodeWithPhone(phone, mac)
      _ = sendCodeToPhone(phone, code, platform)
    } yield {}
  }

  override def verifyCodeWithPhone(phone: String, mac: String, code: String, deleteIfEqual: Boolean): Future[Unit] = Profiler(s"$clazz.verifyCodeWithPhone") {
    val key = buildVerifyCodeKeyWithPhone(phone, mac)
    keyValueRepo.cacheGet(cacheOtpPrefix, key).flatMap({
      case Some(x) if x.equals(code) =>
        if (deleteIfEqual) async(keyValueRepo.cacheDel(cacheOtpPrefix, key))
          .onFailure(ex => logger.error("Failed to delete verify phone code", ex))
          .unit
        else Future.Unit
      case Some(_) =>
        verifyDataLogger.warn(s"verifyCodeWithPhone($phone, $mac, $code, $deleteIfEqual): Failed Some(_)")
        keyValueRepo.cacheGetTtlInSecond(cacheOtpPrefix, key).map(_.map(_.toInt).getOrElse(0))
          .map(result => throw InvalidVerifyPhoneCodeException(expiredIn = result))
      case _ =>
        verifyDataLogger.warn(s"verifyCodeWithPhone($phone, $mac, $code, $deleteIfEqual): Failed None")
        Future.exception(InvalidVerifyPhoneCodeException(isExpiredCode = true))
    })
  }

  override def deleteVerifyCodeWithPhone(phone: String): Future[Unit] = Profiler(s"$clazz.deleteVerifyCodeWithPhone") {
    val key = buildVerifyCodeKeyWithPhone(phone)
    keyValueRepo.cacheDel(cacheOtpPrefix, key)
      .onFailure(ex => logger.error("Failed to delete verify phone code", ex))
      .unit
  }

  override def deleteVerifyCodeWithPhone(phone: String, mac: String): Future[Unit] = {
    val key = buildVerifyCodeKeyWithPhone(phone, mac)
    keyValueRepo.cacheDel(cacheOtpPrefix, key)
      .onFailure(ex => logger.error("Failed to delete verify phone code", ex))
      .unit
  }

  private def _genCodeForPhone(phoneNumber: String): String = {
    mapOtpConstant.getOrElse(phoneNumber, CustomUtils.randomInt(100000, 1000000).toString)
  }

  def genVerifyCodeWithPhone(phone: String, mac: String): Future[String] = {
    val code = _genCodeForPhone(phone)
    val verifyCodeKey = buildVerifyCodeKeyWithPhone(phone, mac)
    keyValueRepo.cacheSet(cacheOtpPrefix, verifyCodeKey, verifyCodeExpireTimeInSecond, code)
      .map(_ => code)
  }

  override def genTokenPhone(phone: String): Future[String] = {
    val token = UUID.randomUUID().toString
    val key = buildTokenPhoneInfoKey(token)
    keyValueRepo.cacheSet(cacheOtpPrefix, key, 3 * verifyCodeExpireTimeInSecond, phone).map(_ => token)
  }

  override def genTokenPhone(phone: String, mac: String): Future[String] = {
    val token = UUID.randomUUID().toString
    val key = buildTokenPhoneInfoKey(token, mac)
    keyValueRepo.cacheSet(cacheOtpPrefix, key, 3 * verifyCodeExpireTimeInSecond, phone).map(_ => token)
  }

  override def genToken(entityId: String, mac: String): Future[String] = {
    val token = UUID.randomUUID().toString
    val key = buildTokenInfoKey(token, mac)
    keyValueRepo.cacheSet(cacheOtpPrefix, key, 3 * verifyCodeExpireTimeInSecond, entityId).map(_ => token)
  }

  override def verifyTokenPhone(token: String, delete: Boolean): Future[String] = {
    val key = buildTokenPhoneInfoKey(token)
    keyValueRepo.cacheGet(cacheOtpPrefix, key).map({
      case Some(x) =>
        if (delete) keyValueRepo.cacheDel(cacheOtpPrefix, key).onFailure(ex => logger.error("Failed to delete token phone info", ex))
        x
      case None =>
        verifyDataLogger.warn(s"verifyTokenPhone($token, $delete): Failed None")
        throw InvalidTokenPhoneException()
    })
  }

  override def verifyTokenPhone(phone: String, token: String, mac: String, delete: Boolean): Future[Unit] = {
    val key = buildTokenPhoneInfoKey(token, mac)
    keyValueRepo.cacheGet(cacheOtpPrefix, key).map({
      case Some(x) if x == phone =>
        if (delete) keyValueRepo.cacheDel(cacheOtpPrefix, key).onFailure(ex => logger.error("Failed to delete token phone info", ex))
      case _ =>
        verifyDataLogger.warn(s"verifyTokenPhone($phone, $token,$mac, $delete): Failed None")
        throw InvalidTokenPhoneException()
    })
  }

  override def verifyToken(entityId: String, token: String, mac: String, delete: Boolean): Future[Unit] = {
    val key = buildTokenInfoKey(token, mac)
    keyValueRepo.cacheGet(cacheOtpPrefix, key).map({
      case Some(x) if x == entityId =>
        if (delete) keyValueRepo.cacheDel(cacheOtpPrefix, key).onFailure(ex => logger.error("Failed to delete token info", ex))
      case _ =>
        verifyDataLogger.warn(s"verifyToken($entityId, $token,$mac, $delete): Failed None")
        throw InvalidTokenException()
    })
  }

  override def deleteTokenPhone(token: String): Future[Unit] = {
    val key = buildTokenPhoneInfoKey(token)
    keyValueRepo.cacheDel(cacheOtpPrefix, key)
      .onFailure(ex => logger.error("Failed to delete token phone info", ex))
      .unit
  }

  override def deleteTokenPhone(token: String, mac: String): Future[Unit] = {
    val key = buildTokenPhoneInfoKey(token, mac)
    keyValueRepo.cacheDel(cacheOtpPrefix, key)
      .onFailure(ex => logger.error("Failed to delete token phone info", ex))
      .unit
  }

  private def buildTemplate(platForm: Option[String],
                            templateHolder: Option[TemplateHolder] = None): String = {
    platForm match {
      case Some(v) if v == PlatForm.ANDROID.toString => templateHolder.map(_.templateForAndroid).getOrElse(smsCodeTemplateForAndroidDefault)
      case Some(v) if v == PlatForm.IOS.toString => templateHolder.map(_.templateForIos).getOrElse(smsCodeTemplateForIosDefault)
      case _ => templateHolder.map(_.defaultTemplate).getOrElse(smsCodeTemplateDefault)
    }
  }

  override def sendCodeToPhone(normPhone: String, code: String, platform: Option[String]): Future[Unit] = Profiler(s"$clazz.sendCodeToPhone") {

    val verifyCodeInfoObj = VerifyCodeInfo(normPhone, code, System.currentTimeMillis())
    val verifyCodeInfoJson: String = JsonHelper.toJson[VerifyCodeInfo](verifyCodeInfoObj)

    val uuid = UUID.randomUUID().toString
    sendSMSStatsLogger.info(s"0\t$uuid\t$normPhone\t$code\t$platform\t${verifyCodeInfoObj.timestamp}")

    val msg = mapSmsCodeTemplateByPrefix
      .find(tuple => tuple._1.exists(normPhone.startsWith)).map(v => buildTemplate(platform, v._2.toSome))
      .getOrElse(buildTemplate(platform))
      .replaceAll("\\$code", code)

    incrQuotaPhone(normPhone)
    deliveryCodeService.sendMessageToPhone(normPhone, msg)
      .onSuccess(_ => sendSMSStatsLogger.info(s"1\t$uuid\t$verifyCodeInfoJson"))
      .onFailure(fn => sendSMSStatsLogger.info(s"-1\t$uuid\t$verifyCodeInfoJson\t${fn.getMessage}"))
  }

  protected def buildVerifyCodeKeyWithPhone(phone: String): String = s"verifyCode:$phone"

  protected def buildVerifyCodeKeyWithPhone(phone: String, mac: String): String = s"verifyCode:$phone-$mac"

  protected def buildTokenPhoneInfoKey(token: String): String = s"tokenPhone:$token"

  protected def buildTokenPhoneInfoKey(token: String, mac: String): String = s"tokenPhone:$token-$mac"

  protected def buildTokenInfoKey(token: String, mac: String): String = s"tokenInfo:$token-$mac"

  // EMAIL

  override def genAndSendCodeToEmail(email: String, mac: String, name: Option[String], isForgot: Boolean): Future[Unit] = Profiler(s"$clazz.genAndSendCodeToEmail") {
    for {
      code <- genVerifyCodeWithEmail(email, mac)
      _ <- sendCodeToEmail(email, code, name, isForgot)
    } yield {}
  }

  override def sendCodeToEmail(email: String, code: String, name: Option[String], isForgot: Boolean): Future[Unit] = Profiler(s"$clazz.sendCodeToEmail") {
    incrQuotaEmail(email)

    val subject = if (isForgot) emailReVerifySubjectTemplate else emailVerifySubjectTemplate
    val body = if (isForgot) emailReVerifyBodyTemplate else emailVerifyBodyTemplate

    deliveryCodeService.sendEmail(email,
      subject, body,
      Map(
        "code" -> code,
        "name" -> name
      )
    )
  }

  def genVerifyCodeWithEmail(email: String, mac: String): Future[String] = {
    //    val code = Utils.randomInt(1000000, 10000000).toString
    val code = CustomUtils.randomInt(100000, 1000000).toString
    val verifyCodeKey = buildVerifyCodeKeyWithEmail(email, mac)
    keyValueRepo.cacheSet(cacheOtpPrefix, verifyCodeKey, verifyCodeExpireTimeInSecond, code).map(_ => code)
  }

  override def verifyCodeWithEmail(email: String, code: String, mac: String, delete: Boolean): Future[Unit] = {
    val key = buildVerifyCodeKeyWithEmail(email, mac)
    keyValueRepo.cacheGet(cacheOtpPrefix, key).flatMap {
      case Some(x) if x.equals(code) =>
        if (delete) deleteVerifyCodeWithEmail(email, mac) else Future.Unit
      case Some(_) =>
        keyValueRepo.cacheGetTtlInSecond(cacheOtpPrefix, key).map(_.map(_.toInt).getOrElse(0))
          .map(result => throw InvalidVerifyEmailCodeException(expiredIn = result))
      case _ =>
        Future.exception(InvalidVerifyEmailCodeException(isExpiredCode = true))
    }
  }

  override def deleteVerifyCodeWithEmail(email: String, mac: String): Future[Unit] = {
    val key = buildVerifyCodeKeyWithEmail(email, mac)
    keyValueRepo.cacheDel(cacheOtpPrefix, key)
      .liftToTry.unit
  }

  protected def buildVerifyCodeKeyWithEmail(email: String, mac: String): String = s"verifyCodeEmail:$email-$mac"

  //region Quota Phone Only

  override def isExceedQuotaPhone(phone: String, numCheck: Option[Int]): Future[Boolean] = {
    val key = buildQuotaPhoneKey(phone)
    keyValueRepo.cacheGet(cacheQuotaPrefix, key).map(resp => {
      val num = resp.map(_.toInt).getOrElse(0)
      if (num >= numCheck.getOrElse(limitQuotaPhone)) true else false
    })
  }

  override def incrQuotaPhone(phone: String): Future[Int] = {
    val quotaKey = buildQuotaPhoneKey(phone)
    for {
      rs <- keyValueRepo.cacheIncr(cacheQuotaPrefix, quotaKey, 1)
        .map(_.getOrElse(throw NotFoundException("")).toInt)
        .onSuccess(i => {
          verifyDataLogger.info(s"incrQuotaPhone($phone) num=$i")
          if (i <= 1) keyValueRepo.cacheSetExpireInSecond(cacheQuotaPrefix, quotaKey, phoneQuotaExpireTimeInSecond)
            .onFailure(fn => logger.error("Failed to expire quota phone number", fn))
        })
        .transform {
          case Return(r) => Future.value(r)
          case Throw(e) => async {
            error("Failed to incrQuotaPhoneNumber" + phone, e)
            0
          }
        }
    } yield rs
  }

  override def deleteQuotaPhone(phone: String): Future[Unit] = {
    val quotaKey = buildQuotaPhoneKey(phone)
    keyValueRepo.cacheDel(cacheQuotaPrefix, quotaKey).unit
  }

  //endregion

  //region Quota Phone with Mac

  override def incrQuotaPhoneMac(phone: String, mac: String, ttl: Duration): Future[Int] = {
    val quotaKey = buildQuotaPhoneKey(phone, mac)
    for {
      rs <- keyValueRepo.cacheIncr(cacheQuotaPrefix, quotaKey, 1)
        .map(_.getOrElse(throw NotFoundException("")).toInt)
        .onSuccess(i => {
          verifyDataLogger.info(s"incrQuotaPhoneMac($phone, $mac, ${ttl.toMillis}) num=$i")
          if (i <= 1) keyValueRepo.cacheSetExpireInSecond(cacheQuotaPrefix, quotaKey, ttl.toSeconds.toInt)
            .onFailure(fn => logger.error(s"Failed to expire quota phone number and mac ($phone, $mac, $ttl)", fn))
        })
        .transform {
          case Return(v) => Future.value(v)
          case Throw(e) => async {
            error("Failed to incrQuotaPhoneMac($phone, $mac, $expireTimeInSecond)", e)
            0
          }
        }
    } yield rs
  }

  override def isExceedQuotaPhoneMac(phone: String, mac: String, numCheck: Int): Future[Boolean] = {
    val quotaKey = buildQuotaPhoneKey(phone, mac)
    keyValueRepo.cacheGet(cacheQuotaPrefix, quotaKey).map(resp => {
      val num = resp.map(_.toInt).getOrElse(0)
      if (num >= numCheck) true else false
    })
  }

  override def deleteQuotaPhoneMac(phone: String, mac: String): Future[Unit] = {
    val quotaKey = buildQuotaPhoneKey(phone, mac)
    keyValueRepo.cacheDel(cacheQuotaPrefix, quotaKey)
      .unit
  }

  def buildQuotaPhoneKey(phone: String, mac: String = ""): String = s"quotaPhone:$phone-$mac"

  //endregion

  //region Quota Email Only

  override def isExceedQuotaEmail(email: String, numCheck: Option[Int]): Future[Boolean] = {
    val key = buildQuotaEmailKey(email)
    keyValueRepo.cacheGet(cacheQuotaPrefix, key).map(resp => {
      val num = resp.map(_.toInt).getOrElse(0)
      if (num >= numCheck.getOrElse(limitQuotaEmail)) true else false
    })
  }

  override def incrQuotaEmail(email: String): Future[Int] = {
    val quotaKey = buildQuotaEmailKey(email)
    for {
      rs <- keyValueRepo.cacheIncr(cacheQuotaPrefix, quotaKey, 1)
        .map(_.getOrElse(throw NotFoundException("")).toInt)
        .onSuccess(i => {
          verifyDataLogger.info(s"incrQuotaEmail($email) num=$i")
          if (i <= 1) keyValueRepo.cacheSetExpireInSecond(cacheQuotaPrefix, quotaKey, emailQuotaExpireTimeInSecond)
            .onFailure(fn => logger.error("Failed to expire quota email", fn))
        })
        .transform {
          case Return(r) => Future.value(r)
          case Throw(e) => async {
            error(s"Failed to incrQuotaEmail $email", e)
            0
          }
        }
    } yield rs
  }

  //endregion

  //region Quota Email with Mac

  override def isExceedQuotaEmailMac(email: String, mac: String, numCheck: Int): Future[Boolean] = {
    val key = buildQuotaEmailKey(email, mac)
    keyValueRepo.cacheGet(cacheQuotaPrefix, key).map(resp => {
      val num = resp.map(_.toInt).getOrElse(0)
      if (num >= numCheck) true else false
    })
  }

  override def incrQuotaEmailMac(email: String, mac: String, ttl: Duration): Future[Int] = {
    val quotaKey = buildQuotaEmailKey(email, mac)
    for {
      rs <- keyValueRepo.cacheIncr(cacheQuotaPrefix, quotaKey, 1)
        .map(_.getOrElse(throw NotFoundException("")).toInt)
        .onSuccess(i => {
          verifyDataLogger.info(s"incrQuotaEmailMac($email, $mac, ${ttl.toMillis}) num=$i")
          if (i <= 1) keyValueRepo.cacheSetExpireInSecond(cacheQuotaPrefix, quotaKey, ttl.toSeconds.toInt)
            .onFailure(fn => logger.error(s"Failed to expire quota email ($email, $mac)", fn))
        })
        .transform {
          case Return(v) => Future.value(v)
          case Throw(e) => async {
            error(s"Failed to incrQuotaEmail ($email, $mac)", e)
            0
          }
        }
    } yield rs
  }

  override def deleteQuotaEmailMac(email: String, mac: String): Future[Unit] = {
    val quotaKey = buildQuotaEmailKey(email, mac)
    keyValueRepo.cacheDel(cacheQuotaPrefix, quotaKey)
      .unit
  }

  override def deleteQuotaEmail(email: String): Future[Unit] = {
    val quotaKey = buildQuotaEmailKey(email)
    keyValueRepo.cacheDel(cacheQuotaPrefix, quotaKey).unit
  }

  def buildQuotaEmailKey(email: String, mac: String = ""): String = s"quotaEmail:$email-$mac"

  //endregion

  //region Quota User with Mac

  override def isExceedQuotaUserMac(username: String, mac: String, numCheck: Int): Future[Boolean] = {
    val key = buildQuotaUserKey(username, mac)
    keyValueRepo.cacheGet(cacheQuotaPrefix, key).map(resp => {
      val num = resp.map(_.toInt).getOrElse(0)
      if (num >= numCheck) true else false
    })
  }

  override def incrQuotaUserMac(username: String, mac: String, ttl: Duration): Future[Int] = {
    val quotaKey = buildQuotaUserKey(username, mac)
    for {
      rs <- keyValueRepo.cacheIncr(cacheQuotaPrefix, quotaKey, 1)
        .map(_.getOrElse(throw NotFoundException("")).toInt)
        .onSuccess(i => {
          verifyDataLogger.info(s"incrQuotaUserMac($username, $mac, ${ttl.toMillis}) num=$i")
          if (i <= 1) keyValueRepo.cacheSetExpireInSecond(cacheQuotaPrefix, quotaKey, ttl.toSeconds.toInt)
            .onFailure(fn => logger.error(s"Failed to expire quota user ($username, $mac)", fn))
        })
        .transform {
          case Return(v) => Future.value(v)
          case Throw(e) => async {
            error(s"Failed to incrQuotaUser ($username, $mac)", e)
            0
          }
        }
    } yield rs
  }

  override def deleteQuotaUserMac(username: String, mac: String): Future[Unit] = {
    val quotaKey = buildQuotaUserKey(username, mac)
    keyValueRepo.cacheDel(cacheQuotaPrefix, quotaKey)
      .unit
  }

  override def getRemainingTtlQuotaUserMac(username: String, mac: String): Future[Option[Int]] = {
    val key = buildQuotaUserKey(username, mac)
    keyValueRepo.cacheGetTtlInSecond(cacheQuotaPrefix, key)
      .map(_.map(_.toInt))
  }

  def buildQuotaUserKey(username: String, mac: String = ""): String = s"quotaUser:$username-$mac"

  //endregion

  //region Quota IP

  override def isExceedQuotaIP(ip: String, numCheck: Option[Int] = None): Future[Boolean] = {
    val key = buildQuotaIPKey(ip)
    val limit = numCheck.getOrElse(limitQuotaIP)
    keyValueRepo.cacheGet(cacheQuotaPrefix, key).map(resp => {
      val num = resp.map(_.toInt).getOrElse(0)
      if (num >= limit) true else false
    })
  }

  override def incrQuotaIP(ip: String, ttl: Option[Duration] = None): Future[Int] = {
    val quotaKey = buildQuotaIPKey(ip)
    val duration = ttl.map(_.toSeconds.toInt).getOrElse(ipQuotaExpireTimeInSecond)
    for {
      rs <- keyValueRepo.cacheIncr(cacheQuotaPrefix, quotaKey, 1)
        .map(_.getOrElse(throw NotFoundException("")).toInt)
        .onSuccess(i => {
          verifyDataLogger.info(s"incrQuotaIP($ip) num=$i")
          if (i <= 1)
            keyValueRepo.cacheSetExpireInSecond(cacheQuotaPrefix, quotaKey, duration)
              .onFailure(fn => logger.error(s"Failed to expire quota ip $ip", fn))
        })
        .transform {
          case Return(r) => Future.value(r)
          case Throw(e) => async {
            error(s"Failed to incrQuotaIP $ip", e)
            0
          }
        }
    } yield rs
  }

  override def deleteQuotaIP(ip: String): Future[Unit] = {
    val quotaKey = buildQuotaIPKey(ip)
    keyValueRepo.cacheDel(cacheQuotaPrefix, quotaKey).unit
  }

  override def getRemainingTtlQuotaIP(ip: String): Future[Option[Int]] = {
    val key = buildQuotaIPKey(ip)
    keyValueRepo.cacheGetTtlInSecond(cacheQuotaPrefix, key)
      .map(_.map(_.toInt))
  }

  private def buildQuotaIPKey(ip: String, mac: String = ""): String = s"quotaIP:$ip-$mac"

  //endregion

  //region Quota Entity with Mac

  override def isExceedQuotaEntityMac(entityType: String, entityId: String, mac: String, numCheck: Int): Future[Boolean] = Profiler(s"$clazz.isExceedQuotaUserMac") {
    keyValueRepo.cacheGet(cacheQuotaPrefix, buildQuotaEntityKey(entityType, entityId, mac))
      .map(resp => {
        val num = resp.map(_.toInt).getOrElse(0)
        if (num >= numCheck) true else false
      })
  }

  override def incrQuotaEntityMac(entityType: String, entityId: String, mac: String, ttl: Duration): Future[Unit] = Profiler(s"$clazz.incrQuotaEntityMac") {
    val quotaKey = buildQuotaEntityKey(entityType, entityId, mac)
    for {
      _ <- keyValueRepo.cacheIncr(cacheQuotaPrefix, quotaKey, 1)
        .onSuccess(i => {
          verifyDataLogger.info(s"incrQuotaEntityMac($entityType, $entityId, $mac, ${ttl.toMillis}) num=$i")
          if (i.map(_.toInt).getOrElse(0) <= 1)
            keyValueRepo.cacheSetExpireInSecond(cacheQuotaPrefix, quotaKey, ttl.toSeconds.toInt)
              .onFailure(fn => error(s"Failed to expire quota entity ($entityType, $entityId, $mac)", fn))
        })
        .transform {
          case Return(_) => Future.Unit
          case Throw(e) => async(error(s"Failed to incrQuotaEntityMac($entityType, $entityId, $mac)", e))
        }
    } yield {}
  }

  override def deleteQuotaEntityMac(entityType: String, entityId: String, mac: String): Future[Unit] = Profiler(s"$clazz.deleteQuotaEntityMac") {
    keyValueRepo.cacheDel(cacheQuotaPrefix, buildQuotaEntityKey(entityType, entityId, mac))
      .unit
  }

  private def buildQuotaEntityKey(entityType: String, entityId: String, mac: String = ""): String = s"quotaEntity-$entityType:$entityId-$mac"

  private def buildQuotaEntityLockKey(entityType: String, entityId: String, mac: String = ""): String = s"quotaEntityWithLock-$entityType:$entityId-$mac"

  override def buildEntityWithLockTime(entityType: String, entityId: String, mac: String, lockTimeIn: Duration): Future[Long] = Profiler(s"$clazz.buildUserWithLockTime") {
    val key = buildQuotaEntityLockKey(entityId, mac)
    val lockTimeIns = lockTimeIn
    val lockTime = System.currentTimeMillis() + lockTimeIns.toMillis
    keyValueRepo.cacheSet(cacheLockPrefix, key, lockTimeIns.toSeconds.toInt, lockTime.toString).map(_ => {
      verifyDataLogger.info(s"buildEntityWithLockTime($entityType, $entityId, $mac) $lockTime")
      lockTime
    })
  }

  override def getLockTimeByEntity(entityType: String, entityId: String, mac: String): Future[Option[Long]] = Profiler(s"$clazz.getLockTimeByUser") {
    val key = buildQuotaEntityLockKey(entityId, mac)
    keyValueRepo.cacheGet(cacheLockPrefix, key).map({
      case Some(v) if (System.currentTimeMillis() + 10.second.toMillis) < v.toLong => v.toLong.toSome
      case Some(_) =>
        keyValueRepo.cacheDel(cacheLockPrefix, key)
          .onFailure(ex => logger.error("Failed to delete user with lockTime", ex))
        None
      case _ => None
    })
  }

  override def deleteLockTimeByEntity(entityType: String, entityId: String, mac: String): Future[Unit] = Profiler(s"$clazz.deleteLockTimeByUser") {
    val key = buildQuotaEntityLockKey(entityId, mac)
    keyValueRepo.cacheDel(cacheLockPrefix, key).unit
  }

  //endregion

  override def genPnlRequestToken(request: RegisterByAdminData): Future[Unit] = Profiler(s"$clazz.genPnlRequestToken") {
    val key = buildPnlRequestTokenKey(request)
    keyValueRepo.cacheSet(cacheOtpPrefix, key, verifyCodeExpireTimeInSecond + 15, request.buildRequestToken()).unit // 15s more than verifyCodeExpireTimeInSecond
  }

  override def verifyAdminRequestToken[T](request: RegisterByAdminData)(fn: => Future[T]): Future[T] = Profiler(s"$clazz.verifyPnlRequestToken") {
    val key = buildPnlRequestTokenKey(request)
    keyValueRepo.cacheGet(cacheOtpPrefix, key).flatMap {
      case Some(x) if x.equals(request.buildRequestToken()) =>
        fn.transform {
          case Return(v) => async(keyValueRepo.cacheDel(cacheOtpPrefix, key)).map(_ => v)
          case Throw(e) => Future.exception(e)
        }
      case Some(_) =>
        Future.exception(InvalidVerifyPnlRequestException("The confirm request does not match the previous request"))
      case _ =>
        Future.exception(InvalidVerifyPnlRequestException("Request not exist or expired", isNotFound = true))
    }
  }

  private def buildPnlRequestTokenKey(request: RegisterByAdminData): String = {
    s"pnlRequestToken-${request.pnl}:${request.phone}-${request.email}"
  }

  override def getVerifyCodeExpireTimeInSecond: Int = verifyCodeExpireTimeInSecond

}

class ConstantVerifyCodeServiceImpl @Inject()(
                                               keyValueRepo: RedisCacheCommon,
                                               deliveryCodeService: DeliveryCodeService,
                                               enableEmail: Boolean,
                                               enableSms: Boolean,
                                             ) extends VerifyCodeServiceImpl(keyValueRepo, deliveryCodeService) {

  private val clazz = getClass.getCanonicalName

  val code = "123123"

  private val whitePhoneList = ZConfig.getStringList("sms.white_phone_list", Nil)
  private val whiteEmailList = ZConfig.getStringList("email.white_email_list", Nil)

  override def genAndSendCodeToPhone(phone: String, mac: String, platform: Option[String] = None): Future[Unit] = Profiler(s"$clazz.genAndSendCodeToPhone") {
    if (enableSms || whitePhoneList.contains(phone)) super.genAndSendCodeToPhone(phone, mac, platform)
    else {
      genVerifyCodeWithPhone(phone, mac)
        .flatMap(_ => incrQuotaPhone(phone))
        .map(_ => {})
    }
  }

  override def genVerifyCodeWithPhone(phone: String, mac: String): Future[String] = Profiler(s"$clazz.genVerifyCodeWithPhone") {
    if (enableSms || whitePhoneList.contains(phone)) super.genVerifyCodeWithPhone(phone, mac)
    else {
      warn(s"genVerifyCodeWithPhone($phone): enableSms=false or not in whitelist")
      val codeKey = buildVerifyCodeKeyWithPhone(phone, mac)
      keyValueRepo.cacheSet(cacheOtpPrefix, codeKey, verifyCodeExpireTimeInSecond, code).map(_ => code)
    }
  }

  override def sendCodeToPhone(normPhone: String, code: String, platform: Option[String]): Future[Unit] = {
    if (enableSms || whitePhoneList.contains(normPhone)) super.sendCodeToPhone(normPhone, code, platform)
    else async {
      warn(s"sendCodeToPhone($normPhone): enableSms=false or not in whitelist")
    }
  }

  override def genAndSendCodeToEmail(email: String, mac: String, name: Option[String], isForgot: Boolean): Future[Unit] = Profiler(s"$clazz.genAndSendCodeToEmail") {
    if (enableEmail || whiteEmailList.contains(email.toLowerCase)) super.genAndSendCodeToEmail(email, mac, name, isForgot = isForgot)
    else {
      genVerifyCodeWithEmail(email, mac)
        .flatMap(_ => incrQuotaEmail(email))
        .map(_ => {})
    }
  }

  override def genVerifyCodeWithEmail(email: String, mac: String): Future[String] = Profiler(s"$clazz.genVerifyCodeWithEmail") {
    if (enableEmail || whiteEmailList.contains(email.toLowerCase)) super.genVerifyCodeWithEmail(email, mac)
    else {
      warn(s"genVerifyCodeWithEmail($email): enableEmail=false or not in whitelist")
      val verifyCodeKey = buildVerifyCodeKeyWithEmail(email, mac)
      keyValueRepo.cacheSet(cacheOtpPrefix, verifyCodeKey, verifyCodeExpireTimeInSecond, code).map(_ => code)
    }
  }

  override def sendCodeToEmail(email: String, code: String, name: Option[String], isForgot: Boolean): Future[Unit] = {
    if (enableEmail || whiteEmailList.contains(email.toLowerCase)) super.sendCodeToEmail(email, code, name, isForgot)
    else async {
      warn(s"sendCodeToEmail($email): enableEmail=false or not in whitelist")
    }

  }

}
