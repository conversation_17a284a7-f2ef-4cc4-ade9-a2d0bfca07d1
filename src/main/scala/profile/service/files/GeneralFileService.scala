package profile.service.files

import com.twitter.util.Future
import org.apache.commons.io.FileUtils
import scalaj.http.{Http, HttpResponse}
import vn.vhm.common.domain.Implicits.async
import vn.vhm.common.domain.profiling.Profiler
import vn.vhm.common.exception.{RCustomException, UnAuthorize}
import vn.vhm.common.util.JsonHelper

import java.io.File

/**
 * <AUTHOR> 8/27/24 07:46
 */
case class FilePrepareUpload(
                              presignUrl: String,
                              presignHeaders: Map[String, String],
                              fileUrl: String
                            )

trait GeneralFileService {

  def preparePublicDirectUpload(category: String, fileId: String, fileName: String, contentType: String): Future[FilePrepareUpload]

  def preparePublicInstantDirectUpload(category: String, fileId: String, fileName: String, contentType: String): Future[FilePrepareUpload]

  def uploadFileToUrl(uploadUrl: String, data: Array[Byte], headers: Option[Map[String, String]]): Future[Boolean]

  def uploadFilePublicDirect(category: String, fileId: String, fileName: String, contentType: String, data: Array[Byte]): Future[String]

  def uploadFilePublicInstantDirect(category: String, fileId: String, fileName: String, contentType: String, data: Array[Byte]): Future[String]

  def uploadFilePublicInstantDirect(category: String, fileId: String, fileName: String, contentType: String, localPath: String): Future[String]

}

case class GeneralFileServiceImpl(
                                   apiHost: String,
                                   uploadIntegrationKey: String,
                                   connTimeoutInMs: Int = 60000, readTimeoutInMs: Int = 60000,
                                   proxy: Option[java.net.Proxy] = None
                                 ) extends GeneralFileService {

  protected val clazz = getClass.getCanonicalName

  override def preparePublicDirectUpload(category: String, fileId: String, fileName: String, contentType: String): Future[FilePrepareUpload] = Profiler(s"$clazz.preparePublicDirectUpload") {
    async {
      val resp = sendRequest(
        apiPath = "/public/prepare_upload",
        integrationkey = uploadIntegrationKey,
        params = Map(),
        postBody = Some(
          JsonHelper.toJson(Map("category" -> category, "file_id" -> fileId, "file_name" -> fileName, "content_type" -> contentType))
        )
      )
      resp.code match {
        case 200 => JsonHelper.fromJson[FilePrepareUpload](resp.body)
        case 401 => throw UnAuthorize("Unauthorized")
        case _ => throw new InternalError(s"Internal error: ${resp.code} - ${resp.body}")
      }
    }
  }

  override def preparePublicInstantDirectUpload(category: String, fileId: String, fileName: String, contentType: String): Future[FilePrepareUpload] = Profiler(s"$clazz.preparePublicInstantDirectUpload") {
    async {
      val resp = sendRequest(
        apiPath = "/public-instant/prepare-upload",
        integrationkey = uploadIntegrationKey,
        params = Map(),
        postBody = Some(
          JsonHelper.toJson(Map("category" -> category, "file_id" -> fileId, "file_name" -> fileName, "content_type" -> contentType))
        )
      )
      resp.code match {
        case 200 => JsonHelper.fromJson[FilePrepareUpload](resp.body)
        case 401 => throw UnAuthorize("Unauthorized")
        case _ => throw new InternalError(s"Internal error: ${resp.code} - ${resp.body}")
      }
    }
  }

  override def uploadFileToUrl(uploadUrl: String, data: Array[Byte], headers: Option[Map[String, String]]): Future[Boolean] = Profiler(s"$clazz.uploadFileToUrl") {
    async {
      val resp = Http(uploadUrl)
        .timeout(connTimeoutInMs, readTimeoutInMs)
        .headers(headers.getOrElse(Map.empty))
        .put(data)
        .asString

      if (!resp.is2xx) throw new InternalError(s"Internal error: ${resp.code} - ${resp.body}")

      true
    }
  }

  override def uploadFilePublicDirect(category: String, fileId: String, fileName: String,
                                      contentType: String, data: Array[Byte]): Future[String] = Profiler(s"$clazz.uploadFilePublicDirect") {
    for {
      prepareResult <- preparePublicDirectUpload(category, fileId, fileName, contentType)
      headers = prepareResult.presignHeaders ++ Map(
        "Content-Type" -> contentType
      )
      uploadOK <- uploadFileToUrl(prepareResult.presignUrl, data, Some(headers))
      _ = if (!uploadOK) throw RCustomException("presign_upload_error", s"Cannot upload data to ${prepareResult.presignUrl}")
    } yield prepareResult.fileUrl
  }

  override def uploadFilePublicInstantDirect(category: String, fileId: String, fileName: String,
                                             contentType: String, data: Array[Byte]): Future[String] = Profiler(s"$clazz.uploadFilePublicInstantDirect_Bytes") {
    for {
      prepareResult <- preparePublicInstantDirectUpload(category, fileId, fileName, contentType)
      headers = prepareResult.presignHeaders ++ Map(
        "Content-Type" -> contentType
      )
      uploadOK <- uploadFileToUrl(prepareResult.presignUrl, data, Some(headers))
      _ = if (!uploadOK) throw RCustomException("presign_upload_error", s"Cannot upload data to ${prepareResult.presignUrl}")
    } yield prepareResult.fileUrl
  }

  override def uploadFilePublicInstantDirect(category: String, fileId: String, fileName: String,
                                             contentType: String, localPath: String): Future[String] = Profiler(s"$clazz.uploadFilePublicInstantDirect_IS") {
    for {
      prepareResult <- preparePublicInstantDirectUpload(category, fileId, fileName, contentType)
      headers = prepareResult.presignHeaders ++ Map(
        "Content-Type" -> contentType
      )
      uploadOK <- uploadFileToUrl(prepareResult.presignUrl, FileUtils.readFileToByteArray(new File(localPath)), Some(headers))
      _ = if (!uploadOK) throw RCustomException("presign_upload_error", s"Cannot upload data to ${prepareResult.presignUrl}")
    } yield prepareResult.fileUrl
  }

  protected def sendRequest(apiPath: String, integrationkey: String, params: Map[String, String], postBody: Option[String]): HttpResponse[String] = Profiler(s"$clazz.sendRequest") {
    val requestBuilder = postBody match {
      case Some(body) =>
        Http(s"$apiHost$apiPath")
          .timeout(connTimeoutInMs, readTimeoutInMs)
          .header("content-type", "application/json")
          .header("integrationkey", integrationkey)
          .params(params)
          .postData(body)
      case None =>
        Http(s"$apiHost$apiPath")
          .timeout(connTimeoutInMs, readTimeoutInMs)
          .header("content-type", "application/json")
          .header("integrationkey", integrationkey)
          .params(params)
    }
    proxy.foreach(_p => requestBuilder.proxy(_p))

    requestBuilder.asString

  }

}