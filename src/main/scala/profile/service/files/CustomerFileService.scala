package profile.service.files

import com.twitter.util.Future
import profile.domain.{BytesFile, VClubFileInfo}
import profile.repository.S3SpecificFileRepository
import profile.util.FileFormatUtils
import vn.vhm.common.domain.Implicits.async
import vn.vhm.common.domain.profiling.Profiler

/**
 * <AUTHOR> 9/23/24 05:55
 */
trait CustomerFileService {

  def uploadFilesCustomerPhotoCard(customerId: Long, bytesFiles: Seq[BytesFile]): Future[Seq[VClubFileInfo]]

  def uploadFilesRawCustomerPhotoCard(customerId: Long, bytesFiles: Seq[BytesFile]): Future[Seq[VClubFileInfo]]

  def uploadFilesCustomerFace(customerId: Long, bytesFiles: Seq[BytesFile]): Future[Seq[VClubFileInfo]]

  def uploadFilesChipCustomerFace(customerId: Long, bytesFiles: Seq[BytesFile]): Future[Seq[VClubFileInfo]]

  def getFileUrl(filePath: String): Future[String]
}

case class CustomerFileServiceImpl(fileRepo: S3SpecificFileRepository) extends CustomerFileService {

  protected val clazz = getClass.getCanonicalName

  protected def uploadFiles(customerId: Long, fileCategory: String, bytesFiles: Seq[BytesFile]): Future[Map[String, String]] = {

    Future.collect(bytesFiles.map(bytesFile => async {

      val rawFilePath = s"$customerId/$fileCategory/${bytesFile.filePath}"

      val path = fileRepo.put(
        localFilePath = bytesFile.localPath,
        contentType = bytesFile.contentType,
        filePath = rawFilePath,
        metadata = Map.empty,
        tags = Map.empty
      )

      bytesFile.id -> path

    })).map(_.toMap)

  }

  override def uploadFilesCustomerPhotoCard(customerId: Long, bytesFiles: Seq[BytesFile]): Future[Seq[VClubFileInfo]] = Profiler(s"$clazz.uploadFilesCustomerPhotoCard") {
    val `type` = "CUSTOMER_IDENTITY_CARD"
    for {
      uploadedFiles <- uploadFiles(customerId, `type`.toLowerCase, bytesFiles)
      result = bytesFiles.map(bytesFile => VClubFileInfo(
        fileName = uploadedFiles.getOrElse(bytesFile.id, ""),
        format = FileFormatUtils.getFileFormat(bytesFile.contentType).getOrElse(bytesFile.contentType),
        `type` = `type`
      ))
    } yield result
  }

  override def uploadFilesRawCustomerPhotoCard(customerId: Long, bytesFiles: Seq[BytesFile]): Future[Seq[VClubFileInfo]] = Profiler(s"$clazz.uploadFilesRawCustomerPhotoCard") {
    val `type` = "CUSTOMER_IDENTITY_CARD_RAW"
    for {
      uploadedFiles <- uploadFiles(customerId, `type`.toLowerCase, bytesFiles)
      result = bytesFiles.map(bytesFile => VClubFileInfo(
        fileName = uploadedFiles.getOrElse(bytesFile.id, ""),
        format = FileFormatUtils.getFileFormat(bytesFile.contentType).getOrElse(bytesFile.contentType),
        `type` = `type`
      ))
    } yield result
  }

  override def uploadFilesCustomerFace(customerId: Long, bytesFiles: Seq[BytesFile]): Future[Seq[VClubFileInfo]] = Profiler(s"$clazz.uploadFilesCustomerFace") {
    val `type` = "CUSTOMER_FACE"
    for {
      uploadedFiles <- uploadFiles(customerId, `type`.toLowerCase, bytesFiles)
      result = bytesFiles.map(bytesFile => VClubFileInfo(
        fileName = uploadedFiles.getOrElse(bytesFile.id, ""),
        format = FileFormatUtils.getFileFormat(bytesFile.contentType).getOrElse(bytesFile.contentType),
        `type` = `type`
      ))
    } yield result
  }

  override def uploadFilesChipCustomerFace(customerId: Long, bytesFiles: Seq[BytesFile]): Future[Seq[VClubFileInfo]] = Profiler(s"$clazz.uploadFilesChipCustomerFace") {
    val `type` = "CUSTOMER_FACE_IN_CHIP"
    for {
      uploadedFiles <- uploadFiles(customerId, `type`.toLowerCase, bytesFiles)
      result = bytesFiles.map(bytesFile => VClubFileInfo(
        fileName = uploadedFiles.getOrElse(bytesFile.id, ""),
        format = FileFormatUtils.getFileFormat(bytesFile.contentType).getOrElse(bytesFile.contentType),
        `type` = `type`
      ))
    } yield result
  }

  override def getFileUrl(filePath: String): Future[String] = Profiler(s"$clazz.getFileUrl") {
    fileRepo.getPresignUrlForDownload(filePath).map(_.presignUrl)
  }
}
