package profile.service.files

import com.twitter.inject.Logging
import com.typesafe.config.Config
import profile.repository.FileRepository
import vn.vhm.common.domain.profiling.Profiler
import vn.vhm.common.exception.InternalError

import java.io.File
import java.util.{Base64, UUID}
import javax.crypto.Mac
import javax.crypto.spec.SecretKeySpec

case class ImgProxyService(config: Config, fileRepo: FileRepository) extends Logging {

  protected val clazz = getClass.getCanonicalName
  private val serviceKey = config.getString("service_key")
  private val serviceSalt = config.getString("service_salt")
  private val baseUrl = config.getString("base_url")

  def resizeFromFile(file: File, width: Option[Int], height: Int, extension: Option[String], contentType: String, mac: String = ""): String = Profiler(s"$clazz.resizeFromFile") {
    try {
      val externalUrl = fileRepo.putPhotoTempFromFile(s"${mac}_${System.currentTimeMillis()}_${UUID.randomUUID().toString}", file, contentType)
      val signedUrl = generateSignedUrlForImgProxy(
        key = serviceKey, salt = serviceSalt,
        url = externalUrl,
        width = width.getOrElse(0), height = height,
        resize = "fit", enlarge = 1, gravity = "no",
        extension = extension.getOrElse("jpg")
      )
      s"$baseUrl$signedUrl"
    } catch {
      case e: Exception =>
        error("Failed when resize image from file", e)
        throw InternalError("Failed to resize image from file")
    }
  }

  private def generateSignedUrlForImgProxy(
                                            key: String, salt: String, url: String,
                                            resize: String, width: Int, height: Int,
                                            enlarge: Int, gravity: String, quality: Int = 100, extension: String): String = {

    val path = s"/rs:$resize:$width:$height:$enlarge/g:$gravity/q:$quality/${Base64.getUrlEncoder.withoutPadding().encodeToString(url.getBytes())}.$extension"

    val sha256HMAC = Mac.getInstance("HmacSHA256")
    val secretKey = new SecretKeySpec(hexStringToByteArray(key), "HmacSHA256")
    sha256HMAC.init(secretKey)
    sha256HMAC.update(hexStringToByteArray(salt))

    val hash = Base64.getUrlEncoder.withoutPadding().encodeToString(sha256HMAC.doFinal(path.getBytes()))
    s"/$hash$path"
  }

  private def hexStringToByteArray(hex: String): Array[Byte] = {
    if (hex.length() % 2 != 0)
      throw new IllegalArgumentException("Even-length string required")

    (for (i <- 0 until (hex.length() / 2)) yield {
      ((Character.digit(hex.charAt(i * 2), 16) << 4) | Character.digit(hex.charAt(i * 2 + 1), 16)).toByte
    }).toArray
  }

}
