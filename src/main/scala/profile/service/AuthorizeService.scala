package profile.service

import caas.service.CaasService
import com.twitter.inject.Logging
import com.twitter.util.Future
import profile.domain.FileLocalData
import profile.domain.customer.Customer
import profile.domain.request.UpdateUserRequest
import profile.service.files.GeneralFileService
import vn.vhm.common.domain.OptionImplicits.OptionImplicitAny
import vn.vhm.common.domain.profiling.Profiler
import vn.vhm.common.exception.RCustomException

import java.util.UUID
import javax.inject.Inject

/**
 * <AUTHOR>
 */
trait AuthorizeService {

  //region Password

  def changePassword(username: String, oldPasswordHashed: Option[String], newPasswordHashed: String): Future[Unit]

  //endregion

  //region Profile

  def updateAvatar(username: String, avatarData: FileLocalData): Future[String]

  def updateUserProfile(username: String, userReq: UpdateUserRequest): Future[Unit]

  //endregion


  //region Email

  def sendCodeToUpdateNewEmail(username: String, newEmail: String): Future[Unit]

  def sendCodeToUpdateNewPhone(username: String, phone: String, platform: Option[String]): Future[Unit]

  def updateNewEmail(username: String, email: String, code: String): Future[Unit]

  def updateNewPhone(username: String, normPhone: String, code: String): Future[Unit]

  def sendCodeToVerifyEmail(username: String): Future[Unit]

  def verifyEmail(username: String, email: String, code: String): Future[Unit]

  //endregion
}

case class AuthorizeServiceImpl @Inject()(
                                           caasService: CaasService,
                                           profileCoreService: ProfileService,
                                           verifyDataService: VerifyDataService,
                                           fileService: GeneralFileService
                                         ) extends AuthorizeService with Logging {

  private val clazz = getClass.getCanonicalName

  //region Password

  override def changePassword(username: String, oldPasswordHashed: Option[String], newPasswordHashed: String): Future[Unit] = Profiler(s"$clazz.changePassword") {
    caasService.updatePasswordUser(username, oldPasswordHashed, newPasswordHashed)
  }

  //endregion

  //region Email

  override def sendCodeToUpdateNewEmail(username: String, newEmail: String): Future[Unit] = Profiler(s"$clazz.sendCodeToUpdateNewEmail") {
    for {
      customer <- profileCoreService.get(username, rescanPnl = false)
      _ <- profileCoreService.verifyUpdateEmail(customer, newEmail, customer.toSome)
      _ <- verifyDataService.genAndSendCodeToEmail(newEmail, s"update-email-$username", customer.fullName, isForgot=false)
    } yield {}
  }

  override def sendCodeToUpdateNewPhone(username: String, normPhone: String, platform: Option[String]): Future[Unit] = Profiler(s"$clazz.sendCodeToUpdateNewPhone") {
    for {
      customer <- profileCoreService.get(username, rescanPnl = false)
      _ <- profileCoreService.verifyUpdatePhone(customer, normPhone, customer.toSome)
      _ <- verifyDataService.genAndSendCodeToPhone(normPhone, s"update-phone-$username", platform)
    } yield {}
  }

  override def updateNewEmail(username: String, newEmail: String, code: String): Future[Unit] = Profiler(s"$clazz.updateNewEmail") {
    for {
      _ <- verifyDataService.verifyCodeWithEmail(newEmail, code, s"update-email-$username", delete = true)
      _ <- profileCoreService.updateEmail(username, newEmail, verified = true)
    } yield {}
  }

  override def updateNewPhone(username: String, normPhone: String, code: String): Future[Unit] = Profiler(s"$clazz.updateNewPhone") {
    for {
      _ <- verifyDataService.verifyCodeWithPhone(normPhone, s"update-phone-$username", code, deleteIfEqual = true)
      _ <- profileCoreService.updatePhone(username, normPhone, verified = true)
    } yield {}
  }

  override def sendCodeToVerifyEmail(username: String): Future[Unit] = Profiler(s"$clazz.sendCodeToVerifyEmail") {
    for {
      user <- profileCoreService.get(username, rescanPnl = false)
      _ = if (user.email.isEmpty) throw new Exception("Not exist email to verify")
      _ = if (user.emailVerified.contains(1)) throw new Exception("Email already verified")
      _ <- verifyDataService.genAndSendCodeToEmail(user.email.get, user.userId.get, user.fullName, isForgot=false)
    } yield {}
  }

  override def verifyEmail(username: String, email: String, code: String): Future[Unit] = Profiler(s"$clazz.verifyEmail") {
    for {
      user <- profileCoreService.get(username, rescanPnl = false)
      _ = if (user.email.isEmpty) throw RCustomException("not_exist_email", "Not exist email to verify")
      _ = if (user.emailVerified.contains(1)) throw RCustomException("email_already_verified", "Email already verified")
      _ = if (!user.email.contains(email)) throw RCustomException("invalid_email", "Invalid email")
      _ <- verifyDataService.verifyCodeWithEmail(email, code, username, delete = true)
      _ <- profileCoreService.internalUpdateUser(username, Customer(emailVerified = 1.toSome))
    } yield {}
  }

  //endregion

  //region Profile

  override def updateAvatar(username: String, avatarData: FileLocalData): Future[String] = Profiler(s"$clazz.updateAvatar") {
    for {
      avatarUrl <- fileService.uploadFilePublicInstantDirect("customer_avatar", username, s"${UUID.randomUUID().toString}.png", contentType = avatarData.mime, localPath = avatarData.localPath)
      _ <- profileCoreService.updateAvatar(username, avatarUrl)
    } yield avatarUrl
  }

  override def updateUserProfile(username: String, userReq: UpdateUserRequest): Future[Unit] = Profiler(s"$clazz.updateUserProfile") {
    profileCoreService.updateUser(username, userReq).map(_ => {})
  }

  //endregion
}
