package profile.consumer

import com.twitter.inject.Logging
import org.apache.kafka.clients.consumer.ConsumerRecord
import profile.domain.event.CustomerNewSignedInEvent
import profile.service.{AuthenJwtService, ProfileService}
import vn.vhm.common.client.kafka_010.StringKafkaConsumerFromConfig
import vn.vhm.common.domain.Implicits.async
import vn.vhm.common.domain.profiling.Profiler
import vn.vhm.common.util.JsonHelper

case class CustomerNewSignedInEventConsumer(
                                             config: com.typesafe.config.Config,
                                             authenJwtService: AuthenJwtService,
                                             profileService: ProfileService
                                           ) extends StringKafkaConsumerFromConfig(config) with Logging {

  protected val clazz = getClass.getCanonicalName

  override def consume(record: ConsumerRecord[String, String]): Unit = Profiler(s"$clazz.consume") {
    try {
      info(s"Consuming record: ${recordAsString(record)}")
      val event = JsonHelper.fromJson[CustomerNewSignedInEvent](record.value())
      authenJwtService.clearOtherSessions(event).rescue {
        case e: Exception => async(error(s"Failed consume(${recordAsString(record)})", e))
      }
      profileService.handleCustomerNewSignedInEvent(event).rescue {
        case e: Exception => async(error(s"Failed handleCustomerNewSignedInEvent(${recordAsString(record)})", e))
      }
    } catch {
      case e: Exception =>
        error(s"Error while consuming record: ${record.value()}", e)
    }
  }
}
