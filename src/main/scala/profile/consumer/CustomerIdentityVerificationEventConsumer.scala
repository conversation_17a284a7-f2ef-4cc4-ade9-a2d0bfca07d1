package profile.consumer

import com.fasterxml.jackson.databind.node.ObjectNode
import com.twitter.inject.Logging
import com.twitter.util.{Await, Future}
import org.apache.kafka.clients.consumer.ConsumerRecord
import profile.domain.event.{CustomerIdentityRejectedEvent, CustomerIdentityVerifiedEvent, ExtEvent}
import profile.service.ProfileService
import vn.vhm.common.client.kafka_010.StringKafkaConsumerFromConfig
import vn.vhm.common.domain.Implicits.async
import vn.vhm.common.domain.profiling.Profiler
import vn.vhm.common.exception.VhmException
import vn.vhm.common.util.JsonHelper

case class CustomerIdentityVerificationEventConsumer(
                                             config: com.typesafe.config.Config,
                                             profileService: ProfileService
                                           ) extends StringKafkaConsumerFromConfig(config) with Logging {

  protected val clazz = getClass.getCanonicalName

  override def consume(record: ConsumerRecord[String, String]): Unit = Profiler(s"$clazz.consume") {
    try {


      val event = JsonHelper.fromJson[ExtEvent[ObjectNode]](record.value())

      Await.result {
        event.code match {
          case "CUSTOMER_IDENTITY_VERIFIED" =>
            profileService.extractDemographicDataByVerifiedIdentity(
              JsonHelper.fromNode[CustomerIdentityVerifiedEvent](event.data)
            ).rescue {
              case e: Exception => async(error(s"Failed consume(${recordAsString(record)})", e))
            }
          case "CUSTOMER_IDENTITY_REJECTED" =>
            profileService.removeDemographicDataByRejectedIdentity(
              JsonHelper.fromNode[CustomerIdentityRejectedEvent](event.data)
            ).rescue {
              case e: VhmException => Future.Unit
              case e: Exception => async(error(s"Failed consume(${recordAsString(record)})", e))
            }
          case _ => Future.exception(new Exception(s"Invalid event code: ${event.code}"))
        }
      }
    } catch {
      case e: Exception =>
        error(s"Error while consuming record: ${record.value()}", e)
    }
  }
}
