package profile.consumer

import com.fasterxml.jackson.annotation.JsonIgnore
import com.fasterxml.jackson.databind.annotation.JsonDeserialize
import com.twitter.inject.Logging
import com.twitter.util.Await
import org.apache.kafka.clients.consumer.ConsumerRecord
import profile.domain.customer.{PnlCustomer, PnlCustomerDAO, PnlCustomerIdentityDocs}
import profile.domain.event.HistoricalEvent
import profile.service.ProfileService
import vn.vhm.common.client.kafka_010.{StringKafkaConsumerFromConfig, StringKafkaProducer}
import vn.vhm.common.domain.Implicits.async
import vn.vhm.common.domain.OptionImplicits.OptionImplicitAny
import vn.vhm.common.domain.profiling.Profiler
import vn.vhm.common.service.DebugServiceNotification
import vn.vhm.common.util.{FutureUtils, JsonHelper, PhoneUtils}

/**
 * <AUTHOR> 7/12/24 16:58
 */
case class PnlCustomerHistoricalConsumer(
                                          config: com.typesafe.config.Config,
                                          profileService: ProfileService,
                                          pnlCustomerDAO: PnlCustomerDAO,
                                          kafkaProducer: StringKafkaProducer,
                                          pnlCustomerChangeLogTopic: String,
                                          index: Int
                                        ) extends StringKafkaConsumerFromConfig(config) with Logging {
  protected val clazz = getClass.getCanonicalName

  override protected val clientId: String = super.clientId + "_" + index

  override def consume(record: ConsumerRecord[String, String]): Unit = Profiler(s"$clazz.consume") {
    try {

      val historicalData = JsonHelper.fromJson[HistoricalEvent[PnlCustomerHistoricalData]](record.value())

      Profiler(s"$clazz.consume-pnlCustomerChangeTrigger") {
        async {
          historicalData.data
            .flatMap(_.id)
            .flatMap(pnlCustomerId => pnlCustomerDAO.select(PnlCustomer(id = pnlCustomerId.toSome))).foreach(pnlCustomer => {
              if (pnlCustomer.vclubUserId.exists(_ > 0)) {
                kafkaProducer.sendOnlyValue(pnlCustomerChangeLogTopic, JsonHelper.toJson(Map(
                  "pnl_customer_id" -> pnlCustomer.id.get,
                  "vclub_user_id" -> pnlCustomer.vclubUserId.get,
                  "timestamp" -> historicalData.timestamp
                )))
              }
            })
        }.onFailure {
          case e: Exception =>
            error(s"Error when consume-pnlCustomerChangeTrigger: ${record.value()}", e)
        }
      }

      if (historicalData.isCreatedOrUpdated) {
        Profiler(s"$clazz.consume-getPnlMappings") {
          val phones = historicalData.data.map(_.normPhone.toSeq).getOrElse(Nil) ++ historicalData.oldData.map(_.normPhone.toSeq).getOrElse(Nil)
          val emails = historicalData.data.map(_.email.toSeq).getOrElse(Nil) ++ historicalData.oldData.map(_.email.toSeq).getOrElse(Nil)
//          val identityNumberDefaults = historicalData.data.map(_.identityNumberDefault.toSeq).getOrElse(Nil) ++ historicalData.oldData.map(_.identityNumberDefault.toSeq).getOrElse(Nil)
          val identityNumbers = historicalData.data.flatMap(_.listIdentityDocument).map(_.flatMap(_.no)).getOrElse(Nil) ++ historicalData.oldData.flatMap(_.listIdentityDocument).map(_.flatMap(_.no)).getOrElse(Nil)

          Await.result {
            for {
              customers <- profileService.searchActiveUserId(
                phone = phones.filter(_.nonEmpty).distinct,
                email = emails.filter(_.nonEmpty).distinct,
                identityNumbers = identityNumbers.filter(_.nonEmpty).distinct
              )
              _ = {
                info(s"Found [${customers.mkString(",")}] customers from pnlCustomerId = ${historicalData.data.flatMap(_.id)}")
              }
              _ <- FutureUtils.processUntilEmpty[String, Unit](customers, userId => {
                profileService.getPnlMappings(userId, true, Nil).unit
              })
            } yield {}
          }
        }
      }

    } catch {
      case e: Exception =>
        error(s"Error while consuming record: ${record.value()}", e)
        DebugServiceNotification.notify(s"PnlCustomerHistoricalConsumer: Error while consuming record: ${record.value()}", e)
    }

  }

}

case class PnlCustomerHistoricalData(
                                      @JsonDeserialize(contentAs = classOf[java.lang.Long])
                                      id: Option[Long],
                                      pnl: String,
                                      pnlProfileId: Option[String],
                                      pnlUserId: Option[String],
                                      phone: Option[String],
                                      email: Option[String],
                                      identityNumberDefault: Option[String],
                                      listIdentityDocument: Option[Seq[PnlCustomerIdentityDocs]],
                                    ) {
  @JsonIgnore
  def normPhone = phone.flatMap(PhoneUtils.normalizePhone(_))

}