package profile.util

import com.github.nscala_time.time.Imports.DateTimeFormat
import profile.util.CTimeUtils.DatePart.DatePart

import java.time.{Instant, LocalDate, ZoneId}
import java.time.format.DateTimeFormatter
import java.util.Calendar
import scala.util.Try

object CTimeUtils {

  val validDateFormatList = Seq(
    "yyyy/MM/dd", "yyyy/dd/MM", "MM/dd/yyyy", "dd/MM/yyyy",
    "yyyy-MM-dd", "yyyy-dd-MM", "MM-dd-yyyy", "dd-MM-yyyy"
  )

  object DatePart extends Enumeration {
    type DatePart = Value
    val YEAR, MONTH, DAY_OF_MONTH = Value
  }

  def stringToLocalDate(date: String, format: String = "yyyy-MM-dd"): LocalDate = {
    val dtf = DateTimeFormatter.ofPattern(format)
    LocalDate.parse(date, dtf)
  }

  def getValidDate(dateStr: String): Option[String] = {
    val valid = validDateFormatList.exists(format => {
      try {
        DateTimeFormat.forPattern(format).parseMillis(dateStr)
        true
      } catch {
        case _: Exception => false
      }
    })
    if (valid) Some(dateStr) else None
  }

  def dailyDiff(hour: Int, min: Int): Long = {
    val cal = Calendar.getInstance()
    cal.set(
      cal.get(Calendar.YEAR),
      cal.get(Calendar.MONTH),
      cal.get(Calendar.DAY_OF_MONTH),
      hour,
      min,
      0
    )
    cal.set(Calendar.MILLISECOND, 0)

    val currentTime = System.currentTimeMillis()
    if (currentTime > cal.getTimeInMillis) {
      cal.add(Calendar.DAY_OF_MONTH, 1)
    }

    cal.getTimeInMillis - currentTime
  }

  def formatToIsoDate(input: Option[String]): Option[String] = {
    input match {
      case Some(value) =>
        validDateFormatList.view
          .map(format => DateTimeFormatter.ofPattern(format))
          .flatMap(formatter => Try(LocalDate.parse(value, formatter)).toOption)
          .headOption
          .map(_.format(DateTimeFormatter.ISO_LOCAL_DATE))
      case _ => None
    }
  }

  def extractDatePart(dateStr: Option[String], partType: DatePart): Option[Int] = {
    val isoDateStr = formatToIsoDate(dateStr)
    isoDateStr.map(isoDate => {
      val parsedDate = LocalDate.parse(isoDate, DateTimeFormatter.ISO_LOCAL_DATE)
      partType match {
        case DatePart.YEAR => parsedDate.getYear
        case DatePart.MONTH => parsedDate.getMonthValue
        case DatePart.DAY_OF_MONTH => parsedDate.getDayOfMonth
        case _ => throw new IllegalArgumentException(s"Invalid partType: $partType")
      }
    })
  }

  def epochToTimestampTz(epochMillis: Option[Long], throwExceptionWhenFail: Boolean): Option[String] = {
    epochMillis match {
      case Some(epoch) if epoch > 0 =>
        try {
          val instant = Instant.ofEpochMilli(epoch)
          val formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss.SSSSSS Z")
            .withZone(ZoneId.systemDefault())
          Some(formatter.format(instant))
        } catch {
          case e: Exception =>
            if (throwExceptionWhenFail) {
              throw new RuntimeException(s"Failed to convert epochMillis: $epochMillis to ISO local date", e)
            } else {
              None
            }
        }
      case _ => None
    }
  }
}
