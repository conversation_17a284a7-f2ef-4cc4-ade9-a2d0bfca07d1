package profile.util

import com.twitter.inject.Logging

import java.net.NetworkInterface
import java.security.SecureRandom
import java.time.Instant

/**
 * <AUTHOR> 6/6/24 09:35
 */
case class SnowflakeId(nodeId: Option[Long] = None, customEpoch: Option[Long] = None) {

  private val UNUSED_BITS: Int = 1 // Sign bit, Unused (always set to 0)
  private val EPOCH_BITS: Int = 41
  private val NODE_ID_BITS: Int = 4 // 16 nodes
  private val SEQUENCE_BITS: Int = 8 // 256 sequence

  private val maxNodeId: Long = (1L << NODE_ID_BITS) - 1
  private val maxSequence: Long = (1L << SEQUENCE_BITS) - 1

  // Custom Epoch (June 1, 2024 Midnight UTC = 2024-06-01T00:00:00Z)
  private val DEFAULT_CUSTOM_EPOCH: Long = 1717200000000L

  if (nodeId.exists(_ < 0L) || nodeId.exists(_ > maxNodeId)) {
    throw new IllegalArgumentException(s"NodeId must be between 0 and $maxNodeId")
  }
  private val _customEpoch = customEpoch.getOrElse(DEFAULT_CUSTOM_EPOCH)
  private val _nodeId = nodeId.getOrElse(createNodeId())

  @volatile
  private var lastTimestamp = -1L
  @volatile
  private var sequence = 0L

  def nextId(): Long = synchronized {

    var currentTimestamp = timestamp()

    if (currentTimestamp < lastTimestamp) throw new IllegalStateException("Invalid System Clock!")

    if (currentTimestamp == lastTimestamp) {
      sequence = (sequence + 1) & maxSequence
      if (sequence == 0) {
        // Sequence Exhausted, wait till next millisecond.
        currentTimestamp = waitNextMillis(currentTimestamp)
      }
    } else {
      // reset sequence to start with zero for the next millisecond
      sequence = 0
    }

    lastTimestamp = currentTimestamp

    currentTimestamp << (NODE_ID_BITS + SEQUENCE_BITS) | (_nodeId << SEQUENCE_BITS) | sequence

  }

  // Get current timestamp in milliseconds, adjust for the custom epoch.
  private def timestamp(): Long = {
    Instant.now().toEpochMilli - _customEpoch
  }

  // Block and wait till next millisecond
  private def waitNextMillis(currentTimestamp: Long): Long = {
    var _currentTimestamp = currentTimestamp
    while (_currentTimestamp == lastTimestamp) {
      _currentTimestamp = timestamp()
    }
    _currentTimestamp
  }

  private def createNodeId(): Long = {
    var nodeId = 0L
    try {
      val sb = new StringBuilder()
      val networkInterfaces = NetworkInterface.getNetworkInterfaces
      while (networkInterfaces.hasMoreElements) {
        val networkInterface = networkInterfaces.nextElement()
        Option(networkInterface.getHardwareAddress)
          .foreach(_.foreach(macPort => sb.append(f"$macPort%02X")))
      }
      nodeId = sb.toString().hashCode()
    } catch {
      case _: Exception => nodeId = new SecureRandom().nextInt()
    }
    nodeId & maxNodeId
  }

  def parse(id: Long): Seq[Long] = {
    val maskNodeId: Long = ((1L << NODE_ID_BITS) - 1) << SEQUENCE_BITS
    val maskSequence: Long = (1L << SEQUENCE_BITS) - 1

    val timestamp: Long = (id >> (NODE_ID_BITS + SEQUENCE_BITS)) + _customEpoch
    val nodeId: Long = (id & maskNodeId) >> SEQUENCE_BITS
    val sequence: Long = id & maskSequence

    Seq(timestamp, nodeId, sequence)
  }

  override def toString() = {
    s"Snowflake Settings [EPOCH_BITS=$EPOCH_BITS, NODE_ID_BITS=$NODE_ID_BITS, SEQUENCE_BITS=$SEQUENCE_BITS, CUSTOM_EPOCH=${_customEpoch}, NodeId=${_nodeId}]"
  }

}

object SnowflakeDefault extends SnowflakeId() with Logging {

  info(toString())

}