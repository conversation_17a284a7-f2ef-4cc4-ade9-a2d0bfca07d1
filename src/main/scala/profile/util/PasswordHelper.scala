package profile.util

import org.apache.commons.codec.binary.Hex
import org.mindrot.jbcrypt.BCrypt
import vn.vhm.common.domain.profiling.Profiler
import vn.vhm.common.util.ZConfig

import java.security.spec.InvalidKeySpecException
import java.security.{NoSuchAlgorithmException, SecureRandom}
import javax.crypto.SecretKeyFactory
import javax.crypto.spec.PBEKeySpec

/**
 * <AUTHOR>
 */
object PasswordHelper {

  val SALT = ZConfig.getString("caas.salt")
  val ITERATIONS = 200000
  val KEY_LENGTH = 512
  val RANDOM = new SecureRandom()

  def passwordIsEmpty(password: String): Boolean = {
    password == "" || password == "-"
  }

  def hashPassword(username: String, password: String, oldSalt: String): String = Profiler(s"${getClass.getCanonicalName}.hashPassword") {
    if (passwordIsEmpty(password)) password
    else try {
      val skf = SecretKeyFactory.getInstance("PBKDF2WithHmacSHA512")
      val spec = new PBEKeySpec(password.toCharArray, (oldSalt + username).getBytes("UTF-8"), ITERATIONS, KEY_LENGTH)
      val key = skf.generateSecret(spec)
      Hex.encodeHexString(key.getEncoded)
    } catch {
      case e@(_: NoSuchAlgorithmException | _: InvalidKeySpecException) =>
        throw new RuntimeException(e)
    }
  }

  def hashPasswordBCrypt(username: String, password: String): String = Profiler(s"${getClass.getCanonicalName}.hashPasswordBCrypt") {
    if (passwordIsEmpty(password)) password
    else {
      val salt = SALT
      BCrypt.hashpw(password, salt)
    }
  }

  //  def genSalt: String = {
  //    val bytes = new Array[Byte](16)
  //    RANDOM.nextBytes(bytes)
  //    Base64.getEncoder.encodeToString(bytes)
  //  }

  //  private val regexPass = "^(?=.*[a-z])(?=.*[A-Z])(?=.*\\d)(?=.*[@$!%*?&-=#])[A-Za-z\\d@$!%*?&]{8,}$".r.pattern
  //private val regexPass = "^(?=.*[a-z])(?=.*[A-Z])(?=.*\\d)(?=.*[@$!%*?&\\-=#\\^])[A-Za-z\\d@$!%*?&\\-=#\\^]{8,}$".r.pattern
  private val regexPass = "^\\d{6}$".r.pattern

  def validatePassword(password: String): Unit = {
    if (!regexPass.matcher(password).matches()) {
      //throw new Exception("Password must contain at least 8 characters, including uppercase, lowercase, number and special character")
      throw new Exception("The PIN code must be exactly 6 digits long")
    }
  }

}
