package profile.util

import com.twitter.inject.Logging
import com.typesafe.config.{Config, ConfigFactory}

import java.io.File
import java.text.Normalizer
import scala.jdk.CollectionConverters.{asScalaBufferConverter, asScalaSetConverter}
import scala.util.{Failure, Success, Try}

object AddressHelper extends Logging {
  // Regions in Vietnam
  private val vnRegions: Map[String, String] = Map(
    //    "DBSH" -> "Đồng bằng sông Hồng",
    //    "DBB" -> "Đông Bắc Bộ",
    //    "TBB" -> "Tây Bắc Bộ",
    //    "BTB" -> "Bắc Trung Bộ",
    //    "DHNTB" -> "Duyên hải Nam Trung Bộ",
    //    "TN" -> "Tây Nguyên",
    //    "DNB" -> "Đông Nam Bộ",
    //    "DBSCL" -> "Đồng bằng sông Cửu Long"
    "1101" -> "Đồng bằng sông Hồ<PERSON>",
    "1102" -> "Đông Bắc Bộ",
    "1103" -> "Tây Bắc Bộ",
    "1104" -> "Bắc Trung Bộ",
    "1105" -> "Duyên hải Nam Trung Bộ",
    "1106" -> "Tây Nguyên",
    "1107" -> "Đông Nam Bộ",
    "1108" -> "Đồng bằng sông Cửu Long"
  )

  // Load address data from config file
  private val addressDataDir = "conf/address"
  private val vnFile = new File(s"$addressDataDir/vn.conf")

  private val addressData: Config = Try {
    if (vnFile.exists()) {
      info(s"|Loading address config from: ${vnFile.getAbsolutePath}")
      ConfigFactory.parseFile(vnFile)
    } else {
      warn(s"|Config file not found: ${vnFile.getAbsolutePath}")
      ConfigFactory.empty()
    }
  } match {
    case Success(config) => config
    case Failure(e) =>
      error(s"|Failed to load config: ${e.getMessage}")
      ConfigFactory.empty()
  }

  // Parse province data
  private val provinceData: Config = if (!addressData.isEmpty) {
    addressData.getConfig("provinces")
  } else {
    ConfigFactory.empty()
  }

  // Create a map for quick lookup of province names
  private val lookupStandardProvinceNameMap: Map[String, String] = {
    if (!provinceData.isEmpty) {
      provinceData.root().keySet().asScala.flatMap { provinceName =>
        val provinceConfig = provinceData.getConfig(provinceName)
        val namingVariations = provinceConfig.getStringList("naming_variations").asScala
        namingVariations.map(variation => cleanProvinceName(variation) -> provinceName)
      }.toMap
    } else {
      Map.empty[String, String]
    }
  }

  def init(): Unit = {
    info("AddressHelper initialized")
  }

  /**
   * Chuẩn hóa tên tỉnh từ tên đầu vào.
   *
   * @param province Tên tỉnh cần chuẩn hóa (có thể là tiếng Việt có dấu hoặc không).
   * @return Tên tỉnh chuẩn nếu tìm thấy, nếu không trả về None.
   */
  def getStandardProvinceName(province: Option[String]): Option[String] = {
    province match {
      case Some(province) =>
        val cleanedProvince = cleanProvinceName(province)
        lookupStandardProvinceNameMap.get(cleanedProvince) match {
          case Some(standardProvinceName) =>
            Some(standardProvinceName)
          case None =>
            warn(s"Province not found: $province")
            None
        }
      case None =>
        None
    }
  }

  /**
   * Lấy tên vùng (ví dụ: Đồng bằng sông Hồng) từ tên tỉnh.
   *
   * @param province Tên tỉnh.
   * @return Tên vùng nếu tìm thấy, nếu không trả về None.
   */
  def getRegionNameByProvinceName(province: Option[String]): Option[String] = {
    getStandardProvinceName(province).flatMap { standardProvinceName =>
      if (provinceData.hasPath(standardProvinceName)) {
        Some(provinceData.getConfig(standardProvinceName).getString("region"))
      } else {
        None
      }
    }
  }

  /**
   * Lấy mã tỉnh từ tên tỉnh.
   *
   * @param province Tên tỉnh.
   * @return Mã tỉnh nếu tìm thấy, nếu không trả về None.
   */
  def getProvinceCode(province: Option[String]): Option[String] = {
    getStandardProvinceName(province).flatMap { standardProvinceName =>
      if (provinceData.hasPath(standardProvinceName)) {
//        Some(provinceData.getConfig(standardProvinceName).getString("code"))
        Some(provinceData.getConfig(standardProvinceName).getString("id")) // Thay đổi từ "code" thành "id"
      } else {
        None
      }
    }
  }

  /**
   * Lấy mã vùng từ tên tỉnh.
   *
   * @param province Tên tỉnh.
   * @return Mã vùng nếu tìm thấy, nếu không trả về None.
   */
  def getRegionCodeByProvinceName(province: Option[String]): Option[String] = {
    getRegionNameByProvinceName(province).flatMap { regionName =>
      vnRegions.find(_._2 == regionName).map(_._1)
    }
  }

  /**
   * Làm sạch tên tỉnh: bỏ dấu, xóa tiền tố/hậu tố, và các ký tự đặc biệt.
   *
   * @param province Tên tỉnh cần làm sạch.
   * @return Tên tỉnh đã làm sạch và chuyển về lowercase.
   */
  private def cleanProvinceName(province: String): String = {
    // Bỏ dấu tiếng Việt
    val clearDiacriticalMarks = removeDiacriticalMarks(province.trim)

    // Xóa tiền tố, hậu tố và ký tự đặc biệt bằng regex
    val cleaned = clearDiacriticalMarks
      .replaceAll("(?i)^(TP|Thanh Pho|Tinh)\\s*", "") // Xóa tiền tố
      .replaceAll("(?i)\\s*(City|Province)$", "")     // Xóa hậu tố
      .replaceAll("[^a-zA-Z0-9\\s]", "")             // Xóa ký tự đặc biệt

    // Xóa khoảng trắng thừa và chuyển về lowercase
    removeRedundantSpaces(cleaned).toLowerCase
  }

  /**
   * Bỏ dấu tiếng Việt.
   *
   * @param string Chuỗi cần bỏ dấu.
   * @return Chuỗi đã bỏ dấu.
   */
  private def removeDiacriticalMarks(string: String): String = {
    Normalizer.normalize(string, Normalizer.Form.NFD)
      .replaceAll("\\p{InCombiningDiacriticalMarks}+", "")
      .replaceAll("Đ", "D")
      .replaceAll("đ", "d")
  }

  /**
   * Xóa khoảng trắng thừa.
   *
   * @param string Chuỗi cần xử lý.
   * @return Chuỗi với khoảng trắng hợp lý.
   */
  private def removeRedundantSpaces(string: String): String = {
    string.trim.replaceAll("\\s+", " ")
  }
}