package profile.util

import com.twitter.inject.Logging
import vn.vhm.common.util.ZConfig

import java.io.File
import scala.io.Source
import scala.util.{Failure, Success, Try}

object EmailHelper extends Logging {

  // Config paths
  private val emailValidateData = "conf/email"

  // helper check config
  private val checkDisposableDomains = ZConfig.getBoolean("email.helper.check_disposable_email", default = true)
  private val checkWhiteListDomains = ZConfig.getBoolean("email.helper.check_domain_whitelist", default = true)
  private val checkBlackListDomains = ZConfig.getBoolean("email.helper.check_domain_blacklist", default = true)
  private val checkWhiteListAddresses = ZConfig.getBoolean("email.helper.check_email_whitelist", default = true)
  private val checkBlackListAddresses = ZConfig.getBoolean("email.helper.check_email_blacklist", default = true)

  // Data sets
  @volatile private var disposableDomains: Set[String] = Set.empty
  @volatile private var whiteListDomains: Set[String] = Set.empty
  @volatile private var blackListDomains: Set[String] = Set.empty
  @volatile private var whiteListAddresses: Set[String] = Set.empty
  @volatile private var blackListAddresses: Set[String] = Set.empty

  // Email normalization strategies
  private val strategies: Map[String, EmailNormalizationStrategy] = Map(
    "gmail.com" -> GmailNormalizationStrategy,
    "googlemail.com" -> GmailNormalizationStrategy,
    "hotmail.com" -> HotmailNormalizationStrategy,
    "live.com" -> LiveNormalizationStrategy,
    "outlook.com" -> OutlookNormalizationStrategy
  )

  // initialize data
  initializeData()

  // region Validate Email
  def isValidEmail(email: String): EmailValidated = {
    if (!CustomUtils.isEmail(email) || !email.contains("@")) {
      return EmailValidated(email, isValid = false, reason = Some("invalid_email_format"))
    }

    val Array(localPart, domain) = email.split("@").map(_.toLowerCase)

    // Kiểm tra white list addresses
    if (checkWhiteListAddresses && whiteListAddresses.contains(email.toLowerCase)) {
      return EmailValidated(email, isValid = true)
    }

    // Kiểm tra white list domains
    if (checkWhiteListDomains && whiteListDomains.contains(domain)) {
      return EmailValidated(email, isValid = true)
    }

    // Kiểm tra black list addresses
    if (checkBlackListAddresses && blackListAddresses.contains(email.toLowerCase)) {
      return EmailValidated(email, isValid = false, reason = Some("blacklisted_email_address"))
    }

    // Kiểm tra black list domains
    if (checkBlackListDomains && blackListDomains.contains(domain)) {
      return EmailValidated(email, isValid = false, reason = Some("blacklisted_email_domain"))
    }

    // Kiểm tra disposable domains
    if (checkDisposableDomains && disposableDomains.contains(domain)) {
      return EmailValidated(email, isValid = false, reason = Some("disposable_email_domain"))
    }

    // Nếu không nằm trong các danh sách trên
    EmailValidated(email, isValid = true)
  }

  def normalize(email: String): String = {
    Try {
      val Array(localPart, domain) = email.split("@")
      (localPart, domain)
    } match {
      case Success((localPart, domain)) => normalize(localPart, domain)
      case Failure(_) => email
    }
  }

  def normalize(localPart: String, domain: String): String = {
    strategies.getOrElse(domain, DefaultEmailNormalizationStrategy).normalize(localPart, domain)
  }
  // endregion

  // region Load Data
  def init(): Unit = {
    println("Initial EmailHelper Successfully")
  }

  private def initializeData(): Unit = {
    if (checkDisposableDomains) disposableDomains = loadDataFile(new File(s"$emailValidateData/disposable-domains.txt"), "Disposable domains")
    if (checkWhiteListDomains) whiteListDomains = loadDataFile(new File(s"$emailValidateData/white-list-domains.txt"), "White list domains")
    if (checkBlackListDomains) blackListDomains = loadDataFile(new File(s"$emailValidateData/black-list-domains.txt"), "Black list domains")
    if (checkWhiteListAddresses) whiteListAddresses = loadDataFile(new File(s"$emailValidateData/white-list-addresses.txt"), "White list addresses")
    if (checkBlackListAddresses) blackListAddresses = loadDataFile(new File(s"$emailValidateData/black-list-addresses.txt"), "Black list addresses")
  }

  private def loadDataFile(file: File, description: String): Set[String] = {
    info(s"|  - Reading $description from: ${file.getAbsolutePath}")
    if (file.exists()) {
      Try {
        val source = Source.fromFile(file)
        try {
          source.getLines().map(_.trim.toLowerCase).filterNot(_.startsWith("#")).toSet
        } finally {
          source.close()
        }
      } match {
        case Success(data) =>
          info(s"|    -- Loaded ${data.size} items")
          data
        case Failure(e) =>
          error(s"|    -- Error reading file: ${e.getMessage}")
          Set.empty
      }
    } else {
      warn(s"|    -- File not found")
      Set.empty
    }
  }
  // endregion

  // region Refresh Data
//  def refreshDisposableDomains(urlFilePath: String): Unit = {
//    info(s"| Refreshing disposable domains from: $urlFilePath")
//    val newDomains = fetchDomainsFromUrl(urlFilePath)
//    if (newDomains.isEmpty) {
//      throw new RuntimeException(s"Failed to fetch domains from $urlFilePath")
//    }
//    val added = newDomains.diff(disposableDomains)
//    val removed = disposableDomains.diff(newDomains)
//    info(s"|    -- Added: ${added.size} domains")
//    info(s"|    -- Removed: ${removed.size} domains")
//    synchronizeUpdate(newDomains)
//  }
//
//  private def fetchDomainsFromUrl(urlPath: String): Set[String] = {
//    Try {
//      val url = new URL(urlPath)
//      val con = url.openConnection().asInstanceOf[HttpURLConnection]
//      con.setRequestMethod("GET")
//      val source = Source.fromInputStream(con.getInputStream)
//      try {
//        source.getLines().map(_.trim.toLowerCase).toSet
//      } finally {
//        source.close()
//      }
//    } match {
//      case Success(domains) => domains
//      case Failure(e) =>
//        error(s"|    -- Failed to fetch domains: ${e.getMessage}")
//        Set.empty
//    }
//  }
//
//  private def synchronizeUpdate(newDomains: Set[String]): Unit = {
//    this.synchronized {
//      disposableDomains = newDomains
//      writeToFile(disposableDomainsFile, newDomains)
//    }
//  }
//
//  private def writeToFile(file: File, data: Set[String]): Unit = {
//    Try {
//      val writer = new PrintWriter(file)
//      try {
//        data.foreach(writer.println)
//      } finally {
//        writer.close()
//      }
//    } match {
//      case Success(_) => info(s"|    -- Successfully wrote to ${file.getAbsolutePath}")
//      case Failure(e) => error(s"|    -- Failed to write: ${e.getMessage}")
//    }
//  }
  // endregion
}

// Các chiến lược normalize email
trait EmailNormalizationStrategy {
  def normalize(localPart: String, domain: String): String
}

object DefaultEmailNormalizationStrategy extends EmailNormalizationStrategy {
  override def normalize(localPart: String, domain: String): String =
    s"${localPart.toLowerCase}@${domain.toLowerCase}"
}

object GmailNormalizationStrategy extends EmailNormalizationStrategy {
  override def normalize(localPart: String, domain: String): String = {
    val cleanLocal = localPart.toLowerCase.split("\\+")(0).replace(".", "")
    if (domain == "googlemail.com") s"$<EMAIL>" else s"$cleanLocal@$domain"
  }
}

object HotmailNormalizationStrategy extends EmailNormalizationStrategy {
  override def normalize(localPart: String, domain: String): String =
    s"${localPart.toLowerCase.split("\\+")(0)}@$domain"
}

object LiveNormalizationStrategy extends EmailNormalizationStrategy {
  override def normalize(localPart: String, domain: String): String =
    s"${localPart.toLowerCase.split("\\+")(0).replace(".", "")}@$domain"
}

object OutlookNormalizationStrategy extends EmailNormalizationStrategy {
  override def normalize(localPart: String, domain: String): String =
    s"${localPart.toLowerCase.split("\\+")(0).replace(".", "")}@$domain"
}

case class EmailValidated(
                           email: String,
                           isValid: Boolean,
                           reason: Option[String] = None
                         ) {
  lazy val domain: String = if (isValid && email.contains("@")) email.split("@")(1) else ""
  lazy val localPart: String = if (isValid && email.contains("@")) email.split("@")(0) else ""
  lazy val normalizedEmail: String = if (isValid && email.contains("@")) EmailHelper.normalize(localPart, domain) else email
}