package profile.util

import com.fasterxml.jackson.databind.exc.MismatchedInputException
import com.fasterxml.jackson.databind.{DeserializationFeature, ObjectMapper, PropertyNamingStrategy}
import com.fasterxml.jackson.module.scala.DefaultScalaModule
import com.fasterxml.jackson.module.scala.experimental.ScalaObjectMapper
import com.twitter.finagle.http.Request
import com.twitter.util.{Return, Throw, Try}
import profile.exception.InvalidParamException
import vn.vhm.common.util.TJsonHelper

/**
 * <AUTHOR> 2/3/24 15:02
 */
object JsonHelperHttpRequest extends TJsonHelper {

  val mapper = new ObjectMapper() with ScalaObjectMapper
  mapper.registerModule(DefaultScalaModule)
  mapper.setPropertyNamingStrategy(PropertyNamingStrategy.SNAKE_CASE)
  mapper.configure(DeserializationFeature.FAIL_ON_NULL_FOR_PRIMITIVES, true)
  mapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false)
  mapper.configure(DeserializationFeature.FAIL_ON_NULL_CREATOR_PROPERTIES, true)

}

object HttpRequestHelper {

  def parse[T: Manifest](request: Request): T = {
    if (!Option(request.contentString).exists(_.nonEmpty)) throw InvalidParamException("request_payload_empty")
    val bodyRequest = Try {
      JsonHelperHttpRequest.fromJson[T](request.contentString)
    } match {
      case Return(r) => r
      case Throw(e) => e match {
        case ex: MismatchedInputException =>
          import scala.collection.JavaConverters._
          ex.getPath.asScala.headOption.map(_.getFieldName) match {
            case Some(field) =>
              throw InvalidParamException("invalid_param", s"field `$field` has invalid value")
            case _ =>
          }
          throw InvalidParamException("invalid_json_data", e.getMessage)
        case _ =>
          throw InvalidParamException("invalid_json_data", e.getMessage)
      }
    }
    bodyRequest
  }
}