package profile.util

import profile.exception.InvalidPhoneException

/**
 * <AUTHOR>
 */
object PhoneUtils {

  def normalizePhone(value: String): String = {
    vn.vhm.common.util.PhoneUtils.normalizePhone(value).getOrElse(throw InvalidPhoneException())
  }

  def normalizePhoneIgnorePlus(value: String): String = {
    val _phone = vn.vhm.common.util.PhoneUtils.normalizePhone(value).getOrElse(throw InvalidPhoneException())
    if (_phone.startsWith("+")) {
      _phone.substring(1)
    } else _phone
  }

  def optNormalizePhoneIgnorePlus(value: String): Option[String] = {
    vn.vhm.common.util.PhoneUtils.normalizePhone(value).map(_phone => {
      if (_phone.startsWith("+")) {
        _phone.substring(1)
      } else _phone
    })
  }

  def normalizePhoneStartZero(value: String): String = {
    val phone = PhoneUtils.normalizePhoneIgnorePlus(value)
    if (phone.startsWith("84")) {
      phone.replaceFirst("84", "0")
    } else {
      phone
    }
  }
}
