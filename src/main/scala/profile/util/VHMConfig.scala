package profile.util

import org.slf4j.{Logger, LoggerFactory}
import vn.vhm.common.util.ZConfig

/**
 * <AUTHOR>
 */
object VHMConfig {

  val sqlLogger: Option[Logger] = if (ZConfig.getBoolean("main.sql_debug", false)) Some(LoggerFactory.getLogger("SqlDebugLogger")) else None

  val verifyPhone: Map[String, Boolean] = ZConfig.optConf("caas.verify_phone").orNull match {
    case null => Map.empty[String, Boolean]
    case x =>
      var map = Map.empty[String, Boolean]
      val iterator = x.entrySet().iterator()
      while (iterator.hasNext) {
        val item = iterator.next()
        val key = item.getKey
        map += (key -> x.getBoolean(key))
      }
      map
  }

  def isNeedVerifyPhone(oauthType: String): Boolean = verifyPhone.getOrElse(oauthType, true)

}
