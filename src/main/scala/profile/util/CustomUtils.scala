package profile.util

import com.twitter.finagle.http.Request
import org.apache.commons.io.{FileUtils, IOUtils}
import vn.vhm.common.domain.profiling.Profiler
import vn.vhm.common.util.Utils
import com.twitter.util.Future
import vn.vhm.common.domain.OptionImplicits.OptionImplicitAny

import java.io.File
import java.net.URL
import java.time.format.DateTimeFormatter
import java.util.concurrent.TimeUnit
import java.util.{Calendar, TimeZone}
import scala.util.Random

/**
 * <AUTHOR>
 */
object CustomUtils {

  private val emailRegex = """^[a-zA-Z0-9\.!#$%&'*+/=?^_`{|}~-]+@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)+$""".r

  def isEmail(value: String): Boolean = {
    emailRegex.findFirstMatchIn(value).isDefined
  }

  private val random = Random

  def randomInt(from: Int = Integer.MIN_VALUE, to: Int = Integer.MAX_VALUE): Int = {
    val randomVal = random.nextInt(to)
    if (randomVal < from) randomInt(from, to) else randomVal
  }

  def isValidEmail(email: String, emailRegex: String): Boolean = {
    val rex = emailRegex.r
    email match {
      case rex(email) => true
      case _ => false
    }
  }

  def getNickname(email: Option[String], defaultValue: String = ""): String = {
    email match {
      case Some(x) => x.split("@")(0)
      case _ => defaultValue
    }
  }

  val timeUnits = Seq(
    (1l, Calendar.MILLISECOND),
    (TimeUnit.SECONDS.toMillis(1), Calendar.SECOND),
    (TimeUnit.MINUTES.toMillis(1), Calendar.MINUTE),
    (TimeUnit.HOURS.toMillis(1), Calendar.HOUR_OF_DAY),
    (TimeUnit.DAYS.toMillis(1), Calendar.DAY_OF_MONTH)
  )

  def roundTimeByInterval(time: Long, interval: Long): Long = {
    val cal = Calendar.getInstance()
    cal.setTimeInMillis(time)
    roundLevelByInterval(interval).foreach(i => cal.set(i, 0))
    incByItv(cal.getTimeInMillis, interval)(_ + interval < time)
  }

  def incByItv(initValue: Long, step: Long)(condition: Long => Boolean): Long =
    if (!condition(initValue))
      initValue
    else
      incByItv(initValue + step, step)(condition)

  private def roundLevelByInterval(interval: Long): Seq[Int] = {
    timeUnits.filter(_._1 <= interval).map(_._2)
  }

  def readBinaryFile(file: String): Array[Byte] = {
    FileUtils.readFileToByteArray(new File(file))
  }

  private val timestamptzFormat = "yyyy-MM-dd HH:mm:ss.SSSX"
  private val timestampFormat = "yyyy-MM-dd HH:mm:ss.SSS"
  private val timestamptzFormatter = DateTimeFormatter.ofPattern(timestamptzFormat)
  private val timestampFormatter = DateTimeFormatter.ofPattern(timestampFormat)

  //  def timestamptzToLong(timestamptz: String): Long = {
  //    ZonedDateTime.parse(timestamptz, timestamptzFormatter).toInstant.toEpochMilli
  //  }

  //  def longToTimestamptz(timestamptz: Long): String = {
  //    TimeUtils.timeToString(timestamptz, timestamptzFormat)
  //  }

  def timestamptzToLong(timestamptz: java.sql.Timestamp): Long = {
    timestamptz match {
      case x: org.postgresql.util.PGTimestamp => Option(x.getCalendar).fold(x.getTime)(_.getTimeInMillis)
      case _ =>
        timestamptz.toLocalDateTime
          .atZone(TimeZone.getDefault.toZoneId)
          .toInstant
          .toEpochMilli
    }
  }

  def longToTimestamptz(timeAsLong: Long): java.sql.Timestamp = {
    new org.postgresql.util.PGTimestamp(timeAsLong)
    //    val zonedDateTime = ZonedDateTime.ofInstant(Instant.ofEpochMilli(timeAsLong), TimeZone.getDefault.toZoneId)
    //    java.sql.Timestamp.from(zonedDateTime.toInstant)
  }

  def exec[T](fn: => Future[T]): Future[T] = {
    try {
      fn
    } catch {
      case e: Exception => Future.exception(e)
    }
  }

  def downloadPhoto(url: String, fileLocal: String): String = Profiler(s"${getClass.getCanonicalName}.downloadPhoto") {
    val conn = new URL(url).openConnection()
    conn.setConnectTimeout(60000)
    conn.setConnectTimeout(60000)
    conn.addRequestProperty("User-Agent", "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/88.0.4324.96 Safari/537.36")
    Utils.using(conn.getInputStream) {
      in =>
        FileUtils.writeByteArrayToFile(new File(fileLocal), IOUtils.toByteArray(in))
        fileLocal
    }
  }

  def getGenderCode(value: Option[String]): Option[String] = {
    value match {
      case Some(g) if "male".equalsIgnoreCase(g) || "m".equalsIgnoreCase(g) || "nam".equalsIgnoreCase(g) => "M".toSome
      case Some(g) if "female".equalsIgnoreCase(g) || "f".equalsIgnoreCase(g) || "nữ".equalsIgnoreCase(g) || "nu".equalsIgnoreCase(g) => "F".toSome
      case None => None
      case _ => "O".toSome
    }
  }

  def getLang(language: String): Option[String] = {
    Some(language).filter(_.nonEmpty)
      .map(_.split("-", -1).toSeq).getOrElse(Nil)
      .headOption.map(_.toLowerCase)
  }

  def getLang(request: Request): String = CustomUtils.getLang(
    Seq("Accept-Language", "accept-language").flatMap(h => request.headerMap.get(h))
      .headOption
      .getOrElse("")
  ).getOrElse(Constant.DEFAULT_LANGUAGE)

  def isEmpty(value: Option[String]): Boolean = value.isEmpty || value.exists(_.isEmpty)

}
