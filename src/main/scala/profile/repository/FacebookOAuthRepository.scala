package profile.repository

import java.net.SocketTimeoutException
import java.util.concurrent.ConcurrentHashMap
import com.fasterxml.jackson.databind.JsonNode
import com.fasterxml.jackson.databind.node.ObjectNode
import org.apache.commons.codec.digest.HmacUtils
import profile.domain.LoginType
import profile.domain.entity.RegisterChannelType
import scalaj.http.Http
import vn.vhm.common.exception.InternalError
import vn.vhm.common.util.{JsonHelper, ZConfig}

import scala.collection.JavaConverters._

/**
 * <AUTHOR> 3/29/20 11:49 AM
 */
object FacebookOAuthRepository {

  private val appSecret = ZConfig.getString("caas.oauth.facebook.app_secret", null)

  private final val mapFbCodeWithInfo = {
    val map = new ConcurrentHashMap[String, (JsonNode, Long)]().asScala
    ZConfig.getMapOrDefault("test.facebook_oauth_info").foreach(pair => {
      val jsonNode = JsonHelper.toNode[ObjectNode](pair._2)
      map(s"${jsonNode.path("id").asText("")}-${pair._1}") = (jsonNode, 0L)
    })
    map
  }

  def getData(facebookId: String, token: String): JsonNode = {
    val key = s"$facebookId-$token"
    val time = System.currentTimeMillis()

    mapFbCodeWithInfo.filter(_._2._2 > 0L).toSeq.foreach(pair => if (time - pair._2._2 > 10L * 60L * 1000L) mapFbCodeWithInfo.remove(pair._1))

    mapFbCodeWithInfo.get(key) match {
      case Some(x) => x._1
      case _ =>
        val resp = {
          try {
            val appSecretProof = HmacUtils.hmacSha256Hex(FacebookOAuthRepository.appSecret, token)
            val bodyResp = Http(s"https://graph.facebook.com/me/?access_token=$token&appsecret_proof=$appSecretProof&fields=id,name,first_name,last_name,email")
              .timeout(30000, 30000)
              .asString.body
            val jsonNode = JsonHelper.readTree(bodyResp)
            if (!jsonNode.isObject || jsonNode.size() == 0) throw InternalError("Error when getting facebook info")
            jsonNode
          } catch {
            case _: SocketTimeoutException => throw new SocketTimeoutException("Timeout when getting facebook info")
            case _: Exception => throw new Exception("Error when getting facebook info")
          }
        }
        mapFbCodeWithInfo(key) = (resp, time)
        resp
    }

  }

  def releaseCache(fbId: String, code: String): Unit = {
    val key = s"$fbId-$code"
    mapFbCodeWithInfo.get(key).filter(_._2 > 0L).foreach(_ => mapFbCodeWithInfo.remove(key))
  }
}

case class FacebookOAuthRepository(facebookId: String, token: String) extends OAuthRepository {

  private val response = FacebookOAuthRepository.getData(facebookId, token)

  private val id = Option(response.path("id").asText(null)).getOrElse(throw new IllegalArgumentException("Illegal token!"))

  private val username = s"${RegisterChannelType.FACEBOOK.toString}-$id"

  private var name: Option[String] = None
  private var familyName: Option[String] = None
  private var givenName: Option[String] = None
  private var phoneNumber: Option[String] = None

  parseName()

  private def parseName() = {
    val names = OAuthRepositoryUtils.parseName(
      response.path("name").asText(null),
      response.path("first_name").asText(null), response.path("last_name").asText(null)
    )
    name = Some(names._1)
    familyName = Some(names._2)
    givenName = Some(names._3)
  }

  override def oauthType: String = LoginType.OAUTH_FACEBOOK.toString

  override def getId: String = id

  override def getUsername: String = username

  override def getName: Option[String] = name

  override def getFamilyName: Option[String] = familyName

  override def getGivenName: Option[String] = givenName

  override def getEmail: Option[String] = Option(response.path("email").asText(null))

  override def verifiedEmail: Boolean = false

  override def getAvatar: Option[String] = Some("https://graph.facebook.com/" + id + "/picture?type=large")

  override def getPhoneNumber: Option[String] = phoneNumber

  override def setPhoneNumber(phoneNumber: String): OAuthRepository = {
    this.phoneNumber = Some(phoneNumber)
    this
  }

}
