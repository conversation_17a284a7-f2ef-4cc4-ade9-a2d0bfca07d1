package profile.repository

import caas.domain.OAuthInfo

/**
 * <AUTHOR>
 *         created on 9/19/16.
 */
trait OAuthRepository {
  def getId: String

  def getUsername: String

  def getName: Option[String]

  def getFamilyName: Option[String]

  def getGivenName: Option[String]

  def verifiedEmail: <PERSON><PERSON><PERSON>

  def getEmail: Option[String]

  def getAvatar: Option[String]

  def getPhoneNumber: Option[String]

  def oauthType: String

  def setPhoneNumber(phoneNumber: String): OAuthRepository

  def asOAuthInfo: OAuthInfo = OAuthInfo(oauthType = oauthType, username = getUsername, id = Some(getId))
}

object OAuthRepositoryUtils {
  def parseName(name: String, familyName: String, givenName: String): (String, String, String) = {
    var newName: String = name
    var newFamilyName: String = familyName
    var newGivenName: String = givenName
    if (familyName != null || givenName != null) {
      newName = s"${if (familyName != null) familyName else ""} ${if (givenName != null) givenName else ""}"
      if (newName.isEmpty) newName = null
    } else {
      if (name != null) {
        val splitName = name.split(" ")
        newFamilyName = splitName.slice(0, splitName.length).mkString(" ")
        newGivenName = splitName.last
      }
    }
    (newName, newFamilyName, newGivenName)
  }
}

