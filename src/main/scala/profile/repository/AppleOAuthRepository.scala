package profile.repository

import java.io.FileReader
import java.security.PrivateKey
import java.util.{Base64, Date}
import com.fasterxml.jackson.databind.JsonNode
import com.twitter.inject.Logging
import io.jsonwebtoken.impl._
import io.jsonwebtoken.{Jwts, SignatureAlgorithm}
import org.bouncycastle.asn1.pkcs.PrivateKeyInfo
import org.bouncycastle.openssl.PEMParser
import org.bouncycastle.openssl.jcajce.JcaPEMKeyConverter
import profile.domain.LoginType
import scalaj.http.Http
import vn.vhm.common.util.{<PERSON>sonHelper, Utils, ZConfig}

import scala.collection.JavaConverters._
import scala.collection._
import java.util.concurrent.ConcurrentHashMap
import com.fasterxml.jackson.databind.node.ObjectNode
import org.apache.commons.codec.CharEncoding
import profile.domain.entity.RegisterChannelType

/**
 * <AUTHOR> 3/29/20 11:50 AM
 */
object AppleOAuthRepository extends Logging {

  val oauthUrl = ZConfig.getString("caas.oauth.apple.auth_url")
  val clientId = ZConfig.getString("caas.oauth.apple.client_id")
  val teamId = ZConfig.getString("caas.oauth.apple.team_id")
  val keyId = ZConfig.getString("caas.oauth.apple.key_id")
  val privateKeyPath = ZConfig.getString("caas.oauth.apple.private_key_path")

  private final val mapAppleCodeWithInfo = new ConcurrentHashMap[String, (JsonNode, Long)]().asScala

  ZConfig.getMapOrDefault("test.apple_oauth_info").foreach(pair => {
    val jsonNode = JsonHelper.toNode[ObjectNode](pair._2)
    mapAppleCodeWithInfo(s"$clientId-${jsonNode.path("sub").asText("")}-${pair._1}") = (jsonNode, 0L)
  })

  def releaseCache(appleId: String, code: String, reqClientId: Option[String]): Unit = {
    val key = s"${reqClientId.getOrElse(clientId)}-$appleId-$code"
    mapAppleCodeWithInfo.get(key).filter(_._2 > 0L).foreach(_ => mapAppleCodeWithInfo.remove(key))
  }

  private def _getIdTokenPayload(appleId: String, code: String, reqClientId: Option[String]): JsonNode = {
    val tmpCID = reqClientId.getOrElse(clientId)
    val clientSecret = genJwt(tmpCID, teamId, keyId, privateKeyPath)
    val resp = Http(oauthUrl).timeout(30000, 30000)
      .postForm(Seq(
        ("client_id", tmpCID), ("client_secret", clientSecret), ("code", code), ("grant_type", "authorization_code")
      )).asString.body
    info(s"${AppleOAuthRepository.getClass.getCanonicalName}.getAuthorizationToken($code, $tmpCID, $clientSecret)\t${resp}")

    val idToken = Option(JsonHelper.readTree(resp).path("id_token").asText(null)).filter(_.nonEmpty).getOrElse(throw new IllegalArgumentException("Illegal token!"))
    val decoded = new String(Base64.getDecoder.decode(idToken.split("\\.")(1)), CharEncoding.UTF_8)

    val jsonNode = JsonHelper.readTree(decoded)
    if (jsonNode.path("aud").asText("") != tmpCID || jsonNode.path("sub").asText("") != appleId) throw new IllegalArgumentException("Illegal token!")
    jsonNode
  }

  def getIdTokenPayload(appleId: String, code: String, reqClientId: Option[String]): JsonNode = {
    val key = s"${reqClientId.getOrElse(clientId)}-$appleId-$code"
    val time = System.currentTimeMillis()

    mapAppleCodeWithInfo.filter(_._2._2 > 0L).toSeq.foreach(pair => if (time - pair._2._2 > 10L * 60L * 1000L) mapAppleCodeWithInfo.remove(pair._1))

    mapAppleCodeWithInfo.get(key) match {
      case Some(x) => x._1
      case _ =>
        val resp = _getIdTokenPayload(appleId, code, reqClientId)
        mapAppleCodeWithInfo(key) = (resp, time)
        resp
    }

  }

  private def genJwt(clientId: String, teamId: String, keyIdentifier: String, privateKeyPath: String): String = {
    val timeNow = System.currentTimeMillis()

    /**
     * issuer — Software organization who issues the token.
     * subject — Intended user of the token.
     * audience — Basically identity of the intended recipient of the token.
     * expiresIn — Expiration time after which the token will be invalid.
     * algorithm — Encryption algorithm to be used to protect the token.
     */
    val jwtBuilder = Jwts.builder()
      .setClaims(
        new DefaultClaims()
          .setIssuer(teamId)
          .setIssuedAt(new Date(timeNow))
          .setExpiration(new Date(timeNow + 1 * 24 * 60 * 60 * 1000L))
          .setAudience("https://appleid.apple.com")
          .setSubject(clientId)
      )
      .setHeader(
        new DefaultJwsHeader().setAlgorithm("ES256").setKeyId(keyIdentifier)
      )
    jwtBuilder.signWith(getPrivateKey(privateKeyPath), SignatureAlgorithm.ES256)

    jwtBuilder.compact()
  }

  private def getPrivateKey(keyPath: String): PrivateKey = {
    Utils.using(new FileReader(keyPath)) {
      reader => {
        val pemParser = new PEMParser(reader)
        val obj = pemParser.readObject().asInstanceOf[PrivateKeyInfo]
        new JcaPEMKeyConverter().getPrivateKey(obj)
      }
    }
  }
}

case class AppleOAuthRepository(appleId: String, code: String, name: Option[String], reqClientId: Option[String]) extends OAuthRepository {

  private val response = AppleOAuthRepository.getIdTokenPayload(appleId, code, reqClientId)

  private val id = Option(response.path("sub").asText(null)).getOrElse(throw new IllegalArgumentException("Illegal token!"))

  private var phoneNumber: Option[String] = None

  private val username = s"${RegisterChannelType.APPLE}-$id"

  override def getId: String = id

  override def getUsername: String = username

  override def getName: Option[String] = name

  override def getFamilyName: Option[String] = None

  override def getGivenName: Option[String] = None

  override def getEmail: Option[String] = Option(response.path("email").asText(null))

  override def verifiedEmail: Boolean = false

  override def getAvatar: Option[String] = None

  override def getPhoneNumber: Option[String] = phoneNumber

  override def oauthType: String = LoginType.OAUTH_APPLE.toString

  override def setPhoneNumber(phoneNumber: String): OAuthRepository = {
    this.phoneNumber = Some(phoneNumber)
    this
  }

}
