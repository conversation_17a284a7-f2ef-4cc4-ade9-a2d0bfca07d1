package profile.repository

import com.twitter.util.Future
import profile.repository.FileInternalDefine.InternalPath

import java.io.{File, InputStream}

object FileInternalDefine {
  type Path = String
  type InternalPath = String
  type RawStoragePath = String
}

case class FileResponse(contentType: String, is: InputStream)

case class PresignUploadData(
                              presignUrl: String,
                              presignHeaders: Map[String, String]
                            )

case class PresignDownloadData(presignUrl: String)


trait FileRepository {

  def getInternalPath(pathOrInternalPath: String): InternalPath

  def put(filePath: String, is: InputStream, size: Long, contentType: String, metadata: Map[String, String], tags: Map[String, String]): InternalPath

  def preparePut(localFilePath: String, contentType: String, filePath: String, metadata: Map[String, String], tags: Map[String, String]): InternalPath

  def put(localFilePath: String, contentType: String, filePath: String, metadata: Map[String, String], tags: Map[String, String]): InternalPath

  def del(filePath: String): Unit

  def downloadPath(filePath: String): Future[FileResponse]

  def buildPreSignUrlForUpload(filePath: String, fileContentType: String,
                               metadata: Map[String, String], tags: Map[String, String]): Future[PresignUploadData]

  def buildPreSignUrlForDownload(filePath: String): Future[PresignDownloadData]

  def existFileOrFolder(fileOrFolderPath: String): Future[Boolean]

  def putPhotoTempFromFile(externalId: String, data: File, contentType: String): String

}
