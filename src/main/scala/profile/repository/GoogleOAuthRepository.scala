package profile.repository

import java.net.SocketTimeoutException
import java.util.concurrent.ConcurrentHashMap
import com.fasterxml.jackson.databind.JsonNode
import com.fasterxml.jackson.databind.node.ObjectNode
import profile.domain.LoginType
import profile.domain.entity.RegisterChannelType
import scalaj.http.Http
import vn.vhm.common.exception.InternalError
import vn.vhm.common.util.{JsonHelper, ZConfig}

import scala.collection.JavaConverters._

/**
 * <AUTHOR> 3/29/20 11:49 AM
 */
object GoogleOAuthRepository {

  val parseProjectIdPattern = "^([^-\\.]+)".r.pattern
  val appID = ZConfig.getStringSeq("caas.oauth.google.app_id", Nil)

  private final val mapGGCodeWithInfo = {
    val map = new ConcurrentHashMap[String, (JsonNode, Long)]().asScala
    ZConfig.getMapOrDefault("test.google_oauth_info").foreach(pair => {
      val jsonNode = JsonHelper.toNode[ObjectNode](pair._2)
      map(s"${jsonNode.path("sub").asText("")}-${pair._1}") = (jsonNode, 0L)
    })
    map
  }

  def getData(ggId: String, token: String): JsonNode = {
    val key = s"$ggId-$token"
    val time = System.currentTimeMillis()

    mapGGCodeWithInfo.filter(_._2._2 > 0L).toSeq.foreach(pair => if (time - pair._2._2 > 10L * 60L * 1000L) mapGGCodeWithInfo.remove(pair._1))

    mapGGCodeWithInfo.get(key) match {
      case Some(x) => x._1
      case _ =>
        val resp = try {
          val bodyResp = Http("https://www.googleapis.com/oauth2/v3/tokeninfo?id_token=" + token).timeout(30000, 30000).asString.body
          val jsonNode = JsonHelper.readTree(bodyResp)
          if (!jsonNode.isObject || jsonNode.size() == 0) throw InternalError("Error when getting google info")
          jsonNode
        } catch {
          case _: SocketTimeoutException => throw new SocketTimeoutException("Timeout when getting google info")
          case _: Exception => throw new Exception("Error when getting google info")
        }
        mapGGCodeWithInfo(key) = (resp, time)
        resp
    }

  }

  def releaseCache(ggId: String, code: String): Unit = {
    val key = s"$ggId-$code"
    mapGGCodeWithInfo.get(key).filter(_._2 > 0L).foreach(_ => mapGGCodeWithInfo.remove(key))
  }
}

case class GoogleOAuthRepository(googleId: String, token: String) extends OAuthRepository {

  private val response = GoogleOAuthRepository.getData(googleId, token)

  Option(response.path("azp").asText(null)) match {
    case Some(s) =>
      val m = GoogleOAuthRepository.parseProjectIdPattern.matcher(s)
      if (!m.find() || !GoogleOAuthRepository.appID.contains(m.group(1))) throw new IllegalArgumentException("Illegal token!")
    case _ => throw new IllegalArgumentException("Illegal token!")
  }

  private val id = Option(response.path("sub").asText(null)).getOrElse(throw new IllegalArgumentException("Illegal token!"))
  private val username = s"${RegisterChannelType.GOOGLE.toString}-$id"

  private var familyName: Option[String] = None
  private var givenName: Option[String] = None
  private var name: Option[String] = None
  private var phoneNumber: Option[String] = None

  parseName()

  private def parseName(): Unit = {
    val names = OAuthRepositoryUtils.parseName(
      response.path("name").asText(null),
      response.path("family_name").asText(null),
      response.path("given_name").asText(null)
    )
    name = Some(names._1)
    familyName = Some(names._2)
    givenName = Some(names._3)
  }

  override def getId: String = id

  override def getUsername: String = username

  override def getName: Option[String] = name

  override def getFamilyName: Option[String] = familyName

  override def getGivenName: Option[String] = givenName

  override def getEmail: Option[String] = Option(response.path("email").asText(null))

  override def verifiedEmail: Boolean = true

  override def getAvatar: Option[String] = Option(response.path("picture").asText(null))

  override def getPhoneNumber: Option[String] = phoneNumber

  override def oauthType: String = LoginType.OAUTH_GOOGLE.toString

  override def setPhoneNumber(phoneNumber: String): OAuthRepository = {
    this.phoneNumber = Some(phoneNumber)
    this
  }
}
