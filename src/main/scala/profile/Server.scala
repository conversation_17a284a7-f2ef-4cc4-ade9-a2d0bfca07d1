package profile

import akka.actor.{<PERSON><PERSON><PERSON>, ActorSystem}
import akka.vhm.AkkaImplicits
import akka.vhm.AkkaImplicits.CommonActorImplicits
import com.google.inject.name.Names
import com.google.inject.{Inject, Singleton}
import com.twitter.finagle.Http
import com.twitter.finagle.http.Request
import com.twitter.finatra.filters.MergedFilter
import com.twitter.finatra.http.HttpServer
import com.twitter.finatra.http.filters._
import com.twitter.finatra.http.routing.HttpRouter
import com.twitter.util.{HandleSignal, StorageUnit}
import profile.consumer.{CustomerIdentityVerificationEventConsumer, CustomerNewSignedInEventConsumer, PnlCustomerHistoricalConsumer}
import profile.controller.http
import profile.controller.http.filter.common._
import profile.controller.http.filter.user.{AuthUserParser, CmsAuthUserParser}
import profile.module._
import profile.util.{Address<PERSON><PERSON><PERSON>, <PERSON>ime<PERSON><PERSON><PERSON>, EmailHelper}
import vn.vhm.common.controller.ProfilerController
import vn.vhm.common.domain.profiling.Profiler
import vn.vhm.common.service.DebugServiceNotification
import vn.vhm.common.util.{JsonHelper, ZConfig}

import java.util.UUID


/**
 * Created by SangDang on 9/8/
 * */
object MainApp extends Server

class Server extends HttpServer { // with ThriftServer

  override protected def defaultHttpPort: String = ZConfig.getString("server.http.port", ":8080")

  //  override protected def defaultThriftPort: String = ZConfig.getString("server.thrift.port", ":8082")

  override protected def disableAdminHttpServer: Boolean = ZConfig.getBoolean("server.admin.disable", true)

  override def modules: Seq[com.google.inject.Module] = Seq(DependencyModule)

  override protected def configureHttpServer(server: Http.Server): Http.Server = {
    super.configureHttpServer(server)
    server.withMaxHeaderSize(StorageUnit.fromKilobytes(ZConfig.getInt("server.http.max_header_size_in_kb", 16)))
  }

  override protected def configureHttpsServer(server: Http.Server): Http.Server = {
    super.configureHttpsServer(server)
    server.withMaxHeaderSize(StorageUnit.fromKilobytes(ZConfig.getInt("server.http.max_header_size_in_kb", 16)))
  }


  override protected def configureHttp(router: HttpRouter): Unit = {
    router
      .filter[CORSFilter](beforeRouting = true)
      .filter[ExceptionMappingFilter[Request]]

      .add[CommonExceptAccessLoggingFilters, ProfilerController]
      .add[CommonFilters, ContextParser, http.AuthenJwtController]
      .add[CommonFilters, CmsAuthUserParser, http.AdminUserController]

      .add[CommonFilters, ContextParser, AuthUserParser, http.UserController]
      .add[CommonFilters, ContextParser, AuthUserParser, http.OwnerVerificationController]
      .add[CommonFilters, ContextParser, AuthUserParser, http.VbdIntegrationController]

      .add[CommonFilters, http.InternalUserController]
      .add[CommonFilters, http.InternalUserManagementController]
      .add[CommonFilters, http.InternalAuthController]

      //      .add[CommonFilters, AuthUserParser, http.AuthenController]
      //      .add[CommonFilters, AuthUserParser, http.AuthenV2Controller]
      //      .add[CommonFilters, AdminAuthenEnhance, AllowConfigRoleFilter, http.AdminController]

      .exceptionMapper[CommonExceptionMapping]
      .exceptionMapper[CaseClassExceptionMapping]
      .exceptionMapper[JsonParseExceptionMapping]
  }

  //  override protected def configureThrift(router: ThriftRouter): Unit = {
  //    router
  //      .filter[com.twitter.finatra.thrift.filters.AccessLoggingFilter]
  //      .add[TUserProfileController]
  //  }

  protected override def warmup(): Unit = {

    Runtime.getRuntime.addShutdownHook(new Thread(() => {
      info("[ShutdownHook] Service is shutting down...")
      DebugServiceNotification.notify("[ShutdownHook] Service is shutting down...")
    }))

    HandleSignal("TERM") { signal =>
      info(s"[Signal-$signal] Service is shutting down...")
      DebugServiceNotification.notify(s"[Signal-$signal] Service is shutting down...")
      System.exit(0)
    }

    injector.instance[Seq[PnlCustomerHistoricalConsumer]].foreach(_.startConsume())

    injector.instance[ActorRef](Names.named("general"))

    injector.instance[CustomerNewSignedInEventConsumer].startConsume()

    if (ZConfig.getBoolean("kafka.customer_identity_verification_event_consumer_enable", true)) {
      injector.instance[CustomerIdentityVerificationEventConsumer].startConsume()
    }

    DebugServiceNotification.notify("Service started")

    AddressHelper.init()
    EmailHelper.init()

    initAkkaRequest()

  }

  private def initAkkaRequest() = {

    import ZConfig.ImplicitConfig
    import profile.service.scheduler.GeneralScheduler._

    import scala.concurrent.duration._

    val system = injector.instance[ActorSystem]
    val actor = injector.instance[ActorRef](Names.named("general"))

    val schedulerSyncCustomerRankingCfg = ZConfig.getConf("sync_schedule.sync_customer_ranking")
    if (schedulerSyncCustomerRankingCfg.getBoolean("enable", true)) {
      system.scheduleOnce(10.second) {
        actor ! SyncCustomerRanking(schedulerSyncCustomerRankingCfg.getInt("interval_in_second"))
      }
    }

    val schedulerSyncCustomerCdpProfileCfg = ZConfig.getConf("sync_schedule.sync_customer_cdp_profile")
    if (schedulerSyncCustomerCdpProfileCfg.getBoolean("enable", true)) {
      system.scheduleOnce(15.second) {
        actor ! SyncCustomerCdpProfile(schedulerSyncCustomerCdpProfileCfg.getInt("interval_in_second"))
      }
    }

    ZConfig.optConf("sync_schedule.reinit_ranking_day0").foreach(schedulerReInitRankingDay0Cfg => {
      if (schedulerReInitRankingDay0Cfg.getBoolean("enable", false)) {
        val initFromUserId = schedulerReInitRankingDay0Cfg.getLong("init_from_user_id", 0)
        val initToUserId = schedulerReInitRankingDay0Cfg.getLong("init_to_user_id", 0)
        val delayInSecond = schedulerReInitRankingDay0Cfg.getInt("delay_in_second", 60)
        system.scheduleOnce(delayInSecond.second) {
          actor ! ReInitRankingDay0(initFromUserId, initToUserId)
        }
      }
    })

    ZConfig.optConf("sync_schedule.rescan_ranking_day0").foreach(schedulerReScanRankingDay0Cfg => {
      if (schedulerReScanRankingDay0Cfg.getBoolean("enable", false)) {
        val scanFromUserId = schedulerReScanRankingDay0Cfg.getLong("scan_from_user_id", 0)
        val scanToUserId = schedulerReScanRankingDay0Cfg.getLong("scan_to_user_id", 0)
        val delayInSecond = schedulerReScanRankingDay0Cfg.getInt("delay_in_second", 60)
        system.scheduleOnce(delayInSecond.second) {
          actor ! ReScanRankingDay0(scanFromUserId, scanToUserId)
        }
      }
    })

    if (ZConfig.getBoolean("debug_schedule.log_profiler", false)) {
      system.schedule(10.second, 1.second) {
        println(JsonHelper.toJson(Profiler.getProfiler(Nil, None), true))
        println("==========================================================")
        println("==========================================================")
        println("==========================================================")
        println("==========================================================")
      }
    }

    val schedulerDeactivateIdentityVerificationCfg = ZConfig.getConf("sync_schedule.deactivate_identity_verification")

    if (schedulerDeactivateIdentityVerificationCfg.getBoolean("enable", false)) {

      val scheduleAtHour = schedulerDeactivateIdentityVerificationCfg.getInt("schedule_at_hour", 2)
      val syncBeforeInMillis = schedulerDeactivateIdentityVerificationCfg.getInt("sync_before_in_hour").hour.toMillis

      AkkaImplicits.CommonActorImplicits(system).scheduleOnce(1.minutes) {
        val reqId = UUID.randomUUID().toString
        info(s"[DeactivateIdentityVerificationScheduler] PutReq-$reqId")
        actor ! DeactivateIdentifyVerificationReq(reqId, syncBeforeInMillis)
      }

      AkkaImplicits.CommonActorImplicits(system).schedule(CTimeUtils.dailyDiff(scheduleAtHour, 0).millis, 1.day) {
        val reqId = UUID.randomUUID().toString
        info(s"[DeactivateIdentityVerificationScheduler] PutReq-$reqId")
        actor ! DeactivateIdentifyVerificationReq(reqId, syncBeforeInMillis)
      }

    }

  }

}

@Singleton
class CommonExceptAccessLoggingFilters @Inject()(
                                                  a: StatsFilter[Request],
                                                  //                                     b: AccessLoggingFilter[Request],
                                                  c: HttpResponseFilter[Request],
                                                  d: ExceptionMappingFilter[Request],
                                                  e: HttpNackFilter[Request]
                                                ) extends MergedFilter(a, c, d, e)