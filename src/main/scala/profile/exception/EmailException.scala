package profile.exception

import vn.vhm.common.exception.RException
import vn.vhm.common.util.JsonHelper

/**
 * <AUTHOR>
 */
case class AlreadyExistEmailException(email: String, data: Map[String, Any] = Map.empty[String, Any]) extends RException {
  override def error: String = "already_exist_email"

  override def toString(): String = JsonHelper.toJson(Map("email" -> email), false)

}

case class AlreadyExistByNormalizedEmailException(email: String, data: Map[String, Any] = Map.empty[String, Any]) extends RException {
  override def error: String = "already_exist_by_normalized_email"

  override def toString(): String = JsonHelper.toJson(Map("email" -> email), false)

}

case class UnSupportEmailException(email: String, category: String) extends RException {
  override def error: String = "unsupport_email"

  override def toString(): String = JsonHelper.toJson(Map("email" -> email), false)

}

case class AlreadyExistUserForInternalException(username: String, data: Map[String, Any] = Map.empty[String, Any]) extends RException {
  override def error: String = "already_exist_user"

  override def toString(): String = JsonHelper.toJson(Map("username" -> username) ++data, false)

}

case class NotExistEmailException(message: String = "", cause: Throwable = null) extends Exception(message, cause)

case class EmptyEmailException() extends Exception("email is empty")
