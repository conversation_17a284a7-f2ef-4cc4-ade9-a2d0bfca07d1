package profile.exception

import vn.vhm.common.exception.RException

/**
 * <AUTHOR>
 */
case class ShieldVerifyFailedException(code: Int, message: String) extends RException(message) {
  override def error: String = "shield_verify_failed"
}

case class ShieldRequiredException(
                                  code: Int,
                                  message: String,
                                  shieldToken: String,
                                  holdTimeInSecond: Long
                                  ) extends RException {
  override def error: String = "shield_required"
}