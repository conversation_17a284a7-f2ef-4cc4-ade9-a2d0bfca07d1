package profile.exception

import vn.vhm.common.exception.RException
import vn.vhm.common.util.JsonHelper

/**
 * <AUTHOR>
 */
case class AlreadyExistPhoneException(phone: String, data: Map[String, Any] = Map.empty[String, Any]) extends RException {
  override def error: String = "already_exist_phone"

  override def toString(): String = JsonHelper.toJson(Map("phone" -> phone), false)

}

case class NotExistPhoneException(message: String = "", cause: Throwable = null) extends Exception(message, cause)

case class InvalidPhoneException(message: String = "invalid_phone", cause: Throwable = null) extends Exception(message, cause)

case class InvalidEmailException(message: String = "invalid_email", cause: Throwable = null) extends Exception(message, cause)

case class NeedPhoneException(message: String = "", cause: Throwable = null) extends Exception(message, cause)
