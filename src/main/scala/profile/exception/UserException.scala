package profile.exception

import vn.vhm.common.exception.RException
import vn.vhm.common.util.JsonHelper

/**
 * <AUTHOR> 2/2/24 05:29
 */
case class NotExistUserByPhone(phone: String) extends RException("Not exist user with phone") {
  override def error: String = "not_exist_user_by_phone"

  override def toString(): String = JsonHelper.toJson(Map("phone" -> phone), false)

}

case class NotExistUserByEmail(email: String) extends RException("Not exist user with email") {
  override def error: String = "not_exist_user_by_email"

  override def toString(): String = JsonHelper.toJson(Map("email" -> email), false)

}

case class AlreadyExistUserByPhone(phone: String) extends RException("Already exist user with phone") {
  override def error: String = "already_exist_user_by_phone"

  override def toString(): String = JsonHelper.toJson(Map("phone" -> phone), false)
}

case class NotExistUserException(user: String) extends RException {
  def error: String = "not_exist_user"

  override def toString: String = Option(user).filter(_.nonEmpty).fold(error)(x => s"$error($x)")

}