package profile.exception

/**
 * <AUTHOR>
 */
case class InvalidTokenPhoneException(message: String = "", cause: Throwable = null) extends Exception(message, cause)

case class InvalidTokenException(message: String = "", cause: Throwable = null) extends Exception(message, cause)

case class InvalidVerifyPhoneCodeException(message: String = "", isExpiredCode: Boolean = false, data: Map[String, Any] = Map.empty[String, Any], cause: Throwable = null, expiredIn: Int = 0) extends Exception(message, cause)

case class InvalidVerifyEmailCodeException(message: String = "", isExpiredCode: Boolean = false, data: Map[String, Any] = Map.empty[String, Any], cause: Throwable = null, expiredIn: Int = 0) extends Exception(message, cause)

case class InvalidTokenCaptchaException(message: String = "", cause: Throwable = null) extends Exception(message, cause)

case class InvalidVerifyPnlRequestException(message: String = "", isNotFound: Boolean = false, data: Map[String, Any] = Map.empty[String, Any], cause: Throwable = null) extends Exception(message, cause)

