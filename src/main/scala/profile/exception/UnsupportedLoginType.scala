package profile.exception

import profile.repository.OAuthRepository
import vn.vhm.common.exception.RException
import vn.vhm.common.util.JsonHelper

/**
 * <AUTHOR> 8/23/20 4:26 PM
 */
case class UnsupportedLoginType(oauthType: String) extends RException(oauthType) {
  override def error: String = "unsupported_login_type"
}

case class NotExistUserOfOAuth(oauthType: String, oauthId: String, info: Option[OAuthRepository]) extends RException {
  override def error: String = "not_exist_user_of_oauth"

  override def toString(): String = JsonHelper.toJson(Map("oauth_type" -> oauthType, "oauth_id" -> oauthId), false)

}

case class AlreadyExistUserOfOAuth(oauthType: String, oauthId: String) extends RException {
  override def error: String = "already_exist_user_of_oauth"

  override def toString(): String = JsonHelper.toJson(Map("oauth_type" -> oauthType, "oauth_id" -> oauthId), false)

}
