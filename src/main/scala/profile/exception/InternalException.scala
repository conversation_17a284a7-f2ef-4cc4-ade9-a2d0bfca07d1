package profile.exception

import profile.repository.OAuthRepository
import vn.vhm.common.exception.RException

/**
 * <AUTHOR>
 */

case class InvalidParamException(error: String, errorMsg: String = "") extends RException(errorMsg)

case class InternalException(error: String, errorMsg: String = "") extends Exception(s"$error:$errorMsg")

case class ExceedQuotaException(message: String = "", data: Map[String, Any] = Map.empty[String, Any], cause: Throwable = null) extends Exception(message, cause)

case class LockedException(message: String = "", data: Map[String, Any] = Map.empty[String, Any], cause: Throwable = null) extends Exception(message, cause)

case class ExceedLoginQuotaException(message: String = "", cause: Throwable = null) extends Exception(message, cause)

case class UnAuthorizeException(message: String = "", cause: Throwable = null) extends Exception(message, cause)

case class UnAuthenException(message: String = "", cause: Throwable = null) extends Exception(message, cause)

case class NeedRegisterException(info: Option[OAuthRepository], message: String = "", cause: Throwable = null) extends Exception(message, cause)

case class InvalidCredentialException(message: String = "", data: Map[String, Any] = Map.empty[String, Any]) extends Exception(message)

case class UserBlockedException(message: String = "", data: Map[String, Any] = Map.empty[String, Any]) extends Exception(message)

case class SessionExpiredException() extends Exception("session is expired or not exist")

case class AccessTokenInvalid() extends RException("access token is expired or invalid") {
  override val error: String = "access_token_invalid"
}

case class RefreshTokenInvalid() extends RException("refresh token is expired or not exist") {
  override val error: String = "refresh_token_invalid"
}

case class InvalidOrMissingIdentifyType(msg: String) extends RException(msg) {
  override def error: String = "invalid_or_missing_identify_type"
}
