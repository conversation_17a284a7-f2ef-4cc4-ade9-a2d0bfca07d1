package profile.module

import _root_.core.message_delivery.service.{DeliveryCodeService, DeliveryCodeServiceImpl}
import akka.actor.{ActorRef, ActorSystem, Props}
import caas.domain.SessionRepository
import caas.repository.MySqlSessionDAO
import caas.service.{CaasService, CaasServiceImpl}
import com.google.inject.name.Named
import com.google.inject.{Inject, Provides, Singleton}
import com.twitter.inject.{Injector, TwitterModule}
import com.zaxxer.hikari.HikariDataSource
import jcaas.core.CAAS
import jcaas.model.dal._
import org.apache.commons.codec.CharEncoding
import org.apache.commons.io.FileUtils
import org.nutz.ssdb4j.SSDBs
import profile.consumer.{CustomerIdentityVerificationEventConsumer, CustomerNewSignedInEventConsumer, PnlCustomerHistoricalConsumer}
import profile.domain.customer.{CustomerDAO, CustomerIdentityVerificationDAO, PnlCustomerDAO}
import profile.repository.{FileRepository, GeneralCacheCommon, S3FileRepository, S3SpecificFileRepository}
import profile.service._
import profile.service.files._
import profile.service.scheduler.{GeneralScheduler, UserRegistrationScheduler}
import vn.vhm.common.client.kafka_010.StringKafkaProducer
import vn.vhm.common.client.ssdb.SSDBClient
import vn.vhm.common.repository.cache.RedisCacheCommon
import vn.vhm.common.repository.lock.{AsyncLockManager, RedisLockManager}
import vn.vhm.common.util.{Utils, ZConfig}
import vn.vhm.jdbc.postgres.PostgreSqlEngine
import vn.vhm.notification.service._

import java.io.File
import javax.sql.DataSource
import scala.concurrent.duration.DurationInt

/**
 * <AUTHOR>
 */
object DependencyModule extends TwitterModule {

  //region Init

  override def singletonStartup(injector: Injector): Unit = {

    initDB()

    super.singletonStartup(injector)

  }

  def initDB(customInitSql: Seq[String] = Nil): Unit = {
    import scala.collection.JavaConverters._
    // Init SQL
    Utils.using(getPostgreDataSource(Some("Init PostgreSQL Structure"), maxPoolSize = Some(2), minIdle = Some(1))) {
      //    Utils.using(getMySqlDataSource("", Some("Init MySQL Structure"))) {
      ds =>
        Utils.using(ds.getConnection) {
          conn =>
            //            ZConfig.getStringList("mysql.init_sql").flatMap(file => {
            ZConfig.getStringList("postgresql.init_sql").flatMap(file => {
              val f = new File(file)
              if (f.isDirectory) FileUtils.listFiles(f, Array("sql"), true).asScala.toArray.toSeq else Seq(f)
            }).foreach(file => {
              val sql = FileUtils.readLines(file, CharEncoding.UTF_8).asScala.map(_.trim).filter(v => v.nonEmpty && !v.startsWith("--")).mkString("\n")
              if (sql.nonEmpty) Utils.using(conn.prepareStatement(sql))(_.execute())
            })
            customInitSql.map(sql => Utils.using(conn.prepareStatement(sql))(_.execute()))
        }
    }
  }

  //  def getMySqlDataSource(dbName: String, poolName: Option[String] = None): HikariDataSource = {
  //    val conf = ZConfig.getConf("mysql")
  //    import ZConfig._
  //
  //    val ds = MysqlEngine.getDataSource(
  //      driver = conf.getString("driver", "com.mysql.jdbc.Driver"),
  //      host = conf.getString("host"),
  //      port = conf.getInt("port"),
  //      user = conf.getString("user"),
  //      password = conf.getString("pass"),
  //      dbName = dbName
  //    ).asInstanceOf[HikariDataSource]
  //    ds.addDataSourceProperty("allowMultiQueries", "true")
  //    ds.addDataSourceProperty("allowPublicKeyRetrieval", "true")
  //    poolName.foreach(ds.setPoolName)
  //    ds
  //  }

  def getPostgreDataSource(
                            poolName: Option[String] = None,
                            maxPoolSize: Option[Int] = None,
                            minIdle: Option[Int] = None
                          ): HikariDataSource = {

    val conf = ZConfig.getConf("postgresql")
    import ZConfig._

    val ds = PostgreSqlEngine.getDataSource(
        driver = conf.getString("driver", "org.postgresql.Driver"),
        host = conf.getString("host"),
        port = conf.getInt("port"),
        user = conf.getString("user"),
        password = conf.getString("pass"),
        dbName = conf.getString("db"),
        schemaName = conf.getString("schema"),
        poolName = poolName,
        allowMultiQuery = true,
        properties = Map.empty[String, String]
      )
      .asInstanceOf[HikariDataSource]

    maxPoolSize.foreach(v => ds.setMaximumPoolSize(v))
    minIdle.foreach(v => ds.setMinimumIdle(v))

    //    ds.addDataSourceProperty("allowMultiQueries", "true")
    //    ds.addDataSourceProperty("allowPublicKeyRetrieval", "true")
    //    poolName.foreach(ds.setPoolName)
    ds
  }

  //endregion

  //region Start CAAS Module

  @Singleton
  @Provides
  def providesDataSource(): DataSource = {
    //    getMySqlDataSource(ZConfig.getString("mysql.db"))

    val optMaxPoolSize = Some(ZConfig.getInt("postgresql.max_pool_size", -1)).filter(_ > -1)
    val optMinIdle = Some(ZConfig.getInt("postgresql.min_idle", -1)).filter(_ > -1)

    getPostgreDataSource(None, maxPoolSize = optMaxPoolSize, minIdle = optMinIdle)
  }

  @Singleton
  @Provides
  def providesCAAS(dataSource: DataSource, sessionDAO: org.apache.shiro.session.mgt.eis.SessionDAO): CAAS = {
    new CAAS(dataSource, sessionDAO)
  }

  @Singleton
  @Provides
  def providesSessionDAO(dataSource: DataSource): SessionRepository = SessionRepository(dataSource)

  @Singleton
  @Provides
  def providesShiroSessionDAO(sessionDAO: SessionRepository): org.apache.shiro.session.mgt.eis.SessionDAO = {
    MySqlSessionDAO(sessionDAO)
  }

  @Singleton
  @Provides
  def providesJUserDAO(dataSource: DataSource): JUserDAO = new JUserDAOImpl(dataSource)

  @Singleton
  @Provides
  def providesCustomerDAO(dataSource: DataSource): CustomerDAO = {
    CustomerDAO(dataSource)
  }

  @Singleton
  @Provides
  def providesPnlCustomerDAO(dataSource: DataSource): PnlCustomerDAO = {
    PnlCustomerDAO(dataSource)
  }

  @Singleton
  @Provides
  def providesCustomerIdentityVerificationDAO(dataSource: DataSource): CustomerIdentityVerificationDAO = {
    CustomerIdentityVerificationDAO(dataSource)
  }

  @Singleton
  @Provides
  def providesCaasService(cAAS: CAAS, jUserDAO: JUserDAO, userDAO: CustomerDAO, sessionRepository: SessionRepository): CaasService = {
    CaasServiceImpl(cAAS, jUserDAO, userDAO, sessionRepository)
  }

  //endregion END CAAS Module

  @Singleton
  @Provides
  def providesInternalService(): InternalService = {
    InternalServiceImpl(ZConfig.getConf("vclub_core_service"))
  }

  @Singleton
  @Provides
  def providesUserProfileService(
                                  @Inject customerDAO: CustomerDAO,
                                  pnlCustomerDAO: PnlCustomerDAO,
                                  tierService: InternalService,
                                  kafkaProducer: StringKafkaProducer,

                                  @Named("create_customer_lock") createCustomerLock: AsyncLockManager,
                                  @Named("update_customer_lock") updateCustomerLock: AsyncLockManager,
                                  @Named("create_update_phone_lock") createUpdatePhoneLock: AsyncLockManager,
                                  @Named("create_update_email_lock") createUpdateEmailLock: AsyncLockManager,
                                  @Named("internal_batch_process_lock") internalBatchProcessLock: AsyncLockManager,

                                  imgProxyService: ImgProxyService,
                                  aiIntegrationService: AiIntegrationService,
                                  vbdEyePassIntegrationService: VbdEyePassIntegrationService,
                                  customerFileService: CustomerFileService,
                                  customerIdentityVerificationDAO: CustomerIdentityVerificationDAO,
                                  contextHolder: ContextHolder
                                ): ProfileService = {
    new ProfileServiceImpl(
      customerDAO, pnlCustomerDAO,
      tierService,
      kafkaProducer,
      customerHistoricalTopic = ZConfig.getString("kafka.customer_historical_topic"),
      customerTierChangedTopic = ZConfig.getString("kafka.customer_tier_changed_topic"),
      autoApproveIdentityVerificationTopic = ZConfig.getString("kafka.customer_auto_approve_identity_verification_topic"),
      createCustomerLock, updateCustomerLock,
      createUpdatePhoneLock, createUpdateEmailLock,
      internalBatchProcessLock,

      imgProxyService, aiIntegrationService, vbdEyePassIntegrationService,
      customerFileService, customerIdentityVerificationDAO,
      contextHolder
    )
  }

  @Singleton
  @Provides
  @Named("common")
  def providesCommonSSDB: SSDBClient = {
    SSDBClient(
      SSDBs.pool(
        ZConfig.getString("ssdb.common.host"),
        ZConfig.getInt("ssdb.common.port"),
        ZConfig.getInt("ssdb.common.timeoutInMs"), null)
    )
  }

  @Singleton
  @Provides
  @Named("data")
  def providesCustomerRankingSSDB: SSDBClient = {
    SSDBClient(
      SSDBs.pool(
        ZConfig.getString("ssdb.data.host"),
        ZConfig.getInt("ssdb.data.port"),
        ZConfig.getInt("ssdb.data.timeoutInMs"), null)
    )
  }

  @Singleton
  @Provides
  def providesSendSmsPhoneService(
                                   deliveryService: DeliveryService,
                                   @Named("notification") notificationKafkaProducer: StringKafkaProducer,
                                   @Named("notification") notificationKafkaTopic: String
                                 ): DeliveryCodeService = {
    DeliveryCodeServiceImpl(
      deliveryService,
      notificationKafkaProducer, notificationKafkaTopic
    )
  }

  @Singleton
  @Provides
  def providesVerifyDataService(
                                 @Named("cache_common") keyValueRepo: RedisCacheCommon,
                                 sendSmsPhoneService: DeliveryCodeService,
                               ): VerifyDataService = {
    val enableSms = ZConfig.getBoolean("sms.enable", default = true)
    val enableEmail = ZConfig.getBoolean("email.enable", default = true)
    if (enableSms && enableEmail) {
      new VerifyCodeServiceImpl(keyValueRepo, sendSmsPhoneService)
    } else {
      new ConstantVerifyCodeServiceImpl(keyValueRepo, sendSmsPhoneService, enableEmail = enableEmail, enableSms)
    }

  }

  @Singleton
  @Provides
  def providesDeliveryService: DeliveryService = {
    new DeliveryServiceImpl(vn.vhm.notification.ClientBuilder.newClient(
      host = ZConfig.getString("delivery_service.thrift.host"),
      port = ZConfig.getInt("delivery_service.thrift.port"),
      label = "userprofile_2_delivery",
      30
    ))
  }

  @Singleton
  @Provides
  @Named("notification")
  def providesNotificationKafka(): StringKafkaProducer = {
    StringKafkaProducer(ZConfig.getConf("kafka_delivery.producer"))
  }

  @Singleton
  @Provides
  @Named("notification")
  def providesNotificationKafkaTopic(): String = {
    ZConfig.getString("kafka_delivery.topic")
  }

  @Singleton
  @Provides
  def providesStringKafkaProducer(): StringKafkaProducer = {
    StringKafkaProducer(ZConfig.getConf("kafka.producer"))
  }

  @Singleton
  @Provides
  def providesAuthenJwtService(caasService: CaasService,
                               userProfileService: ProfileService,
                               verifyDataService: VerifyDataService,
                               quotaService: HttpQuotaService,
                               @Named("create_update_phone_lock") createUpdatePhoneLock: AsyncLockManager,
                               @Named("create_update_email_lock") createUpdateEmailLock: AsyncLockManager,
                               @Named("update_customer_lock") updateCustomerLock: AsyncLockManager,

                               kafkaProducer: StringKafkaProducer,
                              ): AuthenJwtService = {
    AuthenJwtServiceImpl(
      verifyDataService,
      caasService,
      userProfileService,
      quotaService,
      createUpdatePhoneLock, createUpdateEmailLock, updateCustomerLock,
      kafkaProducer,
      customerNewSignedInTopic = ZConfig.getString("kafka.customer_new_signedin_topic"),
    )
  }


  @Singleton
  @Provides
  def providesAuthorService(caasService: CaasService,
                            profileCoreService: ProfileService,
                            verifyDataService: VerifyDataService,
                            fileService: GeneralFileService
                           ): AuthorizeService = {
    AuthorizeServiceImpl(caasService, profileCoreService, verifyDataService, fileService)
  }

  @Singleton
  @Provides
  def providesHttpQuotaService(
                                @Inject profileService: ProfileService,
                                verifyDataService: VerifyDataService,
                              ): HttpQuotaService = {
    HttpQuotaWithLockServiceImpl(profileService, verifyDataService)
  }

  //  @Singleton
  //  @Provides
  //  def providesCaptchaService(
  //                              externalCaptchaService: ExternalCaptchaService,
  //                              @Named("common") commonSsdbClient: SSDBClient
  //                            ): CaptchaService = {
  //    CaptchaServiceImpl(
  //      externalCaptchaService, commonSsdbClient
  //    )
  //  }
  //
  //  @Singleton
  //  @Provides
  //  def providesExternalCaptchaService(): ExternalCaptchaService = {
  //    ReCaptchaServiceImpl(
  //      ZConfig.getConf("recaptcha_service")
  //    )
  //  }

  @Singleton
  @Provides
  def providesPnLCustomerHistoricalConsumer(@Inject profileService: ProfileService,
                                            pnlCustomerDAO: PnlCustomerDAO,
                                            kafkaProducer: StringKafkaProducer,
                                           ): Seq[PnlCustomerHistoricalConsumer] = {

    if (ZConfig.getBoolean("kafka.pnl_customer_historical_consumer_enable", true)) {
      for (index <- 0 until ZConfig.getInt("kafka.pnl_customer_historical_consumer_num", 2)) yield {
        PnlCustomerHistoricalConsumer(
          config = ZConfig.getConf("kafka.pnl_customer_historical_consumer"),
          profileService, pnlCustomerDAO,
          kafkaProducer, ZConfig.getString("kafka.pnl_customer_change_trigger_log_topic"),
          index
        )
      }
    } else Nil
  }

  @Singleton
  @Provides
  def providesMetadataSyncService(@Named("common") commonSsdbClient: SSDBClient): MetadataSyncService = {
    MetadataSyncServiceImpl(commonSsdbClient, "vclub_customer_sync_metadata")
  }

  @Provides
  @Singleton
  def providesActorSystem(): ActorSystem = {
    ActorSystem("vclub-customer-service")
  }

  @Provides
  @Singleton
  def providesDataSyncService(@Inject
                              profileService: ProfileService,
                              @Named("data") dataSsdbClient: SSDBClient,
                              customerDAO: CustomerDAO
                             ): DataSyncService = {
    DataSyncService(profileService, dataSsdbClient, customerDAO)
  }

  @Provides
  @Singleton
  @Named("general")
  def providesGeneralScheduler(@Inject
                               actorSystem: ActorSystem,
                               metadataSyncService: MetadataSyncService,
                               dataSyncService: DataSyncService,
                               @Named("internal_process_lock") internalProcessLock: AsyncLockManager,
                               customerDAO: CustomerDAO,
                               identityVerificationDAO: CustomerIdentityVerificationDAO
                              ): ActorRef = {
    actorSystem.actorOf(Props(
      classOf[GeneralScheduler],
      metadataSyncService,
      dataSyncService,
      internalProcessLock,
      customerDAO,
      identityVerificationDAO,
      null
    ), "general")
  }


  @Singleton
  @Provides
  def providesRedisClient: com.twitter.finagle.redis.Client = {
    val host = ZConfig.getString("redis.host")
    val port = ZConfig.getString("redis.port")
    new com.twitter.finagle.redis.Client(com.twitter.finagle.Redis.client.newClient(host + ":" + port, "vclub-customer-service"))
  }

  @Singleton
  @Provides
  @Named("create_customer_lock")
  def providesCustomerCreateLockManager(@Inject redisClient: com.twitter.finagle.redis.Client): AsyncLockManager = {
    new RedisLockManager("create-customer-lock", redisClient, ZConfig.getInt("common.redis_lock_ttl_in_minute", 1).minute.toMillis)
  }

  @Singleton
  @Provides
  @Named("update_customer_lock")
  def providesCustomerUpdateLockManager(@Inject redisClient: com.twitter.finagle.redis.Client): AsyncLockManager = {
    new RedisLockManager("update-customer-lock", redisClient, ZConfig.getInt("common.redis_lock_ttl_in_minute", 1).minute.toMillis)
  }

  @Singleton
  @Provides
  @Named("create_update_phone_lock")
  def providesPhoneCreateOrUpdateLockManager(@Inject redisClient: com.twitter.finagle.redis.Client): AsyncLockManager = {
    new RedisLockManager("create-update-phone-lock", redisClient, ZConfig.getInt("common.redis_lock_ttl_in_minute", 1).minute.toMillis)
  }

  @Singleton
  @Provides
  @Named("create_update_email_lock")
  def providesEmailCreateOrUpdateLockManager(@Inject redisClient: com.twitter.finagle.redis.Client): AsyncLockManager = {
    new RedisLockManager("create-update-email-lock", redisClient, ZConfig.getInt("common.redis_lock_ttl_in_minute", 1).minute.toMillis)
  }

  @Singleton
  @Provides
  @Named("internal_process_lock")
  def providesInternalProcessLockLockManager(@Inject redisClient: com.twitter.finagle.redis.Client): AsyncLockManager = {
    new RedisLockManager("internal-process-lock", redisClient, ZConfig.getInt("common.redis_lock_internal_process_ttl_in_minute", 5).minute.toMillis)
  }

  @Singleton
  @Provides
  @Named("internal_batch_process_lock")
  def providesInternalBatchProcessLockLockManager(@Inject redisClient: com.twitter.finagle.redis.Client): AsyncLockManager = {
    new RedisLockManager("internal_batch_process_lock", redisClient, ZConfig.getInt("common.redis_lock_internal_batch_process_ttl_in_minute", 60).minute.toMillis)
  }

  @Singleton
  @Provides
  @Named("cache_common")
  def providesRedisCacheCommon(@Inject redisClient: com.twitter.finagle.redis.Client): RedisCacheCommon = {
    GeneralCacheCommon(redisClient, redisPrefix = "")
  }

  @Singleton
  @Provides
  def providesFileService(): GeneralFileService = {
    GeneralFileServiceImpl(
      apiHost = ZConfig.getString("files_client.url"),
      uploadIntegrationKey = ZConfig.getString("files_client.integrationkey"),
    )
  }

  @Singleton
  @Provides
  @Named("general")
  def providerFileRepository(): FileRepository = {
    new S3FileRepository(ZConfig.getConf("s3"))
  }

  @Singleton
  @Provides
  @Named("customer")
  def providerCustomerFileRepository(): S3SpecificFileRepository = {
    val s3Conf = ZConfig.getConf("s3")
    S3SpecificFileRepository(
      region = s3Conf.getString("region"),
      bucket = s3Conf.getString("customer_bucket"),
      basePath = s3Conf.getString("customer_base_path")
    )
  }

  @Singleton
  @Provides
  def providesImgProxyService(@Inject @Named("general") fileRepo: FileRepository): ImgProxyService = {
    ImgProxyService(ZConfig.getConf("imgproxy_service"), fileRepo)
  }

  @Singleton
  @Provides
  def providesAiIntegrationService(): AiIntegrationService = {
    AiIntegrationServiceImpl(ZConfig.getConf("ai_integration"))
  }

  @Provides
  @Singleton
  @Named("user_registration")
  def providesUserRegistrationScheduler(@Inject
                                        actorSystem: ActorSystem,
                                        @Named("data") ssdbClient: SSDBClient,
                                        authService: AuthenJwtService
                                       ): ActorRef = {
    actorSystem.actorOf(Props(
      classOf[UserRegistrationScheduler],
      ssdbClient,
      authService
    ))
  }

  @Singleton
  @Provides
  def providesCustomerFileService(@Inject @Named("customer") fileRepo: S3SpecificFileRepository): CustomerFileService = {
    CustomerFileServiceImpl(fileRepo)
  }

  @Singleton
  @Provides
  def providesCustomerNewSignedInEventConsumer(authenJwtService: AuthenJwtService, profileService: ProfileService): CustomerNewSignedInEventConsumer = {
    CustomerNewSignedInEventConsumer(
      config = ZConfig.getConf("kafka.customer_new_signedin_event_consumer"),
      authenJwtService,
      profileService
    )
  }

  @Singleton
  @Provides
  def providesVbdEyePassIntegrationService(@Inject @Named("cache_common") redisCacheCommon: RedisCacheCommon): VbdEyePassIntegrationService = {
    VbdEyePassIntegrationServiceImpl(ZConfig.getConf("vin_bigdata_integration"), redisCacheCommon)
  }

  @Singleton
  @Provides
  def providesCustomerIdentityVerificationEventConsumer(profileService: ProfileService): CustomerIdentityVerificationEventConsumer = {
    CustomerIdentityVerificationEventConsumer(
      config = ZConfig.getConf("kafka.customer_identity_verification_event_consumer"),
      profileService
    )
  }

  @Provides
  @Singleton
  def providesContextHolder(): ContextHolder = {
    ContextHolderImpl()
  }

  @Singleton
  @Provides
  def providesShieldService(): ShieldService = {
    ShieldServiceImpl(ZConfig.getConf("vclub_shield_service"))
  }

}
