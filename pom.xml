<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>vinclub</groupId>
    <artifactId>customer-service</artifactId>
    <version>1.0-SNAPSHOT</version>
    <properties>
        <maven.compiler.source>1.8</maven.compiler.source>
        <maven.compiler.target>1.8</maven.compiler.target>
        <encoding>UTF-8</encoding>
        <scala.version>2.12.11</scala.version>
        <finatra.version>20.6.0</finatra.version>
        <kafka-client.version>0.10.2.2</kafka-client.version>

        <mainClass>profile.MainApp</mainClass>
    </properties>
    <dependencies>

        <dependency>
            <groupId>software.amazon.awssdk</groupId>
            <artifactId>s3</artifactId>
            <version>2.17.171</version>
            <exclusions>
                <exclusion>
                    <groupId>com.fasterxml.jackson.core</groupId>
                    <artifactId>jackson-databind</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.fasterxml.jackson.dataformat</groupId>
                    <artifactId>jackson-dataformat-cbor</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>software.amazon.awssdk</groupId>
            <artifactId>sts</artifactId>
            <version>2.17.157</version>
            <exclusions>
                <exclusion>
                    <groupId>com.fasterxml.jackson.core</groupId>
                    <artifactId>jackson-databind</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.fasterxml.jackson.dataformat</groupId>
                    <artifactId>jackson-dataformat-cbor</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>software.amazon.awssdk</groupId>
            <artifactId>sso</artifactId>
            <version>2.17.157</version>
            <scope>test</scope>
            <exclusions>
                <exclusion>
                    <groupId>com.fasterxml.jackson.core</groupId>
                    <artifactId>jackson-databind</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.fasterxml.jackson.dataformat</groupId>
                    <artifactId>jackson-dataformat-cbor</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.amazonaws</groupId>
            <artifactId>aws-java-sdk-sts</artifactId>
            <version>1.12.148</version>
            <exclusions>
                <exclusion>
                    <groupId>com.fasterxml.jackson.core</groupId>
                    <artifactId>jackson-databind</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.fasterxml.jackson.dataformat</groupId>
                    <artifactId>jackson-dataformat-cbor</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>software.amazon.awssdk</groupId>
            <artifactId>apache-client</artifactId>
            <version>2.16.63</version>
        </dependency>

        <dependency>
            <groupId>org.apache.httpcomponents</groupId>
            <artifactId>httpmime</artifactId>
            <version>4.5.13</version>
        </dependency>

        <dependency>
            <groupId>vn.vinhomes</groupId>
            <artifactId>vhm-common_2.12</artifactId>
            <version>1.0.50_20.6.0</version>
        </dependency>

        <dependency>
            <groupId>org.mindrot</groupId>
            <artifactId>jbcrypt</artifactId>
            <version>0.4</version>
        </dependency>

        <!-- JWT libs -->
        <dependency>
            <groupId>io.jsonwebtoken</groupId>
            <artifactId>jjwt-api</artifactId>
            <version>0.11.1</version>
        </dependency>
        <dependency>
            <groupId>io.jsonwebtoken</groupId>
            <artifactId>jjwt-impl</artifactId>
            <version>0.11.1</version>
        </dependency>
        <dependency>
            <groupId>io.jsonwebtoken</groupId>
            <artifactId>jjwt-jackson</artifactId>
            <version>0.11.1</version>
            <exclusions>
                <exclusion>
                    <groupId>com.fasterxml.jackson.core</groupId>
                    <artifactId>jackson-databind</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.bouncycastle</groupId>
            <artifactId>bcpkix-jdk15on</artifactId>
            <version>1.64</version>
        </dependency>

        <!-- END -->

        <dependency>
            <groupId>vn.vinhomes</groupId>
            <artifactId>vhm-notification_2.12</artifactId>
            <version>1.0.0_20.6.0</version>
        </dependency>

        <dependency>
            <groupId>vn.vinhomes</groupId>
            <artifactId>vhm-jdbc_2.12</artifactId>
            <version>1.1.12_20.6.0</version>
        </dependency>

        <dependency>
            <groupId>com.googlecode.libphonenumber</groupId>
            <artifactId>libphonenumber</artifactId>
            <version>8.13.29</version>
        </dependency>

        <dependency>
            <groupId>org.jsoup</groupId>
            <artifactId>jsoup</artifactId>
            <!--            <version>1.13.1</version>-->
            <version>1.15.4</version>
        </dependency>

        <dependency>
            <groupId>org.nutz</groupId>
            <artifactId>ssdb4j</artifactId>
            <version>10.0</version>
        </dependency>

        <dependency>
            <groupId>commons-codec</groupId>
            <artifactId>commons-codec</artifactId>
            <version>1.10</version>
        </dependency>

        <dependency>
            <groupId>net.cakesolutions</groupId>
            <artifactId>scala-kafka-client_2.12</artifactId>
            <version>${kafka-client.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>org.slf4j</groupId>
                    <artifactId>log4j-over-slf4j</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.typesafe</groupId>
                    <artifactId>config</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.typesafe.akka</groupId>
            <artifactId>akka-actor_2.12</artifactId>
            <version>2.5.14</version>
            <exclusions>
                <exclusion>
                    <groupId>com.typesafe</groupId>
                    <artifactId>config</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.typesafe.akka</groupId>
            <artifactId>akka-stream_2.12</artifactId>
            <version>2.5.14</version>
        </dependency>

        <dependency>
            <groupId>com.typesafe.akka</groupId>
            <artifactId>akka-testkit_2.12</artifactId>
            <version>2.5.14</version>
            <scope>test</scope>
        </dependency>

        <dependency>
            <groupId>org.apache.shiro</groupId>
            <artifactId>shiro-core</artifactId>
            <version>1.13.0</version>
        </dependency>

        <!--        <dependency>-->
        <!--            <groupId>mysql</groupId>-->
        <!--            <artifactId>mysql-connector-java</artifactId>-->
        <!--            <version>8.0.20</version>-->
        <!--        </dependency>-->

        <dependency>
            <groupId>org.postgresql</groupId>
            <artifactId>postgresql</artifactId>
            <version>42.7.3</version>
        </dependency>

        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-csv</artifactId>
            <version>1.10.0</version>
        </dependency>

        <!-- Scala -->
        <dependency>
            <groupId>org.scala-lang</groupId>
            <artifactId>scala-library</artifactId>
            <version>${scala.version}</version>
        </dependency>

        <!-- Finatra -->
        <dependency>
            <groupId>com.twitter</groupId>
            <artifactId>finagle-redis_2.12</artifactId>
            <version>${finatra.version}</version>
        </dependency>
        <dependency>
            <groupId>com.twitter</groupId>
            <artifactId>util-cache-guava_2.12</artifactId>
            <version>${finatra.version}</version>
        </dependency>
        <!--        <dependency>-->
        <!--            <groupId>com.twitter</groupId>-->
        <!--            <artifactId>finatra-thrift_2.12</artifactId>-->
        <!--            <version>${finatra.version}</version>-->
        <!--        </dependency>-->
        <dependency>
            <groupId>com.twitter</groupId>
            <artifactId>finatra-http_2.12</artifactId>
            <version>${finatra.version}</version>
        </dependency>

        <!-- Test Deps -->
        <!--        <dependency>-->
        <!--            <groupId>com.twitter</groupId>-->
        <!--            <artifactId>finatra-thrift_2.12</artifactId>-->
        <!--            <scope>test</scope>-->
        <!--            <type>test-jar</type>-->
        <!--            <version>${finatra.version}</version>-->
        <!--            <classifier>tests</classifier>-->
        <!--        </dependency>-->
        <dependency>
            <groupId>com.twitter</groupId>
            <artifactId>finatra-http_2.12</artifactId>
            <scope>test</scope>
            <type>test-jar</type>
            <version>${finatra.version}</version>
        </dependency>
        <dependency>
            <groupId>com.twitter</groupId>
            <artifactId>inject-core_2.12</artifactId>
            <scope>test</scope>
            <type>test-jar</type>
            <version>${finatra.version}</version>
        </dependency>
        <dependency>
            <groupId>com.twitter</groupId>
            <artifactId>inject-modules_2.12</artifactId>
            <scope>test</scope>
            <type>test-jar</type>
            <version>${finatra.version}</version>
        </dependency>
        <dependency>
            <groupId>com.twitter</groupId>
            <artifactId>inject-app_2.12</artifactId>
            <scope>test</scope>
            <type>test-jar</type>
            <version>${finatra.version}</version>
        </dependency>
        <dependency>
            <groupId>com.twitter</groupId>
            <artifactId>inject-server_2.12</artifactId>
            <scope>test</scope>
            <type>test-jar</type>
            <version>${finatra.version}</version>
        </dependency>
        <dependency>
            <groupId>com.google.inject.extensions</groupId>
            <artifactId>guice-testlib</artifactId>
            <scope>test</scope>
            <version>4.2.3</version>
        </dependency>

        <dependency>
            <groupId>org.scalatest</groupId>
            <artifactId>scalatest_2.12</artifactId>
            <scope>test</scope>
            <version>3.0.8</version>
        </dependency>
        <dependency>
            <groupId>org.specs2</groupId>
            <artifactId>specs2-cats_2.12</artifactId>
            <version>4.3.1</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.specs2</groupId>
            <artifactId>specs2_2.12</artifactId>
            <type>pom</type>
            <scope>provided</scope>
            <version>3.8.9</version>
            <exclusions>
                <exclusion>
                    <groupId>org.scala-lang</groupId>
                    <artifactId>scala-compiler</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.specs2</groupId>
                    <artifactId>specs2-cats_2.12</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.mockito</groupId>
            <artifactId>mockito-core</artifactId>
            <scope>test</scope>
            <version>1.10.19</version>
        </dependency>
    </dependencies>

    <build>
        <finalName>${project.artifactId}</finalName>

        <resources>
            <resource>
                <directory>conf</directory>
            </resource>
        </resources>

        <plugins>

            <plugin>
                <groupId>net.alchim31.maven</groupId>
                <artifactId>scala-maven-plugin</artifactId>
                <version>3.2.0</version>
                <executions>
                    <execution>
                        <id>scala-compile-first</id>
                        <phase>process-resources</phase>
                        <configuration>
                            <recompileMode>incremental</recompileMode>
                            <useZincServer>true</useZincServer>
                            <args>
                                <param>-language:_</param>
                                <param>-g:vars</param>
                                <param>-unchecked</param>
                                <param>-deprecation</param>
                                <param>-encoding</param>
                                <param>utf8</param>
                            </args>
                            <javacArgs>
                                <javacArg>-target</javacArg>
                                <javacArg>8</javacArg>
                                <javacArg>-source</javacArg>
                                <javacArg>8</javacArg>
                            </javacArgs>
                        </configuration>
                        <goals>
                            <goal>add-source</goal>
                            <goal>compile</goal>
                        </goals>
                    </execution>
                    <execution>
                        <id>scala-test-compile</id>
                        <phase>process-test-resources</phase>
                        <configuration>
                            <args>
                                <param>-language:_</param>
                                <param>-g:vars</param>
                                <param>-unchecked</param>
                                <param>-deprecation</param>
                                <param>-encoding</param>
                                <param>utf8</param>
                            </args>
                            <javacArgs>
                                <javacArg>-target</javacArg>
                                <javacArg>8</javacArg>
                                <javacArg>-source</javacArg>
                                <javacArg>8</javacArg>
                            </javacArgs>
                        </configuration>
                        <goals>
                            <goal>testCompile</goal>
                        </goals>
                    </execution>
                </executions>
                <configuration>
                    <jvmArgs>
                        <jvmArg>-Xmx2G</jvmArg>
                    </jvmArgs>
                </configuration>
            </plugin>

            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
                <version>2.18.1</version>
                <configuration>
                    <skipTests>true</skipTests>
                </configuration>
            </plugin>

            <plugin>
                <groupId>org.scalatest</groupId>
                <artifactId>scalatest-maven-plugin</artifactId>
                <version>1.0</version>
                <configuration>
                    <suffixes>Test|Spec</suffixes>
                    <reportsDirectory>${project.build.directory}/surefire-reports</reportsDirectory>
                    <junitxml>.</junitxml>
                    <filereports>WDF TestSuite.txt</filereports>
                    <stdout>T</stdout>
                    <parallel>false</parallel>
                    <forkMode>once</forkMode>
                    <logForkedProcessCommand>false</logForkedProcessCommand>
                </configuration>
                <executions>
                    <execution>
                        <id>test</id>
                        <phase>test</phase>
                        <goals>
                            <goal>test</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>

            <!-- Thrift Scrooge Generator -->
            <plugin>
                <groupId>com.twitter</groupId>
                <artifactId>scrooge-maven-plugin</artifactId>
                <version>${finatra.version}</version>
                <configuration>
                    <language>scala</language>
                    <!-- default is scala, can also be java -->
                    <thriftOpts>
                        <!-- add other Scrooge command line options using thriftOpts -->
                        <thriftOpt>--finagle</thriftOpt>
                    </thriftOpts>
                </configuration>
                <executions>
                    <execution>
                        <id>thrift-sources</id>
                        <phase>generate-sources</phase>
                        <goals>
                            <goal>compile</goal>
                        </goals>
                    </execution>
                    <execution>
                        <id>thrift-test-sources</id>
                        <phase>generate-test-sources</phase>
                        <goals>
                            <goal>testCompile</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>

            <plugin>
                <artifactId>maven-jar-plugin</artifactId>
                <configuration>
                    <archive>
                        <manifest>
                            <addClasspath>true</addClasspath>
                            <classpathPrefix>lib/</classpathPrefix>
                            <mainClass>${mainClass}</mainClass>
                        </manifest>
                    </archive>
                    <outputDirectory>${basedir}/dist</outputDirectory>
                </configuration>
            </plugin>

            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-dependency-plugin</artifactId>
                <version>2.10</version>
                <executions>
                    <execution>
                        <id>copy-dependencies</id>
                        <phase>package</phase>
                        <goals>
                            <goal>copy-dependencies</goal>
                        </goals>
                        <configuration>
                            <outputDirectory>${basedir}/dist/lib</outputDirectory>
                            <overWriteReleases>false</overWriteReleases>
                            <overWriteSnapshots>false</overWriteSnapshots>
                            <overWriteIfNewer>true</overWriteIfNewer>
                        </configuration>
                    </execution>
                </executions>
            </plugin>

            <plugin>
                <groupId>io.fabric8</groupId>
                <artifactId>docker-maven-plugin</artifactId>
                <version>0.43.0</version>
                <configuration>
                    <skip>${skipDockerMaven}</skip>
                    <images>
                        <image>
                            <name>r3v3r/zookeeper:test-2.4.1</name>
                            <alias>zookeeper</alias>
                            <run>
                                <ports>
                                    <port>2181:2181</port>
                                </ports>
                                <wait>
                                    <log>binding to port</log>
                                    <time>60000</time>
                                </wait>
                            </run>
                        </image>
                        <image>
                            <name>bitnami/kafka:3.5.1</name>
                            <alias>kafka_test</alias>
                            <run>
                                <ports>
                                    <port>9092:9092</port>
                                </ports>
                                <links>
                                    <link>zookeeper:zk</link>
                                </links>
                                <env>
                                    <KAFKA_ZOOKEEPER_CONNECT>zk:2181</KAFKA_ZOOKEEPER_CONNECT>
                                    <KAFKA_ADVERTISED_HOST_NAME>127.0.0.1</KAFKA_ADVERTISED_HOST_NAME>
                                </env>
                                <wait>
                                    <log>started \(kafka\.server\.KafkaServer\)</log>
                                    <time>60000</time>
                                </wait>
                                <log>
                                    <enabled>false</enabled>
                                </log>
                            </run>
                        </image>
                        <image>
                            <alias>postgresql16_test</alias>
                            <name>postgres:16.1</name>
                            <run>
                                <env>
                                    <POSTGRES_USER>vclub_user</POSTGRES_USER>
                                    <POSTGRES_PASSWORD>vclub_pass</POSTGRES_PASSWORD>
                                    <POSTGRES_DB>vinclub_db</POSTGRES_DB>
                                    <POSTGRES_SCHEMA>core_db</POSTGRES_SCHEMA>
                                </env>
                                <ports>
                                    <port>2432:5432</port>
                                </ports>
                                <volumes>
                                    <bind>
                                        <volume>src/test/data/pg_docker_init.sh:/docker-entrypoint-initdb.d/db.sh
                                        </volume>
                                    </bind>
                                </volumes>
                                <log>
                                    <enabled>false</enabled>
                                </log>
                                <wait>
                                    <log>database system is ready to accept connections</log>
                                    <time>20000</time>
                                </wait>
                            </run>
                        </image>
                        <image>
                            <name>r3v3r/ssdb:test-1.9.8</name>
                            <alias>ssdb</alias>
                            <run>
                                <ports>
                                    <port>8888:8888</port>
                                </ports>
                            </run>
                        </image>
                        <image>
                            <name>bitnami/redis:6.2.10</name>
                            <alias>redis_test</alias>
                            <run>
                                <ports>
                                    <port>6379:6379</port>
                                </ports>
                                <env>
                                    <REDIS_AOF_ENABLED>no</REDIS_AOF_ENABLED>
                                    <ALLOW_EMPTY_PASSWORD>yes</ALLOW_EMPTY_PASSWORD>
                                </env>
                                <cmd>
                                    <exec>
                                        <arg>/opt/bitnami/scripts/redis/run.sh</arg>
                                        <arg>--maxmemory 10mb</arg>
                                        <arg>--maxmemory-policy</arg>
                                        <arg>allkeys-lru</arg>
                                    </exec>
                                </cmd>
                            </run>
                        </image>
                    </images>
                </configuration>
                <executions>
                    <execution>
                        <id>start</id>
                        <!--<phase>generate-test-resources</phase>-->
                        <phase>process-test-classes</phase>
                        <!--<phase>test</phase>-->
                        <goals>
                            <goal>start</goal>
                        </goals>
                    </execution>
                    <execution>
                        <id>stop</id>
                        <phase>test</phase>
                        <goals>
                            <goal>stop</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>

        </plugins>
    </build>

    <repositories>
        <repository>
            <id>vhm-release</id>
            <name>vhm-release-local</name>
            <url>https://gitlab.vin-group.net/api/v4/projects/779/packages/maven</url>
            <layout>default</layout>
        </repository>

        <repository>
            <id>vclub-release-reader</id>
            <name>vclub-release-local</name>
            <url>https://gitlab.vin-group.net/api/v4/projects/1123/packages/maven</url>
            <layout>default</layout>
        </repository>

    </repositories>


</project>